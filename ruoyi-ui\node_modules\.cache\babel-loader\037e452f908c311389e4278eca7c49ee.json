{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754447197993}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_questionBank", "require", "_category", "_question", "name", "props", "visible", "type", "Boolean", "default", "paperId", "String", "Number", "data", "_defineProperty2", "rightPanelCollapsed", "rightPanel<PERSON><PERSON>th", "activeCollapse", "paperForm", "paperName", "paperDesc", "paperType", "coverImg", "totalScore", "passScore", "startTime", "endTime", "duration", "lateLimit", "allowEarlySubmit", "earlySubmitTime", "showScore", "showType", "requireAllAnswered", "showResult", "showCorrect", "showAnswer", "showAnalysis", "status", "enableTimeLimit", "durationSeconds", "createTime", "fixedQuestions", "selectAll", "isExpanded", "showManualSelectDialog", "categoryOptions", "cascaderProps", "value", "label", "children", "checkStrictly", "emitPath", "manualSelect", "selectedCate<PERSON><PERSON>", "bankSearchKeyword", "questionBanks", "bankPagination", "pageNum", "pageSize", "total", "selectedBankId", "questionType", "difficulty", "questionSearchKeyword", "questions", "questionPagination", "selectedQuestions", "selectedStats", "showFixedRandomDialog", "fixedRandomRules", "fixedRandomForm", "ruleType", "generateType", "selectedItems", "selectedQuestionTypes", "showBatchScoreDialog", "batchScoreForm", "scoreType", "score", "showRuleDialog", "rules", "showAddRuleDialog", "ruleForm", "currentOperation", "editingRule", "parentRule", "currentQuestionTypeCount", "questionBankLoading", "bankName", "undefined", "computed", "selectedBanks", "_this", "filter", "bank", "includes", "bankId", "totalQuestions", "for<PERSON>ach", "rule", "length", "child", "selectedCount", "totalRuleScore", "parentRules", "parentId", "usedBankIds", "usedIds", "Set", "add", "Array", "from", "fixedQuestionStats", "stats", "question", "fixedRandomTotalQuestions", "fixedRandomTotalScore", "fixedRandomParentRules", "usedFixedRandomBankIds", "watch", "val", "loadPaperData", "handler", "newVal", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "updateQuestionTypeCount", "a", "deep", "methods", "handleBack", "$emit", "toggleRightPanel", "handleExamEntry", "$message", "info", "handlePageSetting", "handlePublish", "handleInviteStudents", "handleInviteList", "handleAddRule", "handleEditRules", "handleAddFirstRule", "queryParams", "searchKeyword", "loadQuestionBanks", "_this3", "Promise", "all", "listQuestionBank", "listCategory", "then", "_ref2", "_ref3", "_slicedToArray2", "bankResponse", "categoryResponse", "rows", "statisticsPromises", "map", "getQuestionStatistics", "questionCount", "totalCount", "catch", "banksWithStats", "handleSaveRules", "handleSaveRule", "_this4", "_callee2", "_context2", "warning", "updateExistingRule", "addFixedRandomRule", "addFixedRandomSubRule", "addNewRule", "success", "resetRuleForm", "_toConsumableArray2", "maxQuestions", "reduce", "sum", "scorePerQuestion", "_this5", "addSingleRule", "index", "id", "Date", "now", "updateRuleScore", "push", "_this6", "updateFixedRandomRuleScore", "addSingleFixedRandomRule", "$set", "handleSearch", "handleReset", "handleSelectionChange", "selection", "item", "removeSelectedBank", "_this7", "indexOf", "splice", "$nextTick", "table", "$refs", "questionBankTable", "rowToDeselect", "find", "toggleRowSelection", "handleCurrentChange", "page", "getCategoryName", "categoryId", "category", "findCategoryById", "categories", "flatCategories", "flattenCategories", "cat", "result", "flatten", "cats", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "err", "e", "f", "getQuestionTypeText", "questionTypes", "questionTypeMap", "isArray", "t", "join", "getChildRule<PERSON>abel", "childRule", "getRuleTypeLabel", "get<PERSON>ule<PERSON><PERSON>nt", "addSubRule", "defaultRuleType", "editRule", "_this8", "deleteRule", "_this9", "$confirm", "confirmButtonText", "cancelButtonText", "findIndex", "r", "deleteChildRule", "_this0", "getDialogTitle", "shouldShowRuleTypeSelection", "shouldShowRuleType", "shouldShowQuestionType", "existingQuestionTypes", "apply", "isRowSelectable", "row", "getRowClassName", "_ref4", "_this1", "_callee3", "count", "_t", "_context3", "p", "getQuestionCountByType", "v", "console", "error", "banks", "_callee4", "_iterator2", "_step2", "_loop", "_t3", "_context5", "response", "statistics", "_t2", "_context4", "log", "concat", "code", "singleChoice", "multipleChoice", "judgment", "d", "_regeneratorValues2", "shouldDisableRuleType", "handleSelectAll", "_this10", "selected", "handleDeleteSelected", "_this11", "q", "handleManualSelect", "initManualSelectData", "loadCategoryTree", "loadManualSelectQuestionBanks", "_this12", "buildCategoryTree", "_objectSpread2", "cleanEmptyChildren", "node", "getAllChildCategoryIds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this13", "categoryIds", "allCategories", "loadQuestionBanksByCategories", "_this14", "promises", "responses", "allBanks", "uniqueBanks", "self", "b", "searchQuestionBanks", "selectQuestionBank", "loadQuestions", "_this15", "questionContent", "listQuestion", "searchQuestions", "resetQuestionSearch", "handleRandomSelect", "resetFixedRandomForm", "handleAddFixedRandomRule", "handleAddFixedRandomSubRule", "deleteFixedRandomRule", "_this16", "childIndex", "deleteFixedRandomChildRule", "handleConfirmFixedRandomSelect", "_this17", "_callee5", "_t4", "_context6", "extractQuestionsFromRules", "questionId", "content", "options", "expanded", "_this18", "_callee6", "allQuestions", "_iterator3", "_step3", "_loop2", "_t0", "_context9", "childRules", "_iterator4", "_step4", "_loop3", "_iterator7", "_step7", "shuffled", "_t7", "_t8", "_t9", "_context8", "_iterator5", "_step5", "_iterator6", "_step6", "_t5", "_t6", "_context7", "shuffle<PERSON><PERSON><PERSON>", "Math", "min", "slice", "array", "i", "j", "floor", "random", "_ref5", "handleSort", "handleBatchSetScore", "getBatchScoreAffectedCount", "_this19", "handleConfirmBatchSetScore", "_this20", "affectedCount", "targetQuestions", "handleExport", "handleToggleExpand", "_this21", "handleQuestionSelectionChange", "updateSelectedStats", "confirmManualSelect", "_this22", "handleBankSizeChange", "handleBankCurrentChange", "handleQuestionSizeChange", "handleQuestionCurrentChange", "getDifficultyText", "difficultyMap", "updateQuestionScore", "$forceUpdate", "toggleQuestionExpand", "deleteQuestion", "_this23", "parseOptions", "optionsStr", "JSON", "parse", "isQuestionSelectable", "some", "getQuestionRowClassName", "_ref6", "beforeUpload", "file", "isImage", "isLt2M", "size", "formatDate", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/biz/paper/create.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-editor\">\n    <!-- 左侧主要内容区域 -->\n    <div class=\"exam-editor-left\" :class=\"{ collapsed: rightPanelCollapsed }\">\n      <!-- 顶部操作栏 -->\n      <div class=\"editPaper_main_top\">\n        <div class=\"el-button-group\">\n          <el-button type=\"primary\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i>\n            <span>返回试卷</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button @click=\"handleExamEntry\">\n            <i class=\"el-icon-share\"></i>\n            <span>考试入口</span>\n          </el-button>\n          <el-button @click=\"handlePageSetting\">\n            <i class=\"el-icon-picture-outline\"></i>\n            <span>设置考试页面</span>\n          </el-button>\n          <el-button type=\"warning\" @click=\"handlePublish\">\n            <i class=\"el-icon-upload\"></i>\n            <span>发布</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button type=\"success\" @click=\"handleInviteStudents\">\n            邀请考生\n          </el-button>\n          <el-button type=\"warning\" @click=\"handleInviteList\">\n            已邀请列表\n          </el-button>\n        </div>\n        \n        <div class=\"clear_both\"></div>\n      </div>\n\n      <!-- 试卷信息卡片 -->\n      <div class=\"subject_main-wrapper\">\n        <div class=\"subject_main\">\n          <el-card class=\"is-hover-shadow\" style=\"width: 100%;\">\n            <div class=\"subtitle-title\" style=\"padding: 10px;\">\n              <div>\n                <div style=\"float: left;\">\n                  <strong class=\"slgfont\">{{ paperForm.paperName || '新建试卷' }}</strong>\n                </div>\n                <div style=\"float: right; color: #ec661a; width: 130px; text-align: right;\">\n                  <span style=\"font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;\">{{ totalRuleScore }}</span>分\n                </div>\n                <div style=\"clear: both;\"></div>\n                <div class=\"paper-count\" style=\"font-size: 13px; color: #aaa;\">\n                  <span v-if=\"paperForm.createTime\">创建时间：{{ paperForm.createTime }}</span>\n                  <el-tag type=\"warning\" size=\"medium\" effect=\"light\">共 {{ totalQuestions }} 题</el-tag>\n                  <el-tag type=\"success\" size=\"medium\" effect=\"light\">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>\n                  <el-tag size=\"medium\" effect=\"light\">自动出分</el-tag>\n                </div>\n                <div class=\"rich-text\" style=\"display: none;\"></div>\n              </div>\n              <div style=\"clear: both;\"></div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 题目设置区域 -->\n        <div>\n          <div class=\"mb10 mt10\">\n            <div class=\"el-button-group\"></div>\n          </div>\n          \n          <div v-if=\"paperForm.paperType === 1\">\n            <!-- 随机试卷 -->\n            <div class=\"random-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 有规则时显示规则内容 -->\n                <div v-if=\"rules.length > 0\" style=\"padding: 10px;\">\n                  <div class=\"tac pd5\">\n                    <div>\n                      <el-button type=\"primary\" @click=\"handleEditRules\">\n                        <i class=\"el-icon-edit\"></i>\n                        <span>编辑规则</span>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 规则显示区域 -->\n                  <div v-for=\"rule in parentRules\" :key=\"rule.id\" class=\"rule-display-container\">\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule-display\">\n                      <span class=\"rule-label\">{{ getRuleTypeLabel(rule) }}</span>\n                      <span class=\"rule-content\">{{ getRuleContent(rule) }}</span>\n                    </div>\n\n                    <!-- 子规则显示 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules-display\">\n                      <div v-for=\"childRule in rule.children\" :key=\"childRule.id\" class=\"child-rule-display\">\n                        <div class=\"child-rule-left\">\n                          <span class=\"rule-label\">{{ getRuleTypeLabel(childRule) }}</span>\n                          <span class=\"rule-content\">{{ getRuleContent(childRule) }}</span>\n                        </div>\n                        <div class=\"child-rule-right\">\n                          <span class=\"rule-stats\">\n                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，\n                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，\n                            总分 <strong>{{ childRule.totalScore }}</strong> 分\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 没有规则时显示添加按钮 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 10px;\">\n                  <div>\n                    <div class=\"mb10\">点击添加规则设置本试卷的抽题规则</div>\n                    <el-button type=\"primary\" @click=\"handleAddRule\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>添加规则</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n          \n          <div v-else>\n            <!-- 固定试卷 -->\n            <div class=\"fixed-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 操作栏 -->\n                <div class=\"mb10 mt10\">\n                  <div class=\"el-button-group\">\n                    <label class=\"el-checkbox fl el-checkbox--medium is-bordered\" style=\"padding: 7.5px 20px;\">\n                      <span class=\"el-checkbox__input\">\n                        <span class=\"el-checkbox__inner\"></span>\n                        <input type=\"checkbox\" aria-hidden=\"false\" class=\"el-checkbox__original\" value=\"\" v-model=\"selectAll\" @change=\"handleSelectAll\">\n                      </span>\n                      <span class=\"el-checkbox__label\">全选</span>\n                    </label>\n                    <el-tooltip content=\"删除选中题目\" placement=\"top\">\n                      <el-button type=\"danger\" size=\"medium\" @click=\"handleDeleteSelected\">\n                        <i class=\"el-icon-delete\"></i>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n\n                  <div class=\"el-button-group\">\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>手动选题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleRandomSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>随机抽题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleSort\">\n                      <i class=\"el-icon-sort\"></i>\n                      <span>排序</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleBatchSetScore\">\n                      <i class=\"icon_size iconfont icon-batch-add\"></i>\n                      <span>批量设置分数</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleExport\">\n                      <i class=\"icon_size iconfont icon-daochu\"></i>\n                      <span>导出</span>\n                    </el-button>\n                  </div>\n\n                  <el-button type=\"default\" size=\"medium\" style=\"vertical-align: middle;\" @click=\"handleToggleExpand\">\n                    <span>\n                      <i :class=\"isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\" style=\"margin-right: 5px;\"></i>\n                      <span>{{ isExpanded ? '收起' : '展开' }}</span>\n                    </span>\n                  </el-button>\n                </div>\n\n                <!-- 题目列表区域 -->\n                <div v-if=\"fixedQuestions.length > 0\" class=\"question-list\">\n                  <!-- 这里将显示已添加的题目列表 -->\n                  <div class=\"question-item\" v-for=\"(question, index) in fixedQuestions\" :key=\"question.id\">\n                    <!-- 题目头部 -->\n                    <div class=\"question-header\">\n                      <div class=\"question-header-left\">\n                        <el-checkbox v-model=\"question.selected\"></el-checkbox>\n                        <span class=\"question-number\">{{ index + 1 }}.</span>\n                        <i\n                          :class=\"question.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\"\n                          class=\"question-expand-icon\"\n                          @click=\"toggleQuestionExpand(question)\"\n                        ></i>\n                        <span class=\"question-content\" @click=\"toggleQuestionExpand(question)\">{{ question.content }}</span>\n                      </div>\n                      <div class=\"question-header-right\">\n                        <!-- 分数设置 -->\n                        <el-input-number\n                          v-model=\"question.score\"\n                          :min=\"0.5\"\n                          :step=\"0.5\"\n                          size=\"mini\"\n                          style=\"width: 100px;\"\n                          @change=\"updateQuestionScore(question)\"\n                        ></el-input-number>\n                        <span style=\"margin-left: 5px; margin-right: 10px;\">分</span>\n\n                        <!-- 展开/收起按钮 -->\n                        <el-tooltip :content=\"question.expanded ? '收起' : '展开'\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"toggleQuestionExpand(question)\"\n                            style=\"margin-right: 5px;\"\n                          >\n                            <i :class=\"question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                          </el-button>\n                        </el-tooltip>\n\n                        <!-- 删除按钮 -->\n                        <el-tooltip content=\"删除\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"deleteQuestion(question, index)\"\n                            style=\"color: #f56c6c;\"\n                          >\n                            <i class=\"el-icon-delete\"></i>\n                          </el-button>\n                        </el-tooltip>\n                      </div>\n                    </div>\n\n                    <!-- 题目详情（展开时显示） -->\n                    <div v-if=\"question.expanded\" class=\"question-details\">\n                      <div class=\"question-info\">\n                        <span class=\"info-item\">题型：{{ getQuestionTypeText(question.type) }}</span>\n                        <span class=\"info-item\">难度：{{ getDifficultyText(question.difficulty) }}</span>\n                      </div>\n\n                      <!-- 选项显示 -->\n                      <div v-if=\"question.options\" class=\"question-options\">\n                        <div class=\"options-title\">选项：</div>\n                        <div\n                          v-for=\"(option, optIndex) in parseOptions(question.options)\"\n                          :key=\"optIndex\"\n                          class=\"option-item\"\n                          :class=\"{ 'correct-option': option.isCorrect }\"\n                        >\n                          <span class=\"option-key\">{{ option.key }}.</span>\n                          <span class=\"option-content\">{{ option.content }}</span>\n                          <span v-if=\"option.isCorrect\" class=\"correct-mark\">✓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 40px 10px;\">\n                  <div>\n                    <div class=\"mb10\">暂无题目，点击上方按钮添加题目到试卷中</div>\n                    <el-button type=\"primary\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>开始添加题目</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧设置面板 -->\n    <div class=\"exam-editor-right\" :style=\"{ width: rightPanelWidth + 'px' }\">\n      <i \n        :class=\"rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'\" \n        class=\"collapse-button\" \n        title=\"收起/展开\"\n        @click=\"toggleRightPanel\"\n      ></i>\n      \n      <div v-show=\"!rightPanelCollapsed\">\n        <div class=\"editor-header editor-header--big\">\n          <span>考试设置</span>\n          <i class=\"el-icon-close close-button\" @click=\"handleBack\" title=\"关闭\"></i>\n        </div>\n        \n        <div class=\"main\">\n          <el-form class=\"h100p\">\n            <el-collapse v-model=\"activeCollapse\" class=\"main-collapse h100p\" accordion>\n              <!-- 基础设置 -->\n              <el-collapse-item title=\"基础设置\" name=\"basic\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-setting\" style=\"color: #67c23a;\"></i>基础设置\n                  </div>\n                </template>\n\n                <div class=\"main-module\">\n                  <!-- 封面图 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>封面图</b>\n                      <div class=\"input-area\">\n                        <div style=\"margin-top: 20px;\">\n                          <span class=\"dpib\" style=\"width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;\">\n                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2\n                          </span>\n                          <div class=\"g-component-cover-uploader dpib\">\n                            <div class=\"avatar-uploader\" style=\"width: 120px; height: 80px;\">\n                              <el-upload\n                                class=\"avatar-uploader\"\n                                action=\"#\"\n                                :show-file-list=\"false\"\n                                :before-upload=\"beforeUpload\"\n                                accept=\".png, .jpeg, .jpg\"\n                              >\n                                <div class=\"image_area\">\n                                  <img v-if=\"paperForm.coverImg\" :src=\"paperForm.coverImg\" class=\"avatar\">\n                                  <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                                </div>\n                              </el-upload>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷名称 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷名称</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperName\"\n                          placeholder=\"请输入试卷名称\"\n                          maxlength=\"100\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷描述 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷描述</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperDesc\"\n                          type=\"textarea\"\n                          :rows=\"3\"\n                          placeholder=\"请输入试卷描述\"\n                          maxlength=\"500\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 出题方式 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>出题方式</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"paper-type-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">出题方式</div>\n                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>\n                            <br>\n                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-radio-group v-model=\"paperForm.paperType\" size=\"mini\">\n                          <el-radio-button :label=\"1\">随机试卷</el-radio-button>\n                          <el-radio-button :label=\"0\">固定试卷</el-radio-button>\n                        </el-radio-group>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n                  <!-- 时长 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>时长(按卷限时)</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">时长</div>\n                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>\n                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.duration\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                        <el-input-number v-model=\"paperForm.durationSeconds\" :min=\"0\" :max=\"59\" size=\"mini\" style=\"width: 85px;\"></el-input-number> 秒\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 及格分 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>及格分</b>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.passScore\" :min=\"0\" :max=\"paperForm.totalScore || 100\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>考试时间</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">考试时间</div>\n                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.enableTimeLimit\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间子项容器 -->\n                  <div class=\"block-expand\" v-if=\"paperForm.enableTimeLimit\">\n                    <!-- 考试开始时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试开始时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.startTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择开始时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 考试结束时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试结束时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.endTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择结束时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n\n                  </div>\n\n                  <!-- 提前交卷 -->\n                  <div class=\"setting-block\" v-if=\"paperForm.enableTimeLimit\">\n                    <div class=\"line block-header\">\n                      <b>提前交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch\n                          v-model=\"paperForm.allowEarlySubmit\"\n                          :active-value=\"1\"\n                          :inactive-value=\"0\"\n                        ></el-switch>\n                        <span v-if=\"paperForm.allowEarlySubmit\" style=\"margin-left: 10px;\">\n                          <el-input-number\n                            v-model=\"paperForm.earlySubmitTime\"\n                            :min=\"1\"\n                            :max=\"60\"\n                            size=\"mini\"\n                            style=\"width: 100px;\"\n                          ></el-input-number> 分钟\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示分值 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示分值</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showScore\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示题型 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示题型</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showType\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 迟到限制 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>迟到限制</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"late-limit-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">迟到限制</div>\n                            <div>设置迟到多少分钟后，禁止再进入考试。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.lateLimit\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分钟\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试中设置 -->\n              <el-collapse-item title=\"考试中\" name=\"during\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-edit-outline\" style=\"color: #409eff;\"></i>考试中\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n\n\n                  <!-- 全部答完才能交卷 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>全部答完才能交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.requireAllAnswered\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试后设置 -->\n              <el-collapse-item title=\"考试后\" name=\"after\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-finished\" style=\"color: #e6a23c;\"></i>考试后\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n                  <!-- 显示成绩 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示成绩</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showResult\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示对错 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示对错</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showCorrect\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示答案 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示答案</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnswer\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示解析 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示解析</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnalysis\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </el-form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 随机规则设置对话框 -->\n    <el-dialog\n      title=\"随机规则设置\"\n      :visible.sync=\"showRuleDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"新版规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFirstRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加一级规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ totalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ totalRuleScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"确定一级规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      按题库、按题型、按难度、按知识点抽题四选一\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"添加子规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      在每个上级规则基础上继续添加子规则\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"保存抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      各级规则设置完成后即可保存规则开始抽题\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"rules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in parentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"addSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}\n                          </div>\n                          <div class=\"rule-content\" v-if=\"childRule.type === 3\">\n                            <span>\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"rules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加一级规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showRuleDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleSaveRules\">保存规则</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 手动选题对话框 -->\n    <el-dialog\n      title=\"选择题目\"\n      :visible.sync=\"showManualSelectDialog\"\n      width=\"1200px\"\n      :close-on-click-modal=\"false\"\n      class=\"checked-question\"\n      top=\"15vh\"\n      :append-to-body=\"true\"\n      :z-index=\"3200\"\n    >\n      <div style=\"display: flex; height: 550px;\">\n        <!-- 左侧：题库列表 -->\n        <div style=\"width: 300px; padding-right: 10px;\">\n          <!-- 搜索区域 -->\n          <div style=\"padding: 5px; height: 42px; display: flex; gap: 10px;\">\n            <el-cascader\n              v-model=\"manualSelect.selectedCategory\"\n              :options=\"categoryOptions\"\n              :props=\"cascaderProps\"\n              placeholder=\"选择题库分类\"\n              size=\"small\"\n              style=\"width: 135px;\"\n              clearable\n              @change=\"searchQuestionBanks\"\n            ></el-cascader>\n\n            <el-input\n              v-model=\"manualSelect.bankSearchKeyword\"\n              placeholder=\"题库名称\"\n              size=\"small\"\n              style=\"width: 150px;\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchQuestionBanks\"></el-button>\n            </el-input>\n          </div>\n\n          <!-- 题库表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questionBanks\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @row-click=\"selectQuestionBank\"\n              highlight-current-row\n            >\n              <el-table-column type=\"index\" width=\"38\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"left\" header-align=\"center\"></el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding-top: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleBankSizeChange\"\n              @current-change=\"handleBankCurrentChange\"\n              :current-page=\"manualSelect.bankPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              :page-size=\"manualSelect.bankPagination.pageSize\"\n              :total=\"manualSelect.bankPagination.total\"\n              layout=\"prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 中间：题目列表 -->\n        <div style=\"width: 700px; padding: 0px 5px;\">\n          <!-- 筛选区域 -->\n          <div style=\"padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;\">\n            <el-select\n              v-model=\"manualSelect.questionType\"\n              placeholder=\"题型\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部题型\" value=\"\"></el-option>\n              <el-option label=\"单选题\" value=\"1\"></el-option>\n              <el-option label=\"多选题\" value=\"2\"></el-option>\n              <el-option label=\"判断题\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-select\n              v-model=\"manualSelect.difficulty\"\n              placeholder=\"难度\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部难度\" value=\"\"></el-option>\n              <el-option label=\"低\" value=\"1\"></el-option>\n              <el-option label=\"中\" value=\"2\"></el-option>\n              <el-option label=\"高\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-input\n              v-model=\"manualSelect.questionSearchKeyword\"\n              placeholder=\"搜索题目\"\n              size=\"small\"\n              style=\"width: 250px;\"\n            >\n              <template slot=\"append\">\n                <el-button @click=\"searchQuestions\" style=\"border-left: 1px solid #dcdfe6;\">搜索</el-button>\n                <el-button @click=\"resetQuestionSearch\" style=\"border-left: 1px solid #dcdfe6;\">重置</el-button>\n              </template>\n            </el-input>\n          </div>\n\n          <!-- 题目表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questions\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @selection-change=\"handleQuestionSelectionChange\"\n              :row-class-name=\"getQuestionRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"40\" :selectable=\"isQuestionSelectable\"></el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题干\" min-width=\"400\" header-align=\"center\" class-name=\"question-content-column\">\n                <template slot-scope=\"scope\">\n                  <div class=\"question-content-text\">\n                    {{ scope.row.questionContent }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionType\" label=\"题目类型\" width=\"78\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getQuestionTypeText(scope.row.questionType) }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"50\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getDifficultyText(scope.row.difficulty) }}\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleQuestionSizeChange\"\n              @current-change=\"handleQuestionCurrentChange\"\n              :current-page=\"manualSelect.questionPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 40, 50, 100]\"\n              :page-size=\"manualSelect.questionPagination.pageSize\"\n              :total=\"manualSelect.questionPagination.total\"\n              layout=\"sizes, prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 右侧：统计信息 -->\n        <div style=\"width: 150px; padding: 0px 5px;\">\n          <div style=\"padding: 7px 0px; height: 42px; font-size: 16px;\">试卷题型统计</div>\n          <div style=\"border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;\">\n            <!-- 显示已添加到试卷的题目统计 -->\n            <div v-for=\"(count, type) in fixedQuestionStats\" :key=\"type\" style=\"margin-bottom: 10px;\">\n              <div style=\"font-size: 14px; color: #606266;\">\n                {{ getQuestionTypeText(type) }}：{{ count }} 题\n              </div>\n            </div>\n\n            <!-- 显示当前选择的题目统计 -->\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;\">\n              <div style=\"font-size: 13px; color: #909399; margin-bottom: 8px;\">本次选择：</div>\n              <div v-for=\"(count, type) in manualSelect.selectedStats\" :key=\"'selected-' + type\" style=\"margin-bottom: 8px;\">\n                <div style=\"font-size: 13px; color: #409eff;\">\n                  {{ getQuestionTypeText(type) }}：{{ count }} 题\n                </div>\n              </div>\n            </div>\n          </div>\n          <div style=\"width: 140px; text-align: right; padding-right: 5px;\">\n            <div style=\"font-size: 14px; font-weight: bold;\">总题数：{{ fixedQuestions.length }} 题</div>\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"font-size: 12px; color: #409eff;\">\n              +{{ manualSelect.selectedQuestions.length }} 题\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showManualSelectDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"confirmManualSelect\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加规则对话框 -->\n    <el-dialog\n      :title=\"getDialogTitle()\"\n      :visible.sync=\"showAddRuleDialog\"\n      width=\"900px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3100\"\n    >\n      <div style=\"margin-bottom: -30px;\">\n        <el-form :model=\"ruleForm\" label-width=\"140px\">\n          <!-- 选择规则类型 -->\n          <el-form-item v-if=\"shouldShowRuleTypeSelection()\" label=\"选择规则类型\">\n            <el-radio-group v-model=\"ruleForm.ruleType\">\n              <el-radio\n                v-if=\"shouldShowRuleType(3)\"\n                :label=\"3\"\n                :disabled=\"shouldDisableRuleType(3)\"\n              >题库</el-radio>\n              <el-radio\n                v-if=\"shouldShowRuleType(1)\"\n                :label=\"1\"\n                :disabled=\"shouldDisableRuleType(1)\"\n              >题型</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 规则生成方式 -->\n          <el-form-item v-if=\"currentOperation !== 'addSub'\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                规则生成方式\n              </div>\n            </template>\n            <el-radio-group v-model=\"ruleForm.generateType\">\n              <el-radio label=\"divided\">分开设置</el-radio>\n              <el-radio label=\"one\">整体设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 已选择的题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"点击选项可取消选择。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                已选择的题库\n              </div>\n            </template>\n            <span v-if=\"ruleForm.selectedItems.length === 0\" class=\"ml20\">\n              暂无选择，请至少选择一项。\n            </span>\n            <div v-else>\n              <el-tag\n                v-for=\"item in selectedBanks\"\n                :key=\"item.bankId\"\n                closable\n                @close=\"removeSelectedBank(item.bankId)\"\n                type=\"info\"\n                size=\"medium\"\n                class=\"selected-bank-tag\"\n              >\n                {{ item.bankName }}\n              </el-tag>\n            </div>\n          </el-form-item>\n\n          <!-- 选择题型 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 1\" label=\"选择题型\">\n            <el-checkbox-group v-model=\"ruleForm.selectedQuestionTypes\">\n              <el-checkbox v-if=\"shouldShowQuestionType('1')\" label=\"1\">单选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('2')\" label=\"2\">多选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('3')\" label=\"3\">判断题</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n\n\n\n          <!-- 选择题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\" label=\"选择题库\">\n            <!-- 搜索区域 -->\n            <div class=\"clearfix\" style=\"margin-bottom: 6px;\">\n              <div style=\"float: right; display: flex; align-items: center;\">\n                <el-input\n                  v-model=\"searchKeyword\"\n                  placeholder=\"题库名称\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 8px;\"\n                  @keyup.enter.native=\"handleSearch\"\n                  clearable\n                >\n                  <el-button slot=\"append\" @click=\"handleSearch\">搜索</el-button>\n                </el-input>\n                <el-button size=\"small\" @click=\"handleReset\">重置</el-button>\n              </div>\n            </div>\n\n            <!-- 题库表格 -->\n            <el-table\n              ref=\"questionBankTable\"\n              :data=\"questionBanks\"\n              border\n              size=\"small\"\n              max-height=\"300\"\n              v-loading=\"questionBankLoading\"\n              @selection-change=\"handleSelectionChange\"\n              :row-class-name=\"getRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"40\" label=\"#\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" :selectable=\"isRowSelectable\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"questionCount\" label=\"题目数量\" width=\"80\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.questionCount || 0 }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分类\" width=\"150\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getCategoryName(scope.row.categoryId) }}\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <!-- 分页 -->\n            <div style=\"text-align: right; margin-top: 10px;\">\n              <el-pagination\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"queryParams.pageNum\"\n                :page-size=\"queryParams.pageSize\"\n                layout=\"total, prev, pager, next\"\n                :total=\"total\"\n                small\n                background\n              >\n              </el-pagination>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showAddRuleDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSaveRule\">保 存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 固定试卷随机抽题对话框 -->\n    <el-dialog\n      title=\"随机抽题规则设置\"\n      :visible.sync=\"showFixedRandomDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"随机抽题规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFixedRandomRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加抽题规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ fixedRandomTotalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ fixedRandomTotalScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"设置抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      选择题库和题型，设置抽题数量和分数\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"确认抽题\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      根据规则随机抽取题目并添加到试卷\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"fixedRandomParentRules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in fixedRandomParentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          题库：\n                        </div>\n                        <div class=\"rule-content\">\n                          <span>\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"handleAddFixedRandomSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}：\n                          </div>\n                          <div class=\"rule-content\">\n                            <span v-if=\"childRule.type === 3\">\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"fixedRandomRules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加抽题规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showFixedRandomDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleConfirmFixedRandomSelect\">确认抽题</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 批量设置分数对话框 -->\n    <el-dialog\n      title=\"批量设置分数\"\n      :visible.sync=\"showBatchScoreDialog\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n    >\n      <el-form :model=\"batchScoreForm\" label-width=\"120px\">\n        <el-form-item label=\"设置范围\">\n          <el-radio-group v-model=\"batchScoreForm.scoreType\">\n            <el-radio label=\"all\">全部题目</el-radio>\n            <el-radio label=\"selected\">选中题目</el-radio>\n            <el-radio label=\"byType\">按题型</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <el-form-item v-if=\"batchScoreForm.scoreType === 'byType'\" label=\"选择题型\">\n          <el-select v-model=\"batchScoreForm.questionType\" placeholder=\"请选择题型\">\n            <el-option label=\"单选题\" value=\"1\"></el-option>\n            <el-option label=\"多选题\" value=\"2\"></el-option>\n            <el-option label=\"判断题\" value=\"3\"></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"分数\">\n          <el-input-number\n            v-model=\"batchScoreForm.score\"\n            :min=\"0.5\"\n            :step=\"0.5\"\n            :precision=\"1\"\n            style=\"width: 150px;\"\n          ></el-input-number>\n          <span style=\"margin-left: 10px;\">分</span>\n        </el-form-item>\n\n        <el-form-item label=\"影响题目\">\n          <div style=\"color: #909399; font-size: 13px;\">\n            {{ getBatchScoreAffectedCount() }} 道题目将被设置为 {{ batchScoreForm.score }} 分\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showBatchScoreDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirmBatchSetScore\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n/* 表格垂直对齐优化 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {\n  vertical-align: middle;\n}\n\n/* 多选框垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n/* 序号列垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n/* 规则节点样式 */\n.rule-node-cascade {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.rule-node-cascade:hover {\n  border: 1px dashed #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n/* 左侧题库信息 */\n.rule-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.rule-type-label {\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n  min-width: 60px;\n}\n\n.rule-content {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 右侧操作区域 */\n.rule-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rule-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.control-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  white-space: nowrap;\n}\n\n.control-divider {\n  color: #999;\n  margin: 0 4px;\n}\n\n.total-score {\n  font-weight: 600;\n  color: #333;\n}\n\n.input-number-mini {\n  width: 110px !important;\n}\n\n.rule-actions {\n  display: flex;\n  gap: 2px;\n}\n\n.action-btn {\n  color: #409eff !important;\n  padding: 4px 6px !important;\n  margin-left: 0 !important;\n}\n\n.action-btn:hover {\n  background-color: #ecf5ff !important;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n/* 子规则样式 */\n.children-rules {\n  margin-top: 12px;\n  margin-left: 20px;\n  border-left: 2px dashed #e8e8e8;\n  padding-left: 20px;\n}\n\n.children_component {\n  margin-bottom: 8px !important;\n  border: 1px dashed #d9d9d9 !important;\n  background-color: #f9f9f9 !important;\n}\n\n.children_component:hover {\n  border: 1px dashed #409eff !important;\n  background-color: #ecf5ff !important;\n}\n\n.parent-rule {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 禁用的表格行样式 */\n::v-deep .disabled-row {\n  background-color: #f5f5f5 !important;\n  color: #c0c4cc !important;\n}\n\n::v-deep .disabled-row:hover {\n  background-color: #f5f5f5 !important;\n}\n\n::v-deep .disabled-row td {\n  color: #c0c4cc !important;\n}\n\n/* random-paper 规则显示样式 */\n.rule-display-container {\n  border: 1px dashed #d0d0d0;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #fafbfc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 题库容器悬停效果 */\n.rule-display-container:hover {\n  border-color: #409eff;\n  background-color: #f8fbff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.rule-display-container:hover .parent-rule-display {\n  background-color: #e3f2fd;\n  border-left-color: #1976d2;\n}\n\n.rule-display-container:hover .parent-rule-display .rule-label {\n  color: #1976d2;\n}\n\n.parent-rule-display {\n  font-size: 14px;\n  color: #303133;\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n  border-left: 4px solid #409eff;\n}\n\n.children-rules-display {\n  margin-left: 20px;\n}\n\n.child-rule-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px;\n  margin-bottom: 8px;\n  background-color: #ffffff;\n  border: 1px dashed #d0d0d0;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.child-rule-display:last-child {\n  margin-bottom: 0;\n}\n\n/* 子规则悬停效果 */\n.child-rule-display:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.child-rule-display:hover .rule-label {\n  color: #1976d2;\n}\n\n.child-rule-display:hover .rule-stats {\n  background-color: #e3f2fd;\n  border-color: #90caf9;\n}\n\n.child-rule-left {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.child-rule-right {\n  flex-shrink: 0;\n  margin-left: 20px;\n}\n\n.rule-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.rule-content {\n  color: #303133;\n}\n\n.rule-stats {\n  font-size: 13px;\n  color: #606266;\n  background-color: #f0f9ff;\n  padding: 4px 8px;\n  border-radius: 5px;\n  border: 1px solid #b3d8ff;\n  white-space: nowrap;\n}\n\n.rule-stats strong {\n  color: #409eff;\n  font-weight: 600;\n}\n\n/* 固定试卷样式 */\n.question-list {\n  margin-top: 20px;\n}\n\n.question-item {\n  margin-bottom: 12px;\n  background-color: #fafafa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.question-item:hover {\n  background-color: #f0f9ff;\n  border-color: #409eff;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 15px;\n}\n\n.question-header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.question-header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.question-number {\n  font-weight: bold;\n  color: #409eff;\n  margin-left: 10px;\n  margin-right: 10px;\n  min-width: 30px;\n}\n\n.question-content {\n  flex: 1;\n  color: #303133;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-right: 15px;\n  cursor: pointer;\n}\n\n.question-expand-icon {\n  margin-right: 8px;\n  margin-left: 8px;\n  color: #909399;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n  font-size: 12px;\n}\n\n.question-expand-icon:hover {\n  color: #409eff;\n}\n\n.question-details {\n  border-top: 1px solid #e4e7ed;\n  padding: 15px;\n  background-color: #ffffff;\n}\n\n.question-info {\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: inline-block;\n  margin-right: 20px;\n  font-size: 13px;\n  color: #606266;\n}\n\n.question-options {\n  margin-top: 10px;\n}\n\n.options-title {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.option-item.correct-option {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.option-key {\n  font-weight: bold;\n  margin-right: 8px;\n  min-width: 20px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n.el-button-group {\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.icon_size {\n  font-size: 14px;\n}\n\n/* 禁用题目行样式 */\n.disabled-question-row {\n  background-color: #f5f7fa !important;\n  color: #c0c4cc !important;\n}\n\n.disabled-question-row:hover {\n  background-color: #f5f7fa !important;\n}\n\n.disabled-question-row td {\n  color: #c0c4cc !important;\n}\n\n/* 题干内容左对齐样式 */\n.question-content-text {\n  max-height: 60px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: left !important;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n/* 强制题干列内容左对齐 */\n.el-table .question-content-column {\n  text-align: left !important;\n}\n\n.el-table .question-content-column .cell {\n  text-align: left !important;\n  padding-left: 10px !important;\n  padding-right: 10px !important;\n}\n\n/* 已选择题库标签样式 */\n.selected-bank-tag {\n  margin-right: 8px !important;\n  margin-bottom: 4px !important;\n  font-size: 13px !important;\n}\n\n/* 修复标签关闭按钮样式 */\n::v-deep .selected-bank-tag .el-tag__close {\n  color: #909399 !important;\n  font-size: 12px !important;\n  margin-left: 6px !important;\n  cursor: pointer !important;\n  border-radius: 50% !important;\n  width: 16px !important;\n  height: 16px !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  line-height: 1 !important;\n  vertical-align: middle !important;\n}\n\n::v-deep .selected-bank-tag .el-tag__close:hover {\n  background-color: #909399 !important;\n  color: #fff !important;\n}\n</style>\n\n<script>\nimport { listQuestionBank } from \"@/api/biz/questionBank\"\nimport { listCategory } from \"@/api/biz/category\"\nimport { getQuestionStatistics, listQuestion } from \"@/api/biz/question\"\n\nexport default {\n  name: \"PaperCreate\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    paperId: {\n      type: [String, Number],\n      default: null\n    }\n  },\n  data() {\n    return {\n      // 右侧面板状态\n      rightPanelCollapsed: false,\n      rightPanelWidth: 410,\n      \n      // 折叠面板激活项（accordion模式下为字符串）\n      activeCollapse: 'basic',\n      \n      // 试卷表单数据\n      paperForm: {\n        paperId: null,\n        paperName: '',\n        paperDesc: '',\n        paperType: 1, // 0: 固定试卷, 1: 随机试卷\n        coverImg: '',\n        totalScore: 100,\n        passScore: 60,\n        startTime: null,\n        endTime: null,\n        duration: 90, // 考试时长，分钟\n        lateLimit: 0, // 迟到限制，分钟\n        allowEarlySubmit: 0, // 是否允许提前交卷\n        earlySubmitTime: 40, // 提前交卷时间，分钟\n        showScore: 0, // 是否显示分值\n        showType: 0, // 是否显示题型\n        requireAllAnswered: 0, // 是否要求全部答完才能交卷\n        showResult: 0, // 是否显示成绩\n        showCorrect: 0, // 是否显示对错\n        showAnswer: 0, // 是否显示答案\n        showAnalysis: 0, // 是否显示解析\n        status: 0, // 状态：0未发布 1已发布\n        enableTimeLimit: 0, // 是否启用考试时间限制\n        durationSeconds: 0, // 时长秒数\n        createTime: null\n      },\n      \n      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）\n\n      // 固定试卷相关数据\n      fixedQuestions: [], // 固定试卷的题目列表\n      selectAll: false, // 全选状态\n      isExpanded: false, // 展开状态\n\n      // 手动选题对话框\n      showManualSelectDialog: false,\n      categoryOptions: [], // 分类选项数据\n      cascaderProps: {\n        value: 'id',\n        label: 'name',\n        children: 'children',\n        checkStrictly: false,\n        emitPath: false\n      },\n      manualSelect: {\n        selectedCategory: '', // 选择的题库目录\n        bankSearchKeyword: '', // 题库搜索关键词\n        questionBanks: [], // 题库列表\n        bankPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedBankId: null, // 选择的题库ID\n        questionType: '', // 题型筛选\n        difficulty: '', // 难度筛选\n        questionSearchKeyword: '', // 题目搜索关键词\n        questions: [], // 题目列表\n        questionPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedQuestions: [], // 选中的题目\n        selectedStats: {} // 选中题目的统计信息\n      },\n\n      // 固定试卷随机抽题对话框\n      showFixedRandomDialog: false,\n\n      // 固定试卷随机抽题相关数据\n      fixedRandomRules: [], // 临时规则列表，用于固定试卷随机抽题\n      fixedRandomForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n\n      // 批量设置分数对话框\n      showBatchScoreDialog: false,\n      batchScoreForm: {\n        scoreType: 'all', // all: 全部题目, selected: 选中题目, byType: 按题型\n        score: 1, // 分数值\n        questionType: '' // 题型（当scoreType为byType时使用）\n      },\n\n      // 随机规则对话框\n      showRuleDialog: false,\n      // 规则列表\n      rules: [],\n\n      // 添加规则对话框\n      showAddRuleDialog: false,\n      ruleForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n      // 当前操作状态\n      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则\n      editingRule: null, // 正在编辑的规则\n      parentRule: null, // 父规则（添加子规则时使用）\n      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量\n      questionBanks: [],\n      questionBankLoading: false,\n      categoryOptions: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n    }\n  },\n  computed: {\n    // 已选择的题库\n    selectedBanks() {\n      return this.questionBanks.filter(bank =>\n        this.ruleForm.selectedItems.includes(bank.bankId)\n      )\n    },\n\n    // 计算总题目数量\n    totalQuestions() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的题目数\n          rule.children.forEach(child => {\n            total += child.selectedCount || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的题目数\n          total += rule.selectedCount || 0\n        }\n      })\n      return total\n    },\n\n    // 计算总分数\n    totalRuleScore() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的总分\n          rule.children.forEach(child => {\n            total += child.totalScore || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的总分\n          total += rule.totalScore || 0\n        }\n      })\n      return total\n    },\n\n    // 获取父规则列表（只包含没有parentId的规则）\n    parentRules() {\n      return this.rules.filter(rule => !rule.parentId)\n    },\n\n    // 获取已使用的题库ID列表\n    usedBankIds() {\n      const usedIds = new Set()\n      this.rules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    },\n\n    // 计算已添加到试卷的题目统计\n    fixedQuestionStats() {\n      const stats = {}\n      this.fixedQuestions.forEach(question => {\n        const type = question.type\n        stats[type] = (stats[type] || 0) + 1\n      })\n      return stats\n    },\n\n    // 固定试卷随机抽题总题数\n    fixedRandomTotalQuestions() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.selectedCount || 0\n      })\n      return total\n    },\n\n    // 固定试卷随机抽题总分数\n    fixedRandomTotalScore() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.totalScore || 0\n      })\n      return total\n    },\n\n    // 获取固定试卷随机抽题父规则列表（只包含没有parentId的规则）\n    fixedRandomParentRules() {\n      return this.fixedRandomRules.filter(rule => !rule.parentId)\n    },\n\n    // 获取固定试卷随机抽题已使用的题库ID列表\n    usedFixedRandomBankIds() {\n      const usedIds = new Set()\n      this.fixedRandomRules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val && this.paperId) {\n        this.loadPaperData()\n      }\n    },\n\n    // 监听题型选择变化\n    'ruleForm.selectedQuestionTypes': {\n      async handler(newVal) {\n        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {\n          await this.updateQuestionTypeCount()\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 返回试卷列表 */\n    handleBack() {\n      this.$emit('close')\n    },\n    \n    /** 切换右侧面板 */\n    toggleRightPanel() {\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\n      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400\n    },\n    \n    /** 考试入口 */\n    handleExamEntry() {\n      this.$message.info('考试入口功能开发中...')\n    },\n    \n    /** 设置考试页面 */\n    handlePageSetting() {\n      this.$message.info('设置考试页面功能开发中...')\n    },\n    \n    /** 发布试卷 */\n    handlePublish() {\n      this.$message.info('发布试卷功能开发中...')\n    },\n    \n    /** 邀请考生 */\n    handleInviteStudents() {\n      this.$message.info('邀请考生功能开发中...')\n    },\n    \n    /** 已邀请列表 */\n    handleInviteList() {\n      this.$message.info('已邀请列表功能开发中...')\n    },\n    \n\n    \n    /** 添加规则（随机试卷） */\n    handleAddRule() {\n      this.showRuleDialog = true\n    },\n\n    /** 编辑规则（打开规则编辑界面） */\n    handleEditRules() {\n      this.showRuleDialog = true\n    },\n\n    /** 添加一级规则 */\n    handleAddFirstRule() {\n      this.currentOperation = 'add'\n      this.editingRule = null\n\n      // 如果已有规则，限制只能选择相同类型\n      if (this.rules.length > 0) {\n        this.ruleForm.ruleType = this.rules[0].type\n      } else {\n        this.ruleForm.ruleType = 3 // 默认题库\n      }\n\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 加载题库列表 */\n    loadQuestionBanks() {\n      this.questionBankLoading = true\n      // 同时加载题库和分类数据\n      Promise.all([\n        listQuestionBank(this.queryParams),\n        listCategory({ pageSize: 1000 })\n      ]).then(([bankResponse, categoryResponse]) => {\n        const questionBanks = bankResponse.rows || []\n        this.total = bankResponse.total || 0\n        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []\n\n        // 为每个题库获取题目统计\n        const statisticsPromises = questionBanks.map(bank =>\n          getQuestionStatistics(bank.bankId).then(stats => {\n            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0\n            return bank\n          }).catch(() => {\n            bank.questionCount = 0\n            return bank\n          })\n        )\n\n        Promise.all(statisticsPromises).then(banksWithStats => {\n          this.questionBanks = banksWithStats\n          this.questionBankLoading = false\n        })\n      }).catch(() => {\n        this.questionBankLoading = false\n      })\n    },\n\n    /** 保存规则 */\n    handleSaveRules() {\n      this.$message.info('保存规则功能开发中...')\n      this.showRuleDialog = false\n    },\n\n    /** 保存单个规则 */\n    async handleSaveRule() {\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {\n        this.$message.warning('请至少选择一个题库')\n        return\n      }\n      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {\n        this.$message.warning('请至少选择一个题型')\n        return\n      }\n\n      // 题型规则验证：检查是否有可用题目\n      if (this.ruleForm.ruleType === 1) {\n        if (this.currentQuestionTypeCount === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')\n          return\n        }\n      }\n\n      if (this.currentOperation === 'edit') {\n        // 编辑现有规则\n        this.updateExistingRule()\n      } else if (this.currentOperation === 'addFixedRandom') {\n        // 添加固定试卷随机抽题规则\n        this.addFixedRandomRule()\n      } else if (this.currentOperation === 'addFixedRandomSub') {\n        // 添加固定试卷随机抽题子规则\n        this.addFixedRandomSubRule()\n      } else {\n        // 添加新规则（包括添加子规则）\n        this.addNewRule()\n      }\n\n      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')\n      this.showAddRuleDialog = false\n\n      // 重置表单\n      this.resetRuleForm()\n    },\n\n    /** 更新现有规则 */\n    updateExistingRule() {\n      const rule = this.editingRule\n      rule.type = this.ruleForm.ruleType\n      rule.generateType = this.ruleForm.generateType\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n\n        // 重新计算总分，确保选取数量不超过最大题目数\n        if (rule.selectedCount > rule.maxQuestions) {\n          rule.selectedCount = rule.maxQuestions\n        }\n        rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n    },\n\n    /** 添加新规则 */\n    addNewRule() {\n      // 如果是添加子规则，按原逻辑处理\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        this.addSingleRule()\n        return\n      }\n\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index, // 确保每个规则有唯一ID\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 0.5,\n            totalScore: 0.5,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateRuleScore(rule)\n          this.rules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleRule()\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    addFixedRandomRule() {\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index,\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 1,\n            totalScore: 1,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateFixedRandomRuleScore(rule)\n          this.fixedRandomRules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleFixedRandomRule()\n      }\n    },\n\n    /** 添加单个固定试卷随机抽题规则 */\n    addSingleFixedRandomRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      }\n\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    addFixedRandomSubRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        parentId: this.parentRule.id,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      } else if (this.ruleForm.ruleType === 3) {\n        // 题库规则（虽然在固定试卷随机抽题中子规则主要是题型，但保持兼容性）\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      }\n\n      // 确保父规则有children数组\n      if (!this.parentRule.children) {\n        this.$set(this.parentRule, 'children', [])\n      }\n\n      // 添加到父规则的children中\n      this.parentRule.children.push(rule)\n\n      // 同时添加到主规则列表中（用于统计和抽题）\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加单个规则 */\n    addSingleRule() {\n      const rule = {\n        id: Date.now(), // 临时ID\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1, // 默认选取1题\n        scorePerQuestion: 0.5, // 默认每题0.5分\n        totalScore: 0.5, // 默认总分0.5分\n        maxQuestions: 0 // 最大题目数\n      }\n\n      // 如果是添加子规则\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        rule.parentId = this.parentRule.id\n\n        // 确保父规则有children数组\n        if (!this.parentRule.children) {\n          this.$set(this.parentRule, 'children', [])\n        }\n\n        // 添加到父规则的children中\n        this.parentRule.children.push(rule)\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n\n      // 只有父规则才添加到主规则列表\n      if (this.currentOperation !== 'addSub') {\n        this.rules.push(rule)\n      }\n\n      // 更新规则分数\n      this.updateRuleScore(rule)\n    },\n\n    /** 重置规则表单 */\n    resetRuleForm() {\n      this.ruleForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n      // 只有不是固定试卷随机抽题时才重置操作状态\n      if (this.currentOperation !== 'addFixedRandom' && this.currentOperation !== 'addFixedRandomSub') {\n        this.currentOperation = 'add'\n        this.editingRule = null\n        this.parentRule = null\n      }\n      this.currentQuestionTypeCount = 0\n    },\n\n    /** 搜索题库 */\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = this.searchKeyword || undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 重置搜索 */\n    handleReset() {\n      this.searchKeyword = ''\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 表格选择变化 */\n    handleSelectionChange(selection) {\n      this.ruleForm.selectedItems = selection.map(item => item.bankId)\n    },\n\n    /** 移除已选择的题库 */\n    removeSelectedBank(bankId) {\n      const index = this.ruleForm.selectedItems.indexOf(bankId)\n      if (index > -1) {\n        this.ruleForm.selectedItems.splice(index, 1)\n      }\n\n      // 同步取消表格中的勾选状态\n      this.$nextTick(() => {\n        const table = this.$refs.questionBankTable\n        if (table) {\n          // 找到对应的行数据\n          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)\n          if (rowToDeselect) {\n            table.toggleRowSelection(rowToDeselect, false)\n          }\n        }\n      })\n    },\n\n    /** 分页变化 */\n    handleCurrentChange(page) {\n      this.queryParams.pageNum = page\n      this.loadQuestionBanks()\n    },\n\n    /** 根据分类ID获取分类名称 */\n    getCategoryName(categoryId) {\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\n      return category ? category.name : '未分类'\n    },\n\n    /** 在分类数据中查找分类（支持扁平和树形结构） */\n    findCategoryById(categories, id) {\n      // 创建扁平化的分类列表\n      const flatCategories = this.flattenCategories(categories)\n\n      // 在扁平化列表中查找\n      const category = flatCategories.find(cat => cat.id === id)\n      return category || null\n    },\n\n    /** 将树形分类数据扁平化 */\n    flattenCategories(categories) {\n      let result = []\n\n      function flatten(cats) {\n        for (const cat of cats) {\n          result.push(cat)\n          if (cat.children && cat.children.length > 0) {\n            flatten(cat.children)\n          }\n        }\n      }\n\n      flatten(categories)\n      return result\n    },\n\n    /** 更新规则分数 */\n    updateRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 获取题型文本 */\n    getQuestionTypeText(questionTypes) {\n      const questionTypeMap = {\n        '1': '单选题', '2': '多选题', '3': '判断题',\n        1: '单选题', 2: '多选题', 3: '判断题'\n      }\n\n      // 如果是数组，处理多个题型\n      if (Array.isArray(questionTypes)) {\n        return questionTypes.map(t => questionTypeMap[t]).join('、')\n      }\n\n      // 如果是单个值，直接返回对应文本\n      return questionTypeMap[questionTypes] || '未知'\n    },\n\n\n\n    /** 获取子规则标签 */\n    getChildRuleLabel(childRule) {\n      if (childRule.type === 3) {\n        return '题库：'\n      } else if (childRule.type === 1) {\n        // 题型子规则只显示具体的题型名称，不显示\"题型：\"前缀\n        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return questionTypeMap[childRule.selectedQuestionTypes[0]]\n        } else {\n          return '题型'\n        }\n      }\n      return '规则：'\n    },\n\n    /** 获取规则类型标签（用于random-paper显示） */\n    getRuleTypeLabel(rule) {\n      if (rule.type === 3) {\n        return '题库：'\n      } else if (rule.type === 1) {\n        return '题型：'\n      }\n      return '规则：'\n    },\n\n    /** 获取规则内容（用于random-paper显示） */\n    getRuleContent(rule) {\n      if (rule.type === 3) {\n        // 题库规则显示题库名称\n        if (rule.selectedBanks && rule.selectedBanks.length > 0) {\n          return rule.selectedBanks.map(bank => bank.bankName).join('、')\n        }\n        return '未选择题库'\n      } else if (rule.type === 1) {\n        // 题型规则显示题型名称\n        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')\n        }\n        return '未选择题型'\n      }\n      return '未配置'\n    },\n\n    /** 添加子规则 */\n    addSubRule(rule) {\n      this.currentOperation = 'addSub'\n      this.parentRule = rule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n\n      if (rule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 编辑规则 */\n    editRule(rule) {\n      this.currentOperation = 'edit'\n      this.editingRule = rule\n      this.ruleForm.ruleType = rule.type\n      this.ruleForm.generateType = rule.generateType\n\n      if (rule.type === 3) {\n        // 题库规则\n        this.ruleForm.selectedItems = [...rule.selectedItems]\n      } else if (rule.type === 1) {\n        // 题型规则\n        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]\n        // 编辑题型规则时，更新题目数量\n        this.$nextTick(() => {\n          this.updateQuestionTypeCount()\n        })\n      }\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 删除规则 */\n    deleteRule(rule) {\n      this.$confirm('确定要删除这条规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = this.rules.findIndex(r => r.id === rule.id)\n        if (index > -1) {\n          this.rules.splice(index, 1)\n          this.$message.success('规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 删除子规则 */\n    deleteChildRule(parentRule, childRule) {\n      this.$confirm('确定要删除这条子规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = parentRule.children.findIndex(child => child.id === childRule.id)\n        if (index > -1) {\n          parentRule.children.splice(index, 1)\n          this.$message.success('子规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 获取对话框标题 */\n    getDialogTitle() {\n      switch (this.currentOperation) {\n        case 'add':\n          return '添加规则'\n        case 'edit':\n          return '编辑规则'\n        case 'addSub':\n          return '添加子规则'\n        case 'addFixedRandom':\n          return '添加抽题规则'\n        case 'addFixedRandomSub':\n          return '添加题型规则'\n        default:\n          return '添加规则'\n      }\n    },\n\n    /** 是否显示规则类型选择 */\n    shouldShowRuleTypeSelection() {\n      // 固定试卷随机抽题的一级规则只能选择题库，但仍显示选择界面\n      if (this.currentOperation === 'addFixedRandom') {\n        return true\n      }\n      // 固定试卷随机抽题的子规则可以选择规则类型\n      if (this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n      // 其他操作都显示规则类型选择\n      return true\n    },\n\n    /** 是否显示规则类型选项 */\n    shouldShowRuleType(ruleType) {\n      // 编辑时只显示当前规则的类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type === ruleType\n      }\n\n      // 添加子规则时的逻辑\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        // 题型规则的子规则只能是题库规则\n        if (this.parentRule.type === 1) {\n          return ruleType === 3\n        }\n        // 题库规则的子规则只能是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 添加一级规则时的逻辑\n      if (this.currentOperation === 'add') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题一级规则时的逻辑\n      if (this.currentOperation === 'addFixedRandom') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题子规则时的逻辑\n      if (this.currentOperation === 'addFixedRandomSub' && this.parentRule) {\n        // 题库规则的子规则可以是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 其他情况显示所有类型\n      return true\n    },\n\n    /** 是否显示特定题型选项 */\n    shouldShowQuestionType(questionType) {\n      // 如果不是添加子规则，显示所有题型\n      if (this.currentOperation !== 'addSub' && this.currentOperation !== 'addFixedRandomSub') {\n        return true\n      }\n\n      if (!this.parentRule) {\n        return true\n      }\n\n      // 获取父规则下已有的题型子规则中选择的题型\n      const existingQuestionTypes = []\n      if (this.parentRule.children) {\n        this.parentRule.children.forEach(child => {\n          if (child.type === 1 && child.selectedQuestionTypes) {\n            existingQuestionTypes.push(...child.selectedQuestionTypes)\n          }\n        })\n      }\n\n      // 如果该题型已经被选择过，则隐藏\n      return !existingQuestionTypes.includes(questionType)\n    },\n\n    /** 判断表格行是否可选择 */\n    isRowSelectable(row) {\n      // 如果是编辑操作，允许选择\n      if (this.currentOperation === 'edit') {\n        return true\n      }\n\n      // 如果是添加子规则，允许选择（子规则不涉及题库选择）\n      if (this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n\n      // 如果是固定试卷随机抽题，检查该题库是否已经被使用\n      if (this.currentOperation === 'addFixedRandom') {\n        return !this.usedFixedRandomBankIds.includes(row.bankId)\n      }\n\n      // 如果是添加一级规则，检查题库是否已被使用\n      return !this.usedBankIds.includes(row.bankId)\n    },\n\n    /** 获取表格行的样式类名 */\n    getRowClassName({ row }) {\n      // 如果题库已被使用且不是编辑操作，添加禁用样式\n      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {\n        return 'disabled-row'\n      }\n      return ''\n    },\n\n    /** 更新题型题目数量 */\n    async updateQuestionTypeCount() {\n      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {\n        this.currentQuestionTypeCount = 0\n        return\n      }\n\n      // 获取题库信息\n      let selectedBanks = []\n      if ((this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') && this.parentRule && this.parentRule.type === 3) {\n        // 添加子规则时，使用父规则的题库\n        selectedBanks = this.parentRule.selectedBanks || []\n      } else {\n        // 其他情况使用当前选择的题库\n        selectedBanks = this.selectedBanks\n      }\n\n      if (!selectedBanks.length) {\n        this.currentQuestionTypeCount = 0\n        this.$message.warning('请先选择题库')\n        return\n      }\n\n      try {\n        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)\n        this.currentQuestionTypeCount = count\n\n        if (count === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目')\n        }\n      } catch (error) {\n        console.error('查询题目数量失败:', error)\n        this.currentQuestionTypeCount = 0\n        this.$message.error('查询题目数量失败')\n      }\n    },\n\n    /** 根据题库和题型查询题目数量 */\n    async getQuestionCountByType(banks, questionTypes) {\n      if (!banks.length || !questionTypes.length) {\n        return 0\n      }\n\n      let totalCount = 0\n\n      // 遍历每个题库，查询题目统计\n      for (const bank of banks) {\n        try {\n          // 使用已导入的API方法\n          const response = await getQuestionStatistics(bank.bankId)\n          console.log(`题库${bank.bankId}统计数据:`, response)\n\n          if (response.code === 200 && response.data) {\n            const statistics = response.data\n\n            // 根据选择的题型累加数量\n            questionTypes.forEach(type => {\n              switch (type) {\n                case '1': // 单选题\n                  totalCount += statistics.singleChoice || 0\n                  break\n                case '2': // 多选题\n                  totalCount += statistics.multipleChoice || 0\n                  break\n                case '3': // 判断题\n                  totalCount += statistics.judgment || 0\n                  break\n              }\n            })\n          }\n        } catch (error) {\n          console.error(`查询题库${bank.bankId}统计信息失败:`, error)\n        }\n      }\n\n      console.log(`总题目数量: ${totalCount}`)\n      return totalCount\n    },\n\n    /** 是否禁用规则类型 */\n    shouldDisableRuleType(ruleType) {\n      // 编辑时不能更改类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type !== ruleType\n      }\n\n      // 添加一级规则时，只能选择题库，禁用其他类型\n      if (this.currentOperation === 'add') {\n        return ruleType !== 3\n      }\n\n      return false\n    },\n    \n    /** 全选/取消全选 */\n    handleSelectAll() {\n      this.fixedQuestions.forEach(question => {\n        question.selected = this.selectAll\n      })\n    },\n\n    /** 删除选中题目 */\n    handleDeleteSelected() {\n      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)\n      if (selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)\n        this.selectAll = false\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 手动选题 */\n    handleManualSelect() {\n      this.showManualSelectDialog = true\n      this.initManualSelectData()\n    },\n\n    /** 初始化手动选题数据 */\n    initManualSelectData() {\n      // 重置数据\n      this.manualSelect.selectedCategory = ''\n      this.manualSelect.bankSearchKeyword = ''\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.manualSelect.selectedQuestions = []\n      this.manualSelect.selectedStats = {}\n\n      // 加载分类数据\n      this.loadCategoryTree()\n\n      // 加载题库列表\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 加载分类树数据 */\n    loadCategoryTree() {\n      listCategory({ pageSize: 1000 }).then(response => {\n        const categories = response.rows || []\n        this.categoryOptions = this.buildCategoryTree(categories)\n      }).catch(error => {\n        console.error('加载分类数据失败:', error)\n        this.categoryOptions = []\n      })\n    },\n\n    /** 构建分类树 */\n    buildCategoryTree(categories) {\n      const map = {}\n\n      // 先将所有分类放入map中\n      categories.forEach(category => {\n        map[category.id] = { ...category, children: [] }\n      })\n\n      // 构建完整的树形结构\n      const result = []\n      categories.forEach(category => {\n        if (category.parentId === 0) {\n          // 顶级分类\n          result.push(map[category.id])\n        } else {\n          // 子分类\n          if (map[category.parentId]) {\n            map[category.parentId].children.push(map[category.id])\n          }\n        }\n      })\n\n      // 清理空的children数组\n      const cleanEmptyChildren = (node) => {\n        if (node.children && node.children.length === 0) {\n          delete node.children\n        } else if (node.children && node.children.length > 0) {\n          node.children.forEach(child => cleanEmptyChildren(child))\n        }\n      }\n\n      // 清理所有节点的空children\n      result.forEach(node => cleanEmptyChildren(node))\n\n      return result\n    },\n\n    /** 获取所有子分类ID */\n    getAllChildCategoryIds(categoryId, categories) {\n      const result = [categoryId]\n\n      const findChildren = (parentId) => {\n        categories.forEach(category => {\n          if (category.parentId === parentId) {\n            result.push(category.id)\n            findChildren(category.id)\n          }\n        })\n      }\n\n      findChildren(categoryId)\n      return result\n    },\n\n    /** 加载手动选题的题库列表 */\n    loadManualSelectQuestionBanks() {\n      let categoryIds = []\n\n      // 如果选择了分类，获取该分类及其所有子分类的ID\n      if (this.manualSelect.selectedCategory) {\n        // 获取原始分类数据（包含所有层级）\n        listCategory({ pageSize: 1000 }).then(response => {\n          const allCategories = response.rows || []\n          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)\n\n          // 执行多次查询，因为后端不支持IN查询\n          this.loadQuestionBanksByCategories(categoryIds)\n        }).catch(error => {\n          console.error('加载分类数据失败:', error)\n          this.loadQuestionBanksByCategories([])\n        })\n      } else {\n        // 没有选择分类，加载所有题库\n        this.loadQuestionBanksByCategories([])\n      }\n    },\n\n    /** 根据分类ID列表加载题库 */\n    loadQuestionBanksByCategories(categoryIds) {\n      const queryParams = {\n        pageNum: this.manualSelect.bankPagination.pageNum,\n        pageSize: this.manualSelect.bankPagination.pageSize,\n        bankName: this.manualSelect.bankSearchKeyword || undefined\n      }\n\n      if (categoryIds.length > 0) {\n        // 如果有分类筛选，需要合并多个分类的结果\n        const promises = categoryIds.map(categoryId => {\n          return listQuestionBank({ ...queryParams, categoryId })\n        })\n\n        Promise.all(promises).then(responses => {\n          const allBanks = []\n          let totalCount = 0\n\n          responses.forEach(response => {\n            if (response.rows) {\n              allBanks.push(...response.rows)\n              totalCount += response.total || 0\n            }\n          })\n\n          // 去重（根据bankId）\n          const uniqueBanks = allBanks.filter((bank, index, self) =>\n            index === self.findIndex(b => b.bankId === bank.bankId)\n          )\n\n          this.manualSelect.questionBanks = uniqueBanks\n          this.manualSelect.bankPagination.total = uniqueBanks.length\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      } else {\n        // 没有分类筛选，直接查询\n        listQuestionBank(queryParams).then(response => {\n          this.manualSelect.questionBanks = response.rows || []\n          this.manualSelect.bankPagination.total = response.total || 0\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      }\n    },\n\n    /** 搜索题库 */\n    searchQuestionBanks() {\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 选择题库 */\n    selectQuestionBank(row) {\n      this.manualSelect.selectedBankId = row.bankId\n      this.loadQuestions()\n    },\n\n    /** 加载题目列表 */\n    loadQuestions() {\n      if (!this.manualSelect.selectedBankId) {\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n        return\n      }\n\n      const queryParams = {\n        pageNum: this.manualSelect.questionPagination.pageNum,\n        pageSize: this.manualSelect.questionPagination.pageSize,\n        bankId: this.manualSelect.selectedBankId,\n        questionType: this.manualSelect.questionType || undefined,\n        difficulty: this.manualSelect.difficulty || undefined,\n        questionContent: this.manualSelect.questionSearchKeyword || undefined\n      }\n\n      listQuestion(queryParams).then(response => {\n        this.manualSelect.questions = response.rows || []\n        this.manualSelect.questionPagination.total = response.total || 0\n      }).catch(error => {\n        console.error('加载题目列表失败:', error)\n        this.$message.error('加载题目列表失败')\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n      })\n    },\n\n    /** 搜索题目 */\n    searchQuestions() {\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 重置题目搜索 */\n    resetQuestionSearch() {\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.searchQuestions()\n    },\n\n    /** 随机抽题 */\n    handleRandomSelect() {\n      // 为固定试卷打开随机规则设置对话框\n      this.showFixedRandomDialog = true\n      this.resetFixedRandomForm()\n      this.loadQuestionBanks()\n    },\n\n    /** 重置固定试卷随机抽题表单 */\n    resetFixedRandomForm() {\n      this.fixedRandomRules = []\n      this.fixedRandomForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    handleAddFixedRandomRule() {\n      this.currentOperation = 'addFixedRandom'\n      this.editingRule = null\n      this.ruleForm.ruleType = 3 // 固定为题库类型\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    handleAddFixedRandomSubRule(parentRule) {\n      this.currentOperation = 'addFixedRandomSub'\n      this.editingRule = null\n      this.parentRule = parentRule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n      if (parentRule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 更新固定试卷随机抽题规则分数 */\n    updateFixedRandomRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 删除固定试卷随机抽题规则 */\n    deleteFixedRandomRule(rule) {\n      // 如果是父规则，需要同时删除所有子规则\n      if (rule.children && rule.children.length > 0) {\n        // 删除所有子规则\n        rule.children.forEach(childRule => {\n          const childIndex = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n          if (childIndex > -1) {\n            this.fixedRandomRules.splice(childIndex, 1)\n          }\n        })\n      }\n\n      // 删除父规则\n      const index = this.fixedRandomRules.findIndex(r => r.id === rule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 删除固定试卷随机抽题子规则 */\n    deleteFixedRandomChildRule(parentRule, childRule) {\n      // 从父规则的children中删除\n      if (parentRule.children) {\n        const childIndex = parentRule.children.findIndex(r => r.id === childRule.id)\n        if (childIndex > -1) {\n          parentRule.children.splice(childIndex, 1)\n        }\n      }\n\n      // 从主规则列表中删除\n      const index = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 确认固定试卷随机抽题 */\n    async handleConfirmFixedRandomSelect() {\n      if (this.fixedRandomRules.length === 0) {\n        this.$message.warning('请先添加抽题规则')\n        return\n      }\n\n      try {\n        // 根据规则随机抽取题目\n        const selectedQuestions = await this.extractQuestionsFromRules(this.fixedRandomRules)\n\n        if (selectedQuestions.length === 0) {\n          this.$message.warning('根据当前规则未能抽取到题目')\n          return\n        }\n\n        // 将抽取的题目添加到固定试卷中\n        selectedQuestions.forEach((question, index) => {\n          this.fixedQuestions.push({\n            id: Date.now() + index,\n            questionId: question.questionId,\n            content: question.questionContent,\n            type: question.questionType,\n            difficulty: question.difficulty,\n            options: question.options,\n            score: question.score || 1, // 使用规则中设置的分数\n            selected: false,\n            expanded: false\n          })\n        })\n\n        this.showFixedRandomDialog = false\n        this.$message.success(`成功随机抽取并添加 ${selectedQuestions.length} 道题目`)\n      } catch (error) {\n        console.error('随机抽题失败:', error)\n        this.$message.error('随机抽题失败，请重试')\n      }\n    },\n\n    /** 根据规则抽取题目 */\n    async extractQuestionsFromRules(rules) {\n      const allQuestions = []\n\n      // 处理父规则（题库规则）\n      const parentRules = rules.filter(rule => !rule.parentId)\n\n      for (const parentRule of parentRules) {\n        try {\n          // 获取子规则\n          const childRules = rules.filter(rule => rule.parentId === parentRule.id)\n\n          if (childRules.length > 0) {\n            // 有子规则：按子规则的题型从父规则的题库中抽取\n            for (const childRule of childRules) {\n              let questions = []\n\n              // 从父规则的题库中按子规则的题型抽取题目\n              for (const bank of parentRule.selectedBanks) {\n                for (const questionType of childRule.selectedQuestionTypes) {\n                  const queryParams = {\n                    bankId: bank.bankId,\n                    questionType: questionType,\n                    pageNum: 1,\n                    pageSize: 1000\n                  }\n\n                  const response = await listQuestion(queryParams)\n                  if (response.rows && response.rows.length > 0) {\n                    questions = questions.concat(response.rows)\n                  }\n                }\n              }\n\n              // 随机选择指定数量的题目\n              if (questions.length > 0) {\n                const shuffled = this.shuffleArray([...questions])\n                const selectedCount = Math.min(childRule.selectedCount, shuffled.length)\n                const selectedQuestions = shuffled.slice(0, selectedCount)\n\n                // 为每个题目设置分数\n                selectedQuestions.forEach(question => {\n                  question.score = childRule.scorePerQuestion\n                })\n\n                allQuestions.push(...selectedQuestions)\n              }\n            }\n          } else {\n            // 没有子规则：直接从父规则的题库中抽取题目\n            let questions = []\n\n            for (const bank of parentRule.selectedBanks) {\n              const queryParams = {\n                bankId: bank.bankId,\n                pageNum: 1,\n                pageSize: 1000\n              }\n\n              const response = await listQuestion(queryParams)\n              if (response.rows && response.rows.length > 0) {\n                questions = questions.concat(response.rows)\n              }\n            }\n\n            // 随机选择指定数量的题目\n            if (questions.length > 0) {\n              const shuffled = this.shuffleArray([...questions])\n              const selectedCount = Math.min(parentRule.selectedCount, shuffled.length)\n              const selectedQuestions = shuffled.slice(0, selectedCount)\n\n              // 为每个题目设置分数\n              selectedQuestions.forEach(question => {\n                question.score = parentRule.scorePerQuestion\n              })\n\n              allQuestions.push(...selectedQuestions)\n            }\n          }\n        } catch (error) {\n          console.error('抽取题目失败:', error)\n        }\n      }\n\n      return allQuestions\n    },\n\n    /** 数组随机排序 */\n    shuffleArray(array) {\n      for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]]\n      }\n      return array\n    },\n\n    /** 排序 */\n    handleSort() {\n      this.$message.info('排序功能开发中...')\n    },\n\n    /** 批量设置分数 */\n    handleBatchSetScore() {\n      if (this.fixedQuestions.length === 0) {\n        this.$message.warning('暂无题目，无法设置分数')\n        return\n      }\n\n      // 重置表单\n      this.batchScoreForm = {\n        scoreType: 'all',\n        score: 1,\n        questionType: ''\n      }\n\n      this.showBatchScoreDialog = true\n    },\n\n    /** 获取批量设置分数影响的题目数量 */\n    getBatchScoreAffectedCount() {\n      if (this.fixedQuestions.length === 0) {\n        return 0\n      }\n\n      switch (this.batchScoreForm.scoreType) {\n        case 'all':\n          return this.fixedQuestions.length\n        case 'selected':\n          return this.fixedQuestions.filter(q => q.selected).length\n        case 'byType':\n          if (!this.batchScoreForm.questionType) {\n            return 0\n          }\n          return this.fixedQuestions.filter(q => q.type == this.batchScoreForm.questionType).length\n        default:\n          return 0\n      }\n    },\n\n    /** 确认批量设置分数 */\n    handleConfirmBatchSetScore() {\n      const affectedCount = this.getBatchScoreAffectedCount()\n\n      if (affectedCount === 0) {\n        this.$message.warning('没有符合条件的题目')\n        return\n      }\n\n      let targetQuestions = []\n\n      switch (this.batchScoreForm.scoreType) {\n        case 'all':\n          targetQuestions = this.fixedQuestions\n          break\n        case 'selected':\n          targetQuestions = this.fixedQuestions.filter(q => q.selected)\n          if (targetQuestions.length === 0) {\n            this.$message.warning('请先选择要设置分数的题目')\n            return\n          }\n          break\n        case 'byType':\n          if (!this.batchScoreForm.questionType) {\n            this.$message.warning('请选择题型')\n            return\n          }\n          targetQuestions = this.fixedQuestions.filter(q => q.type == this.batchScoreForm.questionType)\n          break\n      }\n\n      // 批量设置分数\n      targetQuestions.forEach(question => {\n        question.score = this.batchScoreForm.score\n      })\n\n      this.showBatchScoreDialog = false\n      this.$message.success(`成功为 ${affectedCount} 道题目设置分数为 ${this.batchScoreForm.score} 分`)\n    },\n\n    /** 导出 */\n    handleExport() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    /** 展开/收起 */\n    handleToggleExpand() {\n      this.isExpanded = !this.isExpanded\n      // 批量设置所有题目的展开状态\n      this.fixedQuestions.forEach(question => {\n        question.expanded = this.isExpanded\n      })\n      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)\n    },\n\n    /** 题目选择变化 */\n    handleQuestionSelectionChange(selection) {\n      this.manualSelect.selectedQuestions = selection\n      this.updateSelectedStats()\n    },\n\n    /** 更新选中题目统计 */\n    updateSelectedStats() {\n      const stats = {}\n      this.manualSelect.selectedQuestions.forEach(question => {\n        const type = question.questionType\n        stats[type] = (stats[type] || 0) + 1\n      })\n      this.manualSelect.selectedStats = stats\n    },\n\n    /** 确认手动选题 */\n    confirmManualSelect() {\n      if (this.manualSelect.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择题目')\n        return\n      }\n\n      // 将选中的题目添加到固定试卷中\n      this.manualSelect.selectedQuestions.forEach((question, index) => {\n        this.fixedQuestions.push({\n          id: Date.now() + index,\n          questionId: question.questionId,\n          content: question.questionContent,\n          type: question.questionType,\n          difficulty: question.difficulty,\n          options: question.options, // 保存选项信息\n          score: 1, // 默认1分\n          selected: false,\n          expanded: false // 默认收起状态\n        })\n      })\n\n      this.showManualSelectDialog = false\n      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)\n    },\n\n    /** 题库分页大小变化 */\n    handleBankSizeChange(val) {\n      this.manualSelect.bankPagination.pageSize = val\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题库当前页变化 */\n    handleBankCurrentChange(val) {\n      this.manualSelect.bankPagination.pageNum = val\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题目分页大小变化 */\n    handleQuestionSizeChange(val) {\n      this.manualSelect.questionPagination.pageSize = val\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 题目当前页变化 */\n    handleQuestionCurrentChange(val) {\n      this.manualSelect.questionPagination.pageNum = val\n      this.loadQuestions()\n    },\n\n    /** 获取难度文本 */\n    getDifficultyText(difficulty) {\n      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }\n      return difficultyMap[difficulty] || '未知'\n    },\n\n    /** 更新题目分数 */\n    updateQuestionScore(question) {\n      // 分数更新后可以触发总分重新计算\n      this.$forceUpdate()\n    },\n\n    /** 切换题目展开/收起状态 */\n    toggleQuestionExpand(question) {\n      question.expanded = !question.expanded\n    },\n\n    /** 删除单个题目 */\n    deleteQuestion(question, index) {\n      this.$confirm('确定删除这道题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions.splice(index, 1)\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 解析选项JSON字符串 */\n    parseOptions(optionsStr) {\n      try {\n        if (typeof optionsStr === 'string') {\n          return JSON.parse(optionsStr)\n        }\n        return optionsStr || []\n      } catch (error) {\n        console.error('解析选项失败:', error)\n        return []\n      }\n    },\n\n    /** 判断题目是否可选择 */\n    isQuestionSelectable(row) {\n      // 检查题目是否已经添加到试卷中\n      return !this.fixedQuestions.some(question => question.questionId === row.questionId)\n    },\n\n    /** 获取题目行的样式类名 */\n    getQuestionRowClassName({ row }) {\n      // 如果题目已被添加，添加禁用样式\n      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {\n        return 'disabled-question-row'\n      }\n      return ''\n    },\n    \n    /** 上传前验证 */\n    beforeUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0\n      const isLt2M = file.size / 1024 / 1024 < 2\n      \n      if (!isImage) {\n        this.$message.error('只能上传图片文件!')\n        return false\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!')\n        return false\n      }\n      return true\n    },\n    \n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    \n    /** 加载试卷数据 */\n    loadPaperData() {\n      if (this.paperId) {\n        // TODO: 调用API加载试卷数据\n        console.log('加载试卷数据:', this.paperId)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.exam-editor {\n  display: flex;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.exam-editor-left {\n  flex: 1;\n  padding: 20px;\n  padding-right: 420px; /* 为右侧固定面板留出空间 */\n  overflow-y: auto;\n  transition: padding-right 0.3s ease;\n  background-color: #FFF;\n}\n\n.exam-editor-left.collapsed {\n  padding-right: 70px; /* 右侧面板收起时的空间 */\n}\n\n.exam-editor-right {\n  background: #fff;\n  border-left: 1px solid #e4e7ed;\n  position: fixed;\n  right: 0;\n  top: 0;\n  height: 100vh;\n  transition: width 0.3s ease;\n  z-index: 100;\n}\n\n.collapse-button {\n  position: absolute;\n  left: -15px;\n  top: 20px;\n  width: 30px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e4e7ed;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  font-size: 16px;\n  color: #606266;\n}\n\n.collapse-button:hover {\n  background: #f5f7fa;\n  color: #409eff;\n}\n\n.editPaper_main_top {\n  background: #fff;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.editPaper_main_top .el-button-group {\n  margin-right: 15px;\n  display: inline-block;\n}\n\n.clear_both {\n  clear: both;\n}\n\n.subject_main-wrapper {\n  max-width: 100%;\n}\n\n/* 左侧卡片模块悬停效果 */\n.subject_main-wrapper .el-card {\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.subject_main-wrapper .el-card:hover {\n  border: 2px dashed #409eff;\n  background-color: #fafbff;\n}\n\n.subtitle-title {\n  position: relative;\n}\n\n.slgfont {\n  font-size: 24px;\n  color: #303133;\n}\n\n.paper-count {\n  margin-top: 10px;\n}\n\n.paper-count .el-tag {\n  margin-left: 8px;\n}\n\n\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.tac {\n  text-align: center;\n}\n\n.pd5 {\n  padding: 5px;\n}\n\n.editor-header {\n  padding: 20px;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.editor-header--big {\n  font-size: 20px;\n}\n\n.close-button {\n  font-size: 20px;\n  color: #909399;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  color: #f56c6c;\n  background-color: #fef0f0;\n}\n\n.main {\n  height: calc(100vh - 80px);\n  overflow-y: auto;\n  padding: 0;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.main-collapse {\n  border: none;\n}\n\n.main-collapse .el-collapse-item__header {\n  padding: 0 20px;\n  height: 60px;\n  line-height: 60px;\n  background: #f8f8f8;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* 使用深度选择器覆盖Element UI默认样式 */\n.main-collapse >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n.editor-collapse-item >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n/* Vue 3 深度选择器语法 */\n.main-collapse :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.editor-collapse-item :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.main-module {\n  padding: 20px;\n}\n\n.setting-block {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.setting-block:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n\n/* 二级设置块容器 */\n.block-expand {\n  margin-top: 10px;\n  padding-left: 20px;\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 二级设置块样式 */\n.sub-setting {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #e8e8e8;\n}\n\n.sub-setting:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n}\n\n.line {\n  display: flex;\n  align-items: flex-start;\n}\n\n.block-header {\n  justify-content: space-between;\n}\n\n.block-header b {\n  font-size: 14px;\n  color: #303133;\n  min-width: 93px;\n  line-height: 32px;\n}\n\n.input-area {\n  flex: 1;\n  margin-left: 20px;\n}\n\n.input--number {\n  width: 120px;\n}\n\n.dpib {\n  display: inline-block;\n}\n\n.avatar-uploader {\n  border: none !important;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 120px;\n  height: 80px;\n  box-sizing: border-box;\n}\n\n\n\n/* 使用伪元素创建统一的虚线边框 */\n.avatar-uploader::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.avatar-uploader:hover::before {\n  border-color: #409eff;\n}\n\n/* 确保Element UI组件没有边框 */\n.avatar-uploader .el-upload {\n  border: none !important;\n  width: 100%;\n  height: 100%;\n  background: transparent !important;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 120px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n}\n\n.avatar {\n  width: 120px;\n  height: 80px;\n  display: block;\n  object-fit: cover;\n}\n\n.image_area {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bold {\n  font-weight: bold;\n}\n\n.mr10 {\n  margin-right: 10px;\n}\n\n/* 折叠面板标题样式 */\n.collapse-title {\n  margin-left: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.collapse-title i {\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.el-popover__reference {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n}\n\n.el-popover__title {\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n/* 设置标题容器样式 */\n.setting-title {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n}\n\n.setting-title b {\n  font-size: 14px;\n  color: #303133;\n}\n\n/* 出题方式问号图标样式 */\n.paper-type-question {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n  position: relative;\n  z-index: 10;\n  font-size: 14px;\n  line-height: 1;\n}\n\n.paper-type-question:hover {\n  color: #409eff;\n}\n\n/* 出题方式提示框样式 */\n.paper-type-tooltip {\n  max-width: 320px !important;\n  z-index: 2000 !important;\n}\n\n/* 迟到限制提示框样式 */\n.late-limit-tooltip {\n  max-width: 280px !important;\n  z-index: 2000 !important;\n}\n\n/* 随机规则设置对话框样式 */\n.cascade-random-rules {\n  min-height: 400px;\n}\n\n/* 确保对话框层级正确 */\n::v-deep .el-dialog__wrapper {\n  z-index: 3000 !important;\n}\n\n::v-deep .el-dialog {\n  z-index: 3001 !important;\n  position: relative !important;\n}\n\n::v-deep .el-overlay {\n  z-index: 2999 !important;\n}\n\n.topbar {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.topbar .fl {\n  float: left;\n}\n\n.topbar .summary {\n  margin-left: 15px;\n  color: #666;\n  font-size: 14px;\n}\n\n.topbar .total_score {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.guide-steps-list {\n  margin: 30px 0;\n  padding: 0 20px;\n}\n\n/* 步骤组件样式优化 */\n::v-deep .guide-steps-list .el-step {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__head {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__main {\n  text-align: center;\n  margin-top: 10px;\n}\n\n::v-deep .guide-steps-list .el-step__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n::v-deep .guide-steps-list .el-step__description {\n  margin-top: 8px;\n  padding: 0 10px;\n}\n\n.step-content {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 5px;\n}\n\n.rules-content {\n  min-height: 200px;\n  margin: 20px 0;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 60px 0;\n  color: #999;\n  font-size: 14px;\n}\n\n.bottom-panel {\n  text-align: center;\n  padding: 20px 0;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n/* 强制覆盖Element UI折叠面板背景色 */\n.el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n  font-size: 16px !important;\n  font-weight: bold !important;\n}\n\n.el-collapse-item__header.is-active {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 使用属性选择器强制覆盖 */\n[class*=\"el-collapse-item__header\"] {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 深度选择器 */\n.exam-editor >>> .el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .exam-editor-left {\n    padding-right: 370px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 70px;\n  }\n}\n\n@media (max-width: 768px) {\n  .exam-editor {\n    flex-direction: column;\n  }\n\n  .exam-editor-left {\n    padding-right: 20px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 20px;\n  }\n\n  .exam-editor-right {\n    position: relative !important;\n    width: 100% !important;\n    height: 50vh;\n    border-left: none;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .collapse-button {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg3DA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;MACA;MACAM,mBAAA;MACAC,eAAA;MAEA;MACAC,cAAA;MAEA;MACAC,SAAA;QACAR,OAAA;QACAS,SAAA;QACAC,SAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,SAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QAAA;QACAC,MAAA;QAAA;QACAC,eAAA;QAAA;QACAC,eAAA;QAAA;QACAC,UAAA;MACA;MAEA;;MAEA;MACAC,cAAA;MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;;MAEA;MACAC,sBAAA;MACAC,eAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACAC,YAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;UACAC,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAC,cAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,qBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,kBAAA;UACAR,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAO,iBAAA;QAAA;QACAC,aAAA;MACA;MAEA;MACAC,qBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MAEA;MACAC,oBAAA;MACAC,cAAA;QACAC,SAAA;QAAA;QACAC,KAAA;QAAA;QACAjB,YAAA;MACA;MAEA;MACAkB,cAAA;MACA;MACAC,KAAA;MAEA;MACAC,iBAAA;MACAC,QAAA;QACAX,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACAS,gBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,wBAAA;MAAA;MACA/B,aAAA;MACAgC,mBAAA;IAAA,sBACA,sBACA,oBACA,gBACA,cACA,mBACA;MACA9B,OAAA;MACAC,QAAA;MACA8B,QAAA,EAAAC;IACA;EAEA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,YAAArC,aAAA,CAAAsC,MAAA,WAAAC,IAAA;QAAA,OACAF,KAAA,CAAAV,QAAA,CAAAT,aAAA,CAAAsB,QAAA,CAAAD,IAAA,CAAAE,MAAA;MAAA,CACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAtC,KAAA;MACA,KAAAqB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAlD,QAAA,IAAAkD,IAAA,CAAAlD,QAAA,CAAAmD,MAAA;UACA;UACAD,IAAA,CAAAlD,QAAA,CAAAiD,OAAA,WAAAG,KAAA;YACA1C,KAAA,IAAA0C,KAAA,CAAAC,aAAA;UACA;QACA;UACA;UACA3C,KAAA,IAAAwC,IAAA,CAAAG,aAAA;QACA;MACA;MACA,OAAA3C,KAAA;IACA;IAEA;IACA4C,cAAA,WAAAA,eAAA;MACA,IAAA5C,KAAA;MACA,KAAAqB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAlD,QAAA,IAAAkD,IAAA,CAAAlD,QAAA,CAAAmD,MAAA;UACA;UACAD,IAAA,CAAAlD,QAAA,CAAAiD,OAAA,WAAAG,KAAA;YACA1C,KAAA,IAAA0C,KAAA,CAAA/E,UAAA;UACA;QACA;UACA;UACAqC,KAAA,IAAAwC,IAAA,CAAA7E,UAAA;QACA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACA6C,WAAA,WAAAA,YAAA;MACA,YAAAxB,KAAA,CAAAa,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,OAAA,OAAAC,GAAA;MACA,KAAA5B,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA7F,IAAA,UAAA6F,IAAA,CAAA1B,aAAA;UACA0B,IAAA,CAAA1B,aAAA,CAAAyB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAA;MACA,IAAAC,KAAA;MACA,KAAAxE,cAAA,CAAAyD,OAAA,WAAAgB,QAAA;QACA,IAAA5G,IAAA,GAAA4G,QAAA,CAAA5G,IAAA;QACA2G,KAAA,CAAA3G,IAAA,KAAA2G,KAAA,CAAA3G,IAAA;MACA;MACA,OAAA2G,KAAA;IACA;IAEA;IACAE,yBAAA,WAAAA,0BAAA;MACA,IAAAxD,KAAA;MACA,KAAAU,gBAAA,CAAA6B,OAAA,WAAAC,IAAA;QACAxC,KAAA,IAAAwC,IAAA,CAAAG,aAAA;MACA;MACA,OAAA3C,KAAA;IACA;IAEA;IACAyD,qBAAA,WAAAA,sBAAA;MACA,IAAAzD,KAAA;MACA,KAAAU,gBAAA,CAAA6B,OAAA,WAAAC,IAAA;QACAxC,KAAA,IAAAwC,IAAA,CAAA7E,UAAA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACA0D,sBAAA,WAAAA,uBAAA;MACA,YAAAhD,gBAAA,CAAAwB,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAa,sBAAA,WAAAA,uBAAA;MACA,IAAAX,OAAA,OAAAC,GAAA;MACA,KAAAvC,gBAAA,CAAA6B,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA7F,IAAA,UAAA6F,IAAA,CAAA1B,aAAA;UACA0B,IAAA,CAAA1B,aAAA,CAAAyB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;EACA;EACAY,KAAA;IACAlH,OAAA,WAAAA,QAAAmH,GAAA;MACA,IAAAA,GAAA,SAAA/G,OAAA;QACA,KAAAgH,aAAA;MACA;IACA;IAEA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QAAA,WAAAC,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAAC,QAAA;UAAA,WAAAF,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBAAA,MACAP,MAAA,CAAA1C,QAAA,CAAAX,QAAA,UAAAoD,MAAA,IAAAA,MAAA,CAAAvB,MAAA;kBAAA8B,QAAA,CAAAC,CAAA;kBAAA;gBAAA;gBAAAD,QAAA,CAAAC,CAAA;gBAAA,OACAP,MAAA,CAAAQ,uBAAA;cAAA;gBAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;UAAA,GAAAL,OAAA;QAAA;MAEA;MACAM,IAAA;IACA;EACA;EACAC,OAAA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA;IACA;IAEA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA5H,mBAAA,SAAAA,mBAAA;MACA,KAAAC,eAAA,QAAAD,mBAAA;IACA;IAEA,WACA6H,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,QAAA,CAAAC,IAAA;IACA;IAEA,WACAE,aAAA,WAAAA,cAAA;MACA,KAAAH,QAAA,CAAAC,IAAA;IACA;IAEA,WACAG,oBAAA,WAAAA,qBAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;IACA;IAEA,YACAI,gBAAA,WAAAA,iBAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;IAIA,iBACAK,aAAA,WAAAA,cAAA;MACA,KAAAnE,cAAA;IACA;IAEA,qBACAoE,eAAA,WAAAA,gBAAA;MACA,KAAApE,cAAA;IACA;IAEA,aACAqE,kBAAA,WAAAA,mBAAA;MACA,KAAAjE,gBAAA;MACA,KAAAC,WAAA;;MAEA;MACA,SAAAJ,KAAA,CAAAoB,MAAA;QACA,KAAAlB,QAAA,CAAAX,QAAA,QAAAS,KAAA,IAAA1E,IAAA;MACA;QACA,KAAA4E,QAAA,CAAAX,QAAA;MACA;MAEA,KAAAU,iBAAA;MACA;MACA,KAAAoE,WAAA;QACA5F,OAAA;QACAC,QAAA;QACA8B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjE,mBAAA;MACA;MACAkE,OAAA,CAAAC,GAAA,EACA,IAAAC,8BAAA,OAAAN,WAAA,GACA,IAAAO,sBAAA;QAAAlG,QAAA;MAAA,GACA,EAAAmG,IAAA,WAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAxJ,OAAA,EAAAsJ,KAAA;UAAAG,YAAA,GAAAF,KAAA;UAAAG,gBAAA,GAAAH,KAAA;QACA,IAAAxG,aAAA,GAAA0G,YAAA,CAAAE,IAAA;QACAX,MAAA,CAAA7F,KAAA,GAAAsG,YAAA,CAAAtG,KAAA;QACA6F,MAAA,CAAA3G,eAAA,GAAAqH,gBAAA,CAAAC,IAAA,IAAAD,gBAAA,CAAAtJ,IAAA;;QAEA;QACA,IAAAwJ,kBAAA,GAAA7G,aAAA,CAAA8G,GAAA,WAAAvE,IAAA;UAAA,OACA,IAAAwE,+BAAA,EAAAxE,IAAA,CAAAE,MAAA,EAAA6D,IAAA,WAAA5C,KAAA;YACAnB,IAAA,CAAAyE,aAAA,GAAAtD,KAAA,CAAArG,IAAA,GAAAqG,KAAA,CAAArG,IAAA,CAAA4J,UAAA,IAAAvD,KAAA,CAAArG,IAAA,CAAA+C,KAAA;YACA,OAAAmC,IAAA;UACA,GAAA2E,KAAA;YACA3E,IAAA,CAAAyE,aAAA;YACA,OAAAzE,IAAA;UACA;QAAA,CACA;QAEA2D,OAAA,CAAAC,GAAA,CAAAU,kBAAA,EAAAP,IAAA,WAAAa,cAAA;UACAlB,MAAA,CAAAjG,aAAA,GAAAmH,cAAA;UACAlB,MAAA,CAAAjE,mBAAA;QACA;MACA,GAAAkF,KAAA;QACAjB,MAAA,CAAAjE,mBAAA;MACA;IACA;IAEA,WACAoF,eAAA,WAAAA,gBAAA;MACA,KAAA/B,QAAA,CAAAC,IAAA;MACA,KAAA9D,cAAA;IACA;IAEA,aACA6F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhD,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAA+C,SAAA;QAAA,WAAAhD,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAAA,MACA0C,MAAA,CAAA3F,QAAA,CAAAX,QAAA,UAAAsG,MAAA,CAAA3F,QAAA,CAAAT,aAAA,CAAA2B,MAAA;gBAAA2E,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAGAwC,MAAA,CAAA3F,QAAA,CAAAX,QAAA,UAAAsG,MAAA,CAAA3F,QAAA,CAAAR,qBAAA,CAAA0B,MAAA;gBAAA2E,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAKAwC,MAAA,CAAA3F,QAAA,CAAAX,QAAA;gBAAAwG,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA,MACA0C,MAAA,CAAAvF,wBAAA;gBAAAyF,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAKA,IAAAwC,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAI,kBAAA;cACA,WAAAJ,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAK,kBAAA;cACA,WAAAL,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAM,qBAAA;cACA;gBACA;gBACAN,MAAA,CAAAO,UAAA;cACA;cAEAP,MAAA,CAAAjC,QAAA,CAAAyC,OAAA,CAAAR,MAAA,CAAA1F,gBAAA;cACA0F,MAAA,CAAA5F,iBAAA;;cAEA;cACA4F,MAAA,CAAAS,aAAA;YAAA;cAAA,OAAAP,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,IAAA9E,IAAA,QAAAf,WAAA;MACAe,IAAA,CAAA7F,IAAA,QAAA4E,QAAA,CAAAX,QAAA;MACA4B,IAAA,CAAA3B,YAAA,QAAAU,QAAA,CAAAV,YAAA;MAEA,SAAAU,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAA1B,aAAA,OAAA8G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAT,aAAA;QACA0B,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;;QAEA;QACA,IAAApE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,YAAA;UACArF,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,YAAA;QACA;QACArF,IAAA,CAAA7E,UAAA,GAAA6E,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;MACA,gBAAAzG,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAAzB,qBAAA,OAAA6G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAR,qBAAA;QACAyB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;IACA;IAEA,YACA8F,UAAA,WAAAA,WAAA;MAAA,IAAAQ,MAAA;MACA;MACA,SAAAzG,gBAAA,sBAAAE,UAAA;QACA,KAAAwG,aAAA;QACA;MACA;;MAEA;MACA,SAAA3G,QAAA,CAAAX,QAAA,eAAAW,QAAA,CAAAV,YAAA,uBAAAmB,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAAgG,KAAA;UACA,IAAA3F,IAAA;YACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YAAA;YACAxL,IAAA,EAAAsL,MAAA,CAAA1G,QAAA,CAAAX,QAAA;YACAC,YAAA,EAAAoH,MAAA,CAAA1G,QAAA,CAAAV,YAAA;YACA8B,aAAA;YACAqF,gBAAA;YACArK,UAAA;YACAmD,aAAA,GAAAqB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA+E,aAAA,EAAAzE,IAAA,CAAAyE;YACA;YACAiB,YAAA,EAAA1F,IAAA,CAAAyE,aAAA;UACA;UACAqB,MAAA,CAAAM,eAAA,CAAA/F,IAAA;UACAyF,MAAA,CAAA5G,KAAA,CAAAmH,IAAA,CAAAhG,IAAA;QACA;MACA;QACA;QACA,KAAA0F,aAAA;MACA;IACA;IAEA,mBACAX,kBAAA,WAAAA,mBAAA;MAAA,IAAAkB,MAAA;MACA;MACA,SAAAlH,QAAA,CAAAX,QAAA,eAAAW,QAAA,CAAAV,YAAA,uBAAAmB,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAAgG,KAAA;UACA,IAAA3F,IAAA;YACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YACAxL,IAAA,EAAA8L,MAAA,CAAAlH,QAAA,CAAAX,QAAA;YACAC,YAAA,EAAA4H,MAAA,CAAAlH,QAAA,CAAAV,YAAA;YACA8B,aAAA;YACAqF,gBAAA;YACArK,UAAA;YACAmD,aAAA,GAAAqB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA+E,aAAA,EAAAzE,IAAA,CAAAyE;YACA;YACAiB,YAAA,EAAA1F,IAAA,CAAAyE,aAAA;UACA;UACA6B,MAAA,CAAAC,0BAAA,CAAAlG,IAAA;UACAiG,MAAA,CAAA/H,gBAAA,CAAA8H,IAAA,CAAAhG,IAAA;QACA;MACA;QACA;QACA,KAAAmG,wBAAA;MACA;IACA;IAEA,qBACAA,wBAAA,WAAAA,yBAAA;MACA,IAAAnG,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QACA3L,IAAA,OAAA4E,QAAA,CAAAX,QAAA;QACAC,YAAA,OAAAU,QAAA,CAAAV,YAAA;QACA8B,aAAA;QACAqF,gBAAA;QACArK,UAAA;QACAkK,YAAA;MACA;MAEA,SAAAtG,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAA1B,aAAA,OAAA8G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAT,aAAA;QACA0B,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA,gBAAArF,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAAzB,qBAAA,OAAA6G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAR,qBAAA;QACAyB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;MAEA,KAAAjB,gBAAA,CAAA8H,IAAA,CAAAhG,IAAA;MACA,KAAAkG,0BAAA,CAAAlG,IAAA;IACA;IAEA,oBACAgF,qBAAA,WAAAA,sBAAA;MACA,IAAAhF,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QACA3L,IAAA,OAAA4E,QAAA,CAAAX,QAAA;QACAkC,QAAA,OAAApB,UAAA,CAAA0G,EAAA;QACAzF,aAAA;QACAqF,gBAAA;QACArK,UAAA;QACAkK,YAAA;MACA;MAEA,SAAAtG,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAAzB,qBAAA,OAAA6G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAR,qBAAA;QACAyB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA,gBAAAJ,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAA1B,aAAA,OAAA8G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAT,aAAA;QACA0B,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA;;MAEA;MACA,UAAAlF,UAAA,CAAApC,QAAA;QACA,KAAAsJ,IAAA,MAAAlH,UAAA;MACA;;MAEA;MACA,KAAAA,UAAA,CAAApC,QAAA,CAAAkJ,IAAA,CAAAhG,IAAA;;MAEA;MACA,KAAA9B,gBAAA,CAAA8H,IAAA,CAAAhG,IAAA;MACA,KAAAkG,0BAAA,CAAAlG,IAAA;IACA;IAEA,aACA0F,aAAA,WAAAA,cAAA;MACA,IAAA1F,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;QACA3L,IAAA,OAAA4E,QAAA,CAAAX,QAAA;QACAC,YAAA,OAAAU,QAAA,CAAAV,YAAA;QACA8B,aAAA;QAAA;QACAqF,gBAAA;QAAA;QACArK,UAAA;QAAA;QACAkK,YAAA;MACA;;MAEA;MACA,SAAArG,gBAAA,sBAAAE,UAAA;QACAc,IAAA,CAAAM,QAAA,QAAApB,UAAA,CAAA0G,EAAA;;QAEA;QACA,UAAA1G,UAAA,CAAApC,QAAA;UACA,KAAAsJ,IAAA,MAAAlH,UAAA;QACA;;QAEA;QACA,KAAAA,UAAA,CAAApC,QAAA,CAAAkJ,IAAA,CAAAhG,IAAA;MACA;MAEA,SAAAjB,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAA1B,aAAA,OAAA8G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAT,aAAA;QACA0B,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA,gBAAArF,QAAA,CAAAX,QAAA;QACA;QACA4B,IAAA,CAAAzB,qBAAA,OAAA6G,mBAAA,CAAA/K,OAAA,OAAA0E,QAAA,CAAAR,qBAAA;QACAyB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;;MAEA;MACA,SAAAH,gBAAA;QACA,KAAAH,KAAA,CAAAmH,IAAA,CAAAhG,IAAA;MACA;;MAEA;MACA,KAAA+F,eAAA,CAAA/F,IAAA;IACA;IAEA,aACAmF,aAAA,WAAAA,cAAA;MACA,KAAApG,QAAA;QACAX,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACA,SAAAS,gBAAA,8BAAAA,gBAAA;QACA,KAAAA,gBAAA;QACA,KAAAC,WAAA;QACA,KAAAC,UAAA;MACA;MACA,KAAAC,wBAAA;IACA;IAEA,WACAkH,YAAA,WAAAA,aAAA;MACA,KAAAnD,WAAA,CAAA5F,OAAA;MACA,KAAA4F,WAAA,CAAA7D,QAAA,QAAA8D,aAAA,IAAA7D,SAAA;MACA,KAAA8D,iBAAA;IACA;IAEA,WACAkD,WAAA,WAAAA,YAAA;MACA,KAAAnD,aAAA;MACA,KAAAD,WAAA,CAAA5F,OAAA;MACA,KAAA4F,WAAA,CAAA7D,QAAA,GAAAC,SAAA;MACA,KAAA8D,iBAAA;IACA;IAEA,aACAmD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzH,QAAA,CAAAT,aAAA,GAAAkI,SAAA,CAAAtC,GAAA,WAAAuC,IAAA;QAAA,OAAAA,IAAA,CAAA5G,MAAA;MAAA;IACA;IAEA,eACA6G,kBAAA,WAAAA,mBAAA7G,MAAA;MAAA,IAAA8G,MAAA;MACA,IAAAhB,KAAA,QAAA5G,QAAA,CAAAT,aAAA,CAAAsI,OAAA,CAAA/G,MAAA;MACA,IAAA8F,KAAA;QACA,KAAA5G,QAAA,CAAAT,aAAA,CAAAuI,MAAA,CAAAlB,KAAA;MACA;;MAEA;MACA,KAAAmB,SAAA;QACA,IAAAC,KAAA,GAAAJ,MAAA,CAAAK,KAAA,CAAAC,iBAAA;QACA,IAAAF,KAAA;UACA;UACA,IAAAG,aAAA,GAAAP,MAAA,CAAAvJ,aAAA,CAAA+J,IAAA,WAAAxH,IAAA;YAAA,OAAAA,IAAA,CAAAE,MAAA,KAAAA,MAAA;UAAA;UACA,IAAAqH,aAAA;YACAH,KAAA,CAAAK,kBAAA,CAAAF,aAAA;UACA;QACA;MACA;IACA;IAEA,WACAG,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAApE,WAAA,CAAA5F,OAAA,GAAAgK,IAAA;MACA,KAAAlE,iBAAA;IACA;IAEA,mBACAmE,eAAA,WAAAA,gBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAC,gBAAA,MAAAhL,eAAA,EAAA8K,UAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAzN,IAAA;IACA;IAEA,4BACA0N,gBAAA,WAAAA,iBAAAC,UAAA,EAAA/B,EAAA;MACA;MACA,IAAAgC,cAAA,QAAAC,iBAAA,CAAAF,UAAA;;MAEA;MACA,IAAAF,QAAA,GAAAG,cAAA,CAAAT,IAAA,WAAAW,GAAA;QAAA,OAAAA,GAAA,CAAAlC,EAAA,KAAAA,EAAA;MAAA;MACA,OAAA6B,QAAA;IACA;IAEA,iBACAI,iBAAA,WAAAA,kBAAAF,UAAA;MACA,IAAAI,MAAA;MAEA,SAAAC,QAAAC,IAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA9N,OAAA,EACA4N,IAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAlG,CAAA,IAAAsG,IAAA;YAAA,IAAAR,GAAA,GAAAM,KAAA,CAAAxL,KAAA;YACAmL,MAAA,CAAA/B,IAAA,CAAA8B,GAAA;YACA,IAAAA,GAAA,CAAAhL,QAAA,IAAAgL,GAAA,CAAAhL,QAAA,CAAAmD,MAAA;cACA+H,OAAA,CAAAF,GAAA,CAAAhL,QAAA;YACA;UACA;QAAA,SAAAyL,GAAA;UAAAL,SAAA,CAAAM,CAAA,CAAAD,GAAA;QAAA;UAAAL,SAAA,CAAAO,CAAA;QAAA;MACA;MAEAT,OAAA,CAAAL,UAAA;MACA,OAAAI,MAAA;IACA;IAEA,aACAhC,eAAA,WAAAA,gBAAA/F,IAAA;MACAA,IAAA,CAAA7E,UAAA,GAAA6E,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;IACA;IAEA,aACAkD,mBAAA,WAAAA,oBAAAC,aAAA;MACA,IAAAC,eAAA,OAAAlO,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QACA;QAAA;QAAA;MAAA,QACA,gCACA;;MAEA;MACA,IAAAsG,KAAA,CAAAkI,OAAA,CAAAF,aAAA;QACA,OAAAA,aAAA,CAAAzE,GAAA,WAAA4E,CAAA;UAAA,OAAAF,eAAA,CAAAE,CAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,OAAAH,eAAA,CAAAD,aAAA;IACA;IAIA,cACAK,iBAAA,WAAAA,kBAAAC,SAAA;MACA,IAAAA,SAAA,CAAA9O,IAAA;QACA;MACA,WAAA8O,SAAA,CAAA9O,IAAA;QACA;QACA,IAAA8O,SAAA,CAAA1K,qBAAA,IAAA0K,SAAA,CAAA1K,qBAAA,CAAA0B,MAAA;UACA,IAAA2I,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAA,eAAA,CAAAK,SAAA,CAAA1K,qBAAA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA,iCACA2K,gBAAA,WAAAA,iBAAAlJ,IAAA;MACA,IAAAA,IAAA,CAAA7F,IAAA;QACA;MACA,WAAA6F,IAAA,CAAA7F,IAAA;QACA;MACA;MACA;IACA;IAEA,+BACAgP,cAAA,WAAAA,eAAAnJ,IAAA;MACA,IAAAA,IAAA,CAAA7F,IAAA;QACA;QACA,IAAA6F,IAAA,CAAAR,aAAA,IAAAQ,IAAA,CAAAR,aAAA,CAAAS,MAAA;UACA,OAAAD,IAAA,CAAAR,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;YAAA,OAAAA,IAAA,CAAAN,QAAA;UAAA,GAAA0J,IAAA;QACA;QACA;MACA,WAAA/I,IAAA,CAAA7F,IAAA;QACA;QACA,IAAA6F,IAAA,CAAAzB,qBAAA,IAAAyB,IAAA,CAAAzB,qBAAA,CAAA0B,MAAA;UACA,IAAA2I,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAA5I,IAAA,CAAAzB,qBAAA,CAAA2F,GAAA,WAAA/J,IAAA;YAAA,OAAAyO,eAAA,CAAAzO,IAAA;UAAA,GAAA4O,IAAA;QACA;QACA;MACA;MACA;IACA;IAEA,YACAK,UAAA,WAAAA,WAAApJ,IAAA;MACA,KAAAhB,gBAAA;MACA,KAAAE,UAAA,GAAAc,IAAA;;MAEA;MACA,IAAAqJ,eAAA;MAEA,IAAArJ,IAAA,CAAA7F,IAAA;QACA;QACAkP,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAtK,QAAA,CAAAX,QAAA,GAAAiL,eAAA;MACA,KAAAtK,QAAA,CAAAV,YAAA;MACA,KAAAU,QAAA,CAAAT,aAAA;MACA,KAAAS,QAAA,CAAAR,qBAAA;MAEA,KAAAO,iBAAA;MACA,KAAAoE,WAAA;QACA5F,OAAA;QACAC,QAAA;QACA8B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAkG,QAAA,WAAAA,SAAAtJ,IAAA;MAAA,IAAAuJ,MAAA;MACA,KAAAvK,gBAAA;MACA,KAAAC,WAAA,GAAAe,IAAA;MACA,KAAAjB,QAAA,CAAAX,QAAA,GAAA4B,IAAA,CAAA7F,IAAA;MACA,KAAA4E,QAAA,CAAAV,YAAA,GAAA2B,IAAA,CAAA3B,YAAA;MAEA,IAAA2B,IAAA,CAAA7F,IAAA;QACA;QACA,KAAA4E,QAAA,CAAAT,aAAA,OAAA8G,mBAAA,CAAA/K,OAAA,EAAA2F,IAAA,CAAA1B,aAAA;MACA,WAAA0B,IAAA,CAAA7F,IAAA;QACA;QACA,KAAA4E,QAAA,CAAAR,qBAAA,OAAA6G,mBAAA,CAAA/K,OAAA,EAAA2F,IAAA,CAAAzB,qBAAA;QACA;QACA,KAAAuI,SAAA;UACAyC,MAAA,CAAAtH,uBAAA;QACA;MACA;MAEA,KAAAnD,iBAAA;MACA,KAAAoE,WAAA;QACA5F,OAAA;QACAC,QAAA;QACA8B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAoG,UAAA,WAAAA,WAAAxJ,IAAA;MAAA,IAAAyJ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzP,IAAA;MACA,GAAAuJ,IAAA;QACA,IAAAiC,KAAA,GAAA8D,MAAA,CAAA5K,KAAA,CAAAgL,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAA5F,IAAA,CAAA4F,EAAA;QAAA;QACA,IAAAD,KAAA;UACA8D,MAAA,CAAA5K,KAAA,CAAAgI,MAAA,CAAAlB,KAAA;UACA8D,MAAA,CAAAhH,QAAA,CAAAyC,OAAA;QACA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,YACAyF,eAAA,WAAAA,gBAAA7K,UAAA,EAAA+J,SAAA;MAAA,IAAAe,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzP,IAAA;MACA,GAAAuJ,IAAA;QACA,IAAAiC,KAAA,GAAAzG,UAAA,CAAApC,QAAA,CAAA+M,SAAA,WAAA3J,KAAA;UAAA,OAAAA,KAAA,CAAA0F,EAAA,KAAAqD,SAAA,CAAArD,EAAA;QAAA;QACA,IAAAD,KAAA;UACAzG,UAAA,CAAApC,QAAA,CAAA+J,MAAA,CAAAlB,KAAA;UACAqE,MAAA,CAAAvH,QAAA,CAAAyC,OAAA;QACA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,cACA2F,cAAA,WAAAA,eAAA;MACA,aAAAjL,gBAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA,iBACAkL,2BAAA,WAAAA,4BAAA;MACA;MACA,SAAAlL,gBAAA;QACA;MACA;MACA;MACA,SAAAA,gBAAA;QACA;MACA;MACA;MACA;IACA;IAEA,iBACAmL,kBAAA,WAAAA,mBAAA/L,QAAA;MACA;MACA,SAAAY,gBAAA;QACA,YAAAC,WAAA,CAAA9E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAY,gBAAA,sBAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAA/E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,SAAAc,UAAA,CAAA/E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAc,UAAA,CAAA/E,IAAA;MACA;;MAEA;MACA,SAAA6E,gBAAA;QACA;QACA,OAAAZ,QAAA;MACA;;MAEA;MACA,SAAAY,gBAAA;QACA;QACA,OAAAZ,QAAA;MACA;;MAEA;MACA,SAAAY,gBAAA,iCAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAA/E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAc,UAAA,CAAA/E,IAAA;MACA;;MAEA;MACA;IACA;IAEA,iBACAiQ,sBAAA,WAAAA,uBAAA1M,YAAA;MACA;MACA,SAAAsB,gBAAA,sBAAAA,gBAAA;QACA;MACA;MAEA,UAAAE,UAAA;QACA;MACA;;MAEA;MACA,IAAAmL,qBAAA;MACA,SAAAnL,UAAA,CAAApC,QAAA;QACA,KAAAoC,UAAA,CAAApC,QAAA,CAAAiD,OAAA,WAAAG,KAAA;UACA,IAAAA,KAAA,CAAA/F,IAAA,UAAA+F,KAAA,CAAA3B,qBAAA;YACA8L,qBAAA,CAAArE,IAAA,CAAAsE,KAAA,CAAAD,qBAAA,MAAAjF,mBAAA,CAAA/K,OAAA,EAAA6F,KAAA,CAAA3B,qBAAA;UACA;QACA;MACA;;MAEA;MACA,QAAA8L,qBAAA,CAAAzK,QAAA,CAAAlC,YAAA;IACA;IAEA,iBACA6M,eAAA,WAAAA,gBAAAC,GAAA;MACA;MACA,SAAAxL,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA,sBAAAA,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA;QACA,aAAAmC,sBAAA,CAAAvB,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;MACA;;MAEA;MACA,aAAAU,WAAA,CAAAX,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;IACA;IAEA,iBACA4K,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;MACA;MACA,SAAAxL,gBAAA,oBAAAA,gBAAA,sBAAAuB,WAAA,CAAAX,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;QACA;MACA;MACA;IACA;IAEA,eACAoC,uBAAA,WAAAA,wBAAA;MAAA,IAAA0I,MAAA;MAAA,WAAAjJ,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAAgJ,SAAA;QAAA,IAAApL,aAAA,EAAAqL,KAAA,EAAAC,EAAA;QAAA,WAAAnJ,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAAiJ,SAAA;UAAA,kBAAAA,SAAA,CAAA/I,CAAA;YAAA;cAAA,MACA2I,MAAA,CAAA5L,QAAA,CAAAX,QAAA,WAAAuM,MAAA,CAAA5L,QAAA,CAAAR,qBAAA,CAAA0B,MAAA;gBAAA8K,SAAA,CAAA/I,CAAA;gBAAA;cAAA;cACA2I,MAAA,CAAAxL,wBAAA;cAAA,OAAA4L,SAAA,CAAA7I,CAAA;YAAA;cAIA;cACA1C,aAAA;cACA,KAAAmL,MAAA,CAAA3L,gBAAA,iBAAA2L,MAAA,CAAA3L,gBAAA,6BAAA2L,MAAA,CAAAzL,UAAA,IAAAyL,MAAA,CAAAzL,UAAA,CAAA/E,IAAA;gBACA;gBACAqF,aAAA,GAAAmL,MAAA,CAAAzL,UAAA,CAAAM,aAAA;cACA;gBACA;gBACAA,aAAA,GAAAmL,MAAA,CAAAnL,aAAA;cACA;cAAA,IAEAA,aAAA,CAAAS,MAAA;gBAAA8K,SAAA,CAAA/I,CAAA;gBAAA;cAAA;cACA2I,MAAA,CAAAxL,wBAAA;cACAwL,MAAA,CAAAlI,QAAA,CAAAoC,OAAA;cAAA,OAAAkG,SAAA,CAAA7I,CAAA;YAAA;cAAA6I,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAA/I,CAAA;cAAA,OAKA2I,MAAA,CAAAM,sBAAA,CAAAzL,aAAA,EAAAmL,MAAA,CAAA5L,QAAA,CAAAR,qBAAA;YAAA;cAAAsM,KAAA,GAAAE,SAAA,CAAAG,CAAA;cACAP,MAAA,CAAAxL,wBAAA,GAAA0L,KAAA;cAEA,IAAAA,KAAA;gBACAF,MAAA,CAAAlI,QAAA,CAAAoC,OAAA;cACA;cAAAkG,SAAA,CAAA/I,CAAA;cAAA;YAAA;cAAA+I,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAG,CAAA;cAEAC,OAAA,CAAAC,KAAA,cAAAN,EAAA;cACAH,MAAA,CAAAxL,wBAAA;cACAwL,MAAA,CAAAlI,QAAA,CAAA2I,KAAA;YAAA;cAAA,OAAAL,SAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA0I,QAAA;MAAA;IAEA;IAEA,oBACAK,sBAAA,WAAAA,uBAAAI,KAAA,EAAA1C,aAAA;MAAA,WAAAjH,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAA0J,SAAA;QAAA,IAAAjH,UAAA,EAAAkH,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAA/J,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAA6J,SAAA;UAAA,kBAAAA,SAAA,CAAA3J,CAAA;YAAA;cAAA,MACA,CAAAqJ,KAAA,CAAApL,MAAA,KAAA0I,aAAA,CAAA1I,MAAA;gBAAA0L,SAAA,CAAA3J,CAAA;gBAAA;cAAA;cAAA,OAAA2J,SAAA,CAAAzJ,CAAA,IACA;YAAA;cAGAmC,UAAA,MAEA;cAAAkH,UAAA,OAAApD,2BAAA,CAAA9N,OAAA,EACAgR,KAAA;cAAAM,SAAA,CAAAX,CAAA;cAAAS,KAAA,oBAAA9J,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAA6J,MAAA;gBAAA,IAAA9L,IAAA,EAAAiM,QAAA,EAAAC,UAAA,EAAAC,GAAA;gBAAA,WAAAnK,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAAiK,SAAA;kBAAA,kBAAAA,SAAA,CAAA/J,CAAA;oBAAA;sBAAArC,IAAA,GAAA6L,MAAA,CAAA5O,KAAA;sBAAAmP,SAAA,CAAAf,CAAA;sBAAAe,SAAA,CAAA/J,CAAA;sBAAA,OAGA,IAAAmC,+BAAA,EAAAxE,IAAA,CAAAE,MAAA;oBAAA;sBAAA+L,QAAA,GAAAG,SAAA,CAAAb,CAAA;sBACAC,OAAA,CAAAa,GAAA,gBAAAC,MAAA,CAAAtM,IAAA,CAAAE,MAAA,gCAAA+L,QAAA;sBAEA,IAAAA,QAAA,CAAAM,IAAA,YAAAN,QAAA,CAAAnR,IAAA;wBACAoR,UAAA,GAAAD,QAAA,CAAAnR,IAAA,EAEA;wBACAkO,aAAA,CAAA5I,OAAA,WAAA5F,IAAA;0BACA,QAAAA,IAAA;4BACA;8BAAA;8BACAkK,UAAA,IAAAwH,UAAA,CAAAM,YAAA;8BACA;4BACA;8BAAA;8BACA9H,UAAA,IAAAwH,UAAA,CAAAO,cAAA;8BACA;4BACA;8BAAA;8BACA/H,UAAA,IAAAwH,UAAA,CAAAQ,QAAA;8BACA;0BACA;wBACA;sBACA;sBAAAN,SAAA,CAAA/J,CAAA;sBAAA;oBAAA;sBAAA+J,SAAA,CAAAf,CAAA;sBAAAc,GAAA,GAAAC,SAAA,CAAAb,CAAA;sBAEAC,OAAA,CAAAC,KAAA,4BAAAa,MAAA,CAAAtM,IAAA,CAAAE,MAAA,4CAAAiM,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAA7J,CAAA;kBAAA;gBAAA,GAAAuJ,KAAA;cAAA;cAAAF,UAAA,CAAAlD,CAAA;YAAA;cAAA,KAAAmD,MAAA,GAAAD,UAAA,CAAAvJ,CAAA,IAAAsG,IAAA;gBAAAqD,SAAA,CAAA3J,CAAA;gBAAA;cAAA;cAAA,OAAA2J,SAAA,CAAAW,CAAA,KAAAC,mBAAA,CAAAlS,OAAA,EAAAoR,KAAA;YAAA;cAAAE,SAAA,CAAA3J,CAAA;cAAA;YAAA;cAAA2J,SAAA,CAAA3J,CAAA;cAAA;YAAA;cAAA2J,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAAAK,UAAA,CAAA/C,CAAA,CAAAkD,GAAA;YAAA;cAAAC,SAAA,CAAAX,CAAA;cAAAO,UAAA,CAAA9C,CAAA;cAAA,OAAAkD,SAAA,CAAAlD,CAAA;YAAA;cAIA0C,OAAA,CAAAa,GAAA,oCAAAC,MAAA,CAAA5H,UAAA;cAAA,OAAAsH,SAAA,CAAAzJ,CAAA,IACAmC,UAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IAEA,eACAkB,qBAAA,WAAAA,sBAAApO,QAAA;MACA;MACA,SAAAY,gBAAA;QACA,YAAAC,WAAA,CAAA9E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAY,gBAAA;QACA,OAAAZ,QAAA;MACA;MAEA;IACA;IAEA,cACAqO,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,KAAApQ,cAAA,CAAAyD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAA4L,QAAA,GAAAD,OAAA,CAAAnQ,SAAA;MACA;IACA;IAEA,aACAqQ,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAA9O,iBAAA,QAAAzB,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;QAAA,OAAAA,CAAA,CAAAH,QAAA;MAAA;MACA,IAAA5O,iBAAA,CAAAkC,MAAA;QACA,KAAAwC,QAAA,CAAAoC,OAAA;QACA;MACA;MAEA,KAAA6E,QAAA,+CAAAuC,MAAA,CAAAlO,iBAAA,CAAAkC,MAAA;QACA0J,iBAAA;QACAC,gBAAA;QACAzP,IAAA;MACA,GAAAuJ,IAAA;QACAmJ,OAAA,CAAAvQ,cAAA,GAAAuQ,OAAA,CAAAvQ,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;UAAA,QAAAA,CAAA,CAAAH,QAAA;QAAA;QACAE,OAAA,CAAAtQ,SAAA;QACAsQ,OAAA,CAAApK,QAAA,CAAAyC,OAAA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,WACAyI,kBAAA,WAAAA,mBAAA;MACA,KAAAtQ,sBAAA;MACA,KAAAuQ,oBAAA;IACA;IAEA,gBACAA,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA/P,YAAA,CAAAC,gBAAA;MACA,KAAAD,YAAA,CAAAE,iBAAA;MACA,KAAAF,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAAX,YAAA,CAAAc,iBAAA;MACA,KAAAd,YAAA,CAAAe,aAAA;;MAEA;MACA,KAAAiP,gBAAA;;MAEA;MACA,KAAAC,6BAAA;IACA;IAEA,cACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,OAAA;MACA,IAAA1J,sBAAA;QAAAlG,QAAA;MAAA,GAAAmG,IAAA,WAAAkI,QAAA;QACA,IAAAjE,UAAA,GAAAiE,QAAA,CAAA5H,IAAA;QACAmJ,OAAA,CAAAzQ,eAAA,GAAAyQ,OAAA,CAAAC,iBAAA,CAAAzF,UAAA;MACA,GAAArD,KAAA,WAAA8G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA+B,OAAA,CAAAzQ,eAAA;MACA;IACA;IAEA,YACA0Q,iBAAA,WAAAA,kBAAAzF,UAAA;MACA,IAAAzD,GAAA;;MAEA;MACAyD,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;QACAvD,GAAA,CAAAuD,QAAA,CAAA7B,EAAA,QAAAyH,cAAA,CAAAhT,OAAA,MAAAgT,cAAA,CAAAhT,OAAA,MAAAoN,QAAA;UAAA3K,QAAA;QAAA;MACA;;MAEA;MACA,IAAAiL,MAAA;MACAJ,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;QACA,IAAAA,QAAA,CAAAnH,QAAA;UACA;UACAyH,MAAA,CAAA/B,IAAA,CAAA9B,GAAA,CAAAuD,QAAA,CAAA7B,EAAA;QACA;UACA;UACA,IAAA1B,GAAA,CAAAuD,QAAA,CAAAnH,QAAA;YACA4D,GAAA,CAAAuD,QAAA,CAAAnH,QAAA,EAAAxD,QAAA,CAAAkJ,IAAA,CAAA9B,GAAA,CAAAuD,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;;MAEA;MACA,IAAA0H,mBAAA,YAAAA,mBAAAC,IAAA;QACA,IAAAA,IAAA,CAAAzQ,QAAA,IAAAyQ,IAAA,CAAAzQ,QAAA,CAAAmD,MAAA;UACA,OAAAsN,IAAA,CAAAzQ,QAAA;QACA,WAAAyQ,IAAA,CAAAzQ,QAAA,IAAAyQ,IAAA,CAAAzQ,QAAA,CAAAmD,MAAA;UACAsN,IAAA,CAAAzQ,QAAA,CAAAiD,OAAA,WAAAG,KAAA;YAAA,OAAAoN,mBAAA,CAAApN,KAAA;UAAA;QACA;MACA;;MAEA;MACA6H,MAAA,CAAAhI,OAAA,WAAAwN,IAAA;QAAA,OAAAD,mBAAA,CAAAC,IAAA;MAAA;MAEA,OAAAxF,MAAA;IACA;IAEA,gBACAyF,sBAAA,WAAAA,uBAAAhG,UAAA,EAAAG,UAAA;MACA,IAAAI,MAAA,IAAAP,UAAA;MAEA,IAAAiG,aAAA,YAAAA,aAAAnN,QAAA;QACAqH,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;UACA,IAAAA,QAAA,CAAAnH,QAAA,KAAAA,QAAA;YACAyH,MAAA,CAAA/B,IAAA,CAAAyB,QAAA,CAAA7B,EAAA;YACA6H,aAAA,CAAAhG,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;MAEA6H,aAAA,CAAAjG,UAAA;MACA,OAAAO,MAAA;IACA;IAEA,kBACAmF,6BAAA,WAAAA,8BAAA;MAAA,IAAAQ,OAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAA1Q,YAAA,CAAAC,gBAAA;QACA;QACA,IAAAuG,sBAAA;UAAAlG,QAAA;QAAA,GAAAmG,IAAA,WAAAkI,QAAA;UACA,IAAAgC,aAAA,GAAAhC,QAAA,CAAA5H,IAAA;UACA2J,WAAA,GAAAD,OAAA,CAAAF,sBAAA,CAAAE,OAAA,CAAAzQ,YAAA,CAAAC,gBAAA,EAAA0Q,aAAA;;UAEA;UACAF,OAAA,CAAAG,6BAAA,CAAAF,WAAA;QACA,GAAArJ,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACAsC,OAAA,CAAAG,6BAAA;QACA;MACA;QACA;QACA,KAAAA,6BAAA;MACA;IACA;IAEA,mBACAA,6BAAA,WAAAA,8BAAAF,WAAA;MAAA,IAAAG,OAAA;MACA,IAAA5K,WAAA;QACA5F,OAAA,OAAAL,YAAA,CAAAI,cAAA,CAAAC,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAI,cAAA,CAAAE,QAAA;QACA8B,QAAA,OAAApC,YAAA,CAAAE,iBAAA,IAAAmC;MACA;MAEA,IAAAqO,WAAA,CAAA1N,MAAA;QACA;QACA,IAAA8N,QAAA,GAAAJ,WAAA,CAAAzJ,GAAA,WAAAsD,UAAA;UACA,WAAAhE,8BAAA,MAAA6J,cAAA,CAAAhT,OAAA,MAAAgT,cAAA,CAAAhT,OAAA,MAAA6I,WAAA;YAAAsE,UAAA,EAAAA;UAAA;QACA;QAEAlE,OAAA,CAAAC,GAAA,CAAAwK,QAAA,EAAArK,IAAA,WAAAsK,SAAA;UACA,IAAAC,QAAA;UACA,IAAA5J,UAAA;UAEA2J,SAAA,CAAAjO,OAAA,WAAA6L,QAAA;YACA,IAAAA,QAAA,CAAA5H,IAAA;cACAiK,QAAA,CAAAjI,IAAA,CAAAsE,KAAA,CAAA2D,QAAA,MAAA7I,mBAAA,CAAA/K,OAAA,EAAAuR,QAAA,CAAA5H,IAAA;cACAK,UAAA,IAAAuH,QAAA,CAAApO,KAAA;YACA;UACA;;UAEA;UACA,IAAA0Q,WAAA,GAAAD,QAAA,CAAAvO,MAAA,WAAAC,IAAA,EAAAgG,KAAA,EAAAwI,IAAA;YAAA,OACAxI,KAAA,KAAAwI,IAAA,CAAAtE,SAAA,WAAAuE,CAAA;cAAA,OAAAA,CAAA,CAAAvO,MAAA,KAAAF,IAAA,CAAAE,MAAA;YAAA;UAAA,CACA;UAEAiO,OAAA,CAAA7Q,YAAA,CAAAG,aAAA,GAAA8Q,WAAA;UACAJ,OAAA,CAAA7Q,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAA0Q,WAAA,CAAAjO,MAAA;QACA,GAAAqE,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAArL,QAAA,CAAA2I,KAAA;UACA0C,OAAA,CAAA7Q,YAAA,CAAAG,aAAA;UACA0Q,OAAA,CAAA7Q,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;QACA;QACA,IAAAgG,8BAAA,EAAAN,WAAA,EAAAQ,IAAA,WAAAkI,QAAA;UACAkC,OAAA,CAAA7Q,YAAA,CAAAG,aAAA,GAAAwO,QAAA,CAAA5H,IAAA;UACA8J,OAAA,CAAA7Q,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAAoO,QAAA,CAAApO,KAAA;QACA,GAAA8G,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAArL,QAAA,CAAA2I,KAAA;UACA0C,OAAA,CAAA7Q,YAAA,CAAAG,aAAA;UACA0Q,OAAA,CAAA7Q,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEA,WACA6Q,mBAAA,WAAAA,oBAAA;MACA,KAAApR,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAA4P,6BAAA;IACA;IAEA,WACAoB,kBAAA,WAAAA,mBAAA9D,GAAA;MACA,KAAAvN,YAAA,CAAAQ,cAAA,GAAA+M,GAAA,CAAA3K,MAAA;MACA,KAAA0O,aAAA;IACA;IAEA,aACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAAvR,YAAA,CAAAQ,cAAA;QACA,KAAAR,YAAA,CAAAY,SAAA;QACA,KAAAZ,YAAA,CAAAa,kBAAA,CAAAN,KAAA;QACA;MACA;MAEA,IAAA0F,WAAA;QACA5F,OAAA,OAAAL,YAAA,CAAAa,kBAAA,CAAAR,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAa,kBAAA,CAAAP,QAAA;QACAsC,MAAA,OAAA5C,YAAA,CAAAQ,cAAA;QACAC,YAAA,OAAAT,YAAA,CAAAS,YAAA,IAAA4B,SAAA;QACA3B,UAAA,OAAAV,YAAA,CAAAU,UAAA,IAAA2B,SAAA;QACAmP,eAAA,OAAAxR,YAAA,CAAAW,qBAAA,IAAA0B;MACA;MAEA,IAAAoP,sBAAA,EAAAxL,WAAA,EAAAQ,IAAA,WAAAkI,QAAA;QACA4C,OAAA,CAAAvR,YAAA,CAAAY,SAAA,GAAA+N,QAAA,CAAA5H,IAAA;QACAwK,OAAA,CAAAvR,YAAA,CAAAa,kBAAA,CAAAN,KAAA,GAAAoO,QAAA,CAAApO,KAAA;MACA,GAAA8G,KAAA,WAAA8G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAoD,OAAA,CAAA/L,QAAA,CAAA2I,KAAA;QACAoD,OAAA,CAAAvR,YAAA,CAAAY,SAAA;QACA2Q,OAAA,CAAAvR,YAAA,CAAAa,kBAAA,CAAAN,KAAA;MACA;IACA;IAEA,WACAmR,eAAA,WAAAA,gBAAA;MACA,KAAA1R,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAAiR,aAAA;IACA;IAEA,aACAK,mBAAA,WAAAA,oBAAA;MACA,KAAA3R,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAA+Q,eAAA;IACA;IAEA,WACAE,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAA5Q,qBAAA;MACA,KAAA6Q,oBAAA;MACA,KAAA1L,iBAAA;IACA;IAEA,mBACA0L,oBAAA,WAAAA,qBAAA;MACA,KAAA5Q,gBAAA;MACA,KAAAC,eAAA;QACAC,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;IACA;IAEA,mBACAwQ,wBAAA,WAAAA,yBAAA;MACA,KAAA/P,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAF,QAAA,CAAAX,QAAA;MACA,KAAAU,iBAAA;MACA;MACA,KAAAoE,WAAA;QACA5F,OAAA;QACAC,QAAA;QACA8B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,oBACA4L,2BAAA,WAAAA,4BAAA9P,UAAA;MACA,KAAAF,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,UAAA,GAAAA,UAAA;;MAEA;MACA,IAAAmK,eAAA;MACA,IAAAnK,UAAA,CAAA/E,IAAA;QACA;QACAkP,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAtK,QAAA,CAAAX,QAAA,GAAAiL,eAAA;MACA,KAAAtK,QAAA,CAAAV,YAAA;MACA,KAAAU,QAAA,CAAAT,aAAA;MACA,KAAAS,QAAA,CAAAR,qBAAA;MAEA,KAAAO,iBAAA;MACA,KAAAoE,WAAA;QACA5F,OAAA;QACAC,QAAA;QACA8B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,qBACA8C,0BAAA,WAAAA,2BAAAlG,IAAA;MACAA,IAAA,CAAA7E,UAAA,GAAA6E,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;IACA;IAEA,mBACAyJ,qBAAA,WAAAA,sBAAAjP,IAAA;MAAA,IAAAkP,OAAA;MACA;MACA,IAAAlP,IAAA,CAAAlD,QAAA,IAAAkD,IAAA,CAAAlD,QAAA,CAAAmD,MAAA;QACA;QACAD,IAAA,CAAAlD,QAAA,CAAAiD,OAAA,WAAAkJ,SAAA;UACA,IAAAkG,UAAA,GAAAD,OAAA,CAAAhR,gBAAA,CAAA2L,SAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;UAAA;UACA,IAAAuJ,UAAA;YACAD,OAAA,CAAAhR,gBAAA,CAAA2I,MAAA,CAAAsI,UAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAxJ,KAAA,QAAAzH,gBAAA,CAAA2L,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAA5F,IAAA,CAAA4F,EAAA;MAAA;MACA,IAAAD,KAAA;QACA,KAAAzH,gBAAA,CAAA2I,MAAA,CAAAlB,KAAA;MACA;IACA;IAEA,oBACAyJ,0BAAA,WAAAA,2BAAAlQ,UAAA,EAAA+J,SAAA;MACA;MACA,IAAA/J,UAAA,CAAApC,QAAA;QACA,IAAAqS,UAAA,GAAAjQ,UAAA,CAAApC,QAAA,CAAA+M,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;QAAA;QACA,IAAAuJ,UAAA;UACAjQ,UAAA,CAAApC,QAAA,CAAA+J,MAAA,CAAAsI,UAAA;QACA;MACA;;MAEA;MACA,IAAAxJ,KAAA,QAAAzH,gBAAA,CAAA2L,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;MAAA;MACA,IAAAD,KAAA;QACA,KAAAzH,gBAAA,CAAA2I,MAAA,CAAAlB,KAAA;MACA;IACA;IAEA,iBACA0J,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5N,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAA2N,SAAA;QAAA,IAAAxR,iBAAA,EAAAyR,GAAA;QAAA,WAAA7N,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAA2N,SAAA;UAAA,kBAAAA,SAAA,CAAAzN,CAAA;YAAA;cAAA,MACAsN,OAAA,CAAApR,gBAAA,CAAA+B,MAAA;gBAAAwP,SAAA,CAAAzN,CAAA;gBAAA;cAAA;cACAsN,OAAA,CAAA7M,QAAA,CAAAoC,OAAA;cAAA,OAAA4K,SAAA,CAAAvN,CAAA;YAAA;cAAAuN,SAAA,CAAAzE,CAAA;cAAAyE,SAAA,CAAAzN,CAAA;cAAA,OAMAsN,OAAA,CAAAI,yBAAA,CAAAJ,OAAA,CAAApR,gBAAA;YAAA;cAAAH,iBAAA,GAAA0R,SAAA,CAAAvE,CAAA;cAAA,MAEAnN,iBAAA,CAAAkC,MAAA;gBAAAwP,SAAA,CAAAzN,CAAA;gBAAA;cAAA;cACAsN,OAAA,CAAA7M,QAAA,CAAAoC,OAAA;cAAA,OAAA4K,SAAA,CAAAvN,CAAA;YAAA;cAIA;cACAnE,iBAAA,CAAAgC,OAAA,WAAAgB,QAAA,EAAA4E,KAAA;gBACA2J,OAAA,CAAAhT,cAAA,CAAA0J,IAAA;kBACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;kBACAgK,UAAA,EAAA5O,QAAA,CAAA4O,UAAA;kBACAC,OAAA,EAAA7O,QAAA,CAAA0N,eAAA;kBACAtU,IAAA,EAAA4G,QAAA,CAAArD,YAAA;kBACAC,UAAA,EAAAoD,QAAA,CAAApD,UAAA;kBACAkS,OAAA,EAAA9O,QAAA,CAAA8O,OAAA;kBACAlR,KAAA,EAAAoC,QAAA,CAAApC,KAAA;kBAAA;kBACAgO,QAAA;kBACAmD,QAAA;gBACA;cACA;cAEAR,OAAA,CAAArR,qBAAA;cACAqR,OAAA,CAAA7M,QAAA,CAAAyC,OAAA,2DAAA+G,MAAA,CAAAlO,iBAAA,CAAAkC,MAAA;cAAAwP,SAAA,CAAAzN,CAAA;cAAA;YAAA;cAAAyN,SAAA,CAAAzE,CAAA;cAAAwE,GAAA,GAAAC,SAAA,CAAAvE,CAAA;cAEAC,OAAA,CAAAC,KAAA,YAAAoE,GAAA;cACAF,OAAA,CAAA7M,QAAA,CAAA2I,KAAA;YAAA;cAAA,OAAAqE,SAAA,CAAAvN,CAAA;UAAA;QAAA,GAAAqN,QAAA;MAAA;IAEA;IAEA,eACAG,yBAAA,WAAAA,0BAAA7Q,KAAA;MAAA,IAAAkR,OAAA;MAAA,WAAArO,kBAAA,CAAArH,OAAA,mBAAAsH,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAAoO,SAAA;QAAA,IAAAC,YAAA,EAAA5P,WAAA,EAAA6P,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,GAAA;QAAA,WAAA1O,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAAwO,SAAA;UAAA,kBAAAA,SAAA,CAAAtO,CAAA;YAAA;cACAiO,YAAA,OAEA;cACA5P,WAAA,GAAAxB,KAAA,CAAAa,MAAA,WAAAM,IAAA;gBAAA,QAAAA,IAAA,CAAAM,QAAA;cAAA;cAAA4P,UAAA,OAAA/H,2BAAA,CAAA9N,OAAA,EAEAgG,WAAA;cAAAiQ,SAAA,CAAAtF,CAAA;cAAAoF,MAAA,oBAAAzO,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAAwO,OAAA;gBAAA,IAAAlR,UAAA,EAAAqR,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAA7S,SAAA,EAAA8S,UAAA,EAAAC,MAAA,EAAAjR,IAAA,EAAAuD,WAAA,EAAA0I,QAAA,EAAAiF,QAAA,EAAA1Q,aAAA,EAAApC,iBAAA,EAAA+S,GAAA,EAAAC,GAAA,EAAAC,GAAA;gBAAA,WAAArP,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAAmP,SAAA;kBAAA,kBAAAA,SAAA,CAAAjP,CAAA;oBAAA;sBAAA9C,UAAA,GAAAiR,MAAA,CAAAvT,KAAA;sBAAAqU,SAAA,CAAAjG,CAAA;sBAEA;sBACAuF,UAAA,GAAA1R,KAAA,CAAAa,MAAA,WAAAM,IAAA;wBAAA,OAAAA,IAAA,CAAAM,QAAA,KAAApB,UAAA,CAAA0G,EAAA;sBAAA;sBAAA,MAEA2K,UAAA,CAAAtQ,MAAA;wBAAAgR,SAAA,CAAAjP,CAAA;wBAAA;sBAAA;sBACA;sBAAAwO,UAAA,OAAArI,2BAAA,CAAA9N,OAAA,EACAkW,UAAA;sBAAAU,SAAA,CAAAjG,CAAA;sBAAA0F,MAAA,oBAAA/O,aAAA,CAAAtH,OAAA,IAAAuH,CAAA,UAAA8O,OAAA;wBAAA,IAAAzH,SAAA,EAAApL,SAAA,EAAAqT,UAAA,EAAAC,MAAA,EAAAxR,IAAA,EAAAyR,UAAA,EAAAC,MAAA,EAAA3T,YAAA,EAAAwF,WAAA,EAAA0I,QAAA,EAAAiF,QAAA,EAAA1Q,aAAA,EAAApC,iBAAA,EAAAuT,GAAA,EAAAC,GAAA;wBAAA,WAAA5P,aAAA,CAAAtH,OAAA,IAAAyH,CAAA,WAAA0P,SAAA;0BAAA,kBAAAA,SAAA,CAAAxP,CAAA;4BAAA;8BAAAiH,SAAA,GAAAwH,MAAA,CAAA7T,KAAA;8BACAiB,SAAA,OAEA;8BAAAqT,UAAA,OAAA/I,2BAAA,CAAA9N,OAAA,EACA6E,UAAA,CAAAM,aAAA;8BAAAgS,SAAA,CAAAxG,CAAA;8BAAAkG,UAAA,CAAA7I,CAAA;4BAAA;8BAAA,KAAA8I,MAAA,GAAAD,UAAA,CAAAlP,CAAA,IAAAsG,IAAA;gCAAAkJ,SAAA,CAAAxP,CAAA;gCAAA;8BAAA;8BAAArC,IAAA,GAAAwR,MAAA,CAAAvU,KAAA;8BAAAwU,UAAA,OAAAjJ,2BAAA,CAAA9N,OAAA,EACA4O,SAAA,CAAA1K,qBAAA;8BAAAiT,SAAA,CAAAxG,CAAA;8BAAAoG,UAAA,CAAA/I,CAAA;4BAAA;8BAAA,KAAAgJ,MAAA,GAAAD,UAAA,CAAApP,CAAA,IAAAsG,IAAA;gCAAAkJ,SAAA,CAAAxP,CAAA;gCAAA;8BAAA;8BAAAtE,YAAA,GAAA2T,MAAA,CAAAzU,KAAA;8BACAsG,WAAA;gCACArD,MAAA,EAAAF,IAAA,CAAAE,MAAA;gCACAnC,YAAA,EAAAA,YAAA;gCACAJ,OAAA;gCACAC,QAAA;8BACA;8BAAAiU,SAAA,CAAAxP,CAAA;8BAAA,OAEA,IAAA0M,sBAAA,EAAAxL,WAAA;4BAAA;8BAAA0I,QAAA,GAAA4F,SAAA,CAAAtG,CAAA;8BACA,IAAAU,QAAA,CAAA5H,IAAA,IAAA4H,QAAA,CAAA5H,IAAA,CAAA/D,MAAA;gCACApC,SAAA,GAAAA,SAAA,CAAAoO,MAAA,CAAAL,QAAA,CAAA5H,IAAA;8BACA;4BAAA;8BAAAwN,SAAA,CAAAxP,CAAA;8BAAA;4BAAA;8BAAAwP,SAAA,CAAAxP,CAAA;8BAAA;4BAAA;8BAAAwP,SAAA,CAAAxG,CAAA;8BAAAsG,GAAA,GAAAE,SAAA,CAAAtG,CAAA;8BAAAkG,UAAA,CAAA5I,CAAA,CAAA8I,GAAA;4BAAA;8BAAAE,SAAA,CAAAxG,CAAA;8BAAAoG,UAAA,CAAA3I,CAAA;8BAAA,OAAA+I,SAAA,CAAA/I,CAAA;4BAAA;8BAAA+I,SAAA,CAAAxP,CAAA;8BAAA;4BAAA;8BAAAwP,SAAA,CAAAxP,CAAA;8BAAA;4BAAA;8BAAAwP,SAAA,CAAAxG,CAAA;8BAAAuG,GAAA,GAAAC,SAAA,CAAAtG,CAAA;8BAAAgG,UAAA,CAAA1I,CAAA,CAAA+I,GAAA;4BAAA;8BAAAC,SAAA,CAAAxG,CAAA;8BAAAkG,UAAA,CAAAzI,CAAA;8BAAA,OAAA+I,SAAA,CAAA/I,CAAA;4BAAA;8BAIA;8BACA,IAAA5K,SAAA,CAAAoC,MAAA;gCACA4Q,QAAA,GAAAd,OAAA,CAAA0B,YAAA,KAAArM,mBAAA,CAAA/K,OAAA,EAAAwD,SAAA;gCACAsC,aAAA,GAAAuR,IAAA,CAAAC,GAAA,CAAA1I,SAAA,CAAA9I,aAAA,EAAA0Q,QAAA,CAAA5Q,MAAA;gCACAlC,iBAAA,GAAA8S,QAAA,CAAAe,KAAA,IAAAzR,aAAA,GAEA;gCACApC,iBAAA,CAAAgC,OAAA,WAAAgB,QAAA;kCACAA,QAAA,CAAApC,KAAA,GAAAsK,SAAA,CAAAzD,gBAAA;gCACA;gCAEAyK,YAAA,CAAAjK,IAAA,CAAAsE,KAAA,CAAA2F,YAAA,MAAA7K,mBAAA,CAAA/K,OAAA,EAAA0D,iBAAA;8BACA;4BAAA;8BAAA,OAAAyT,SAAA,CAAAtP,CAAA;0BAAA;wBAAA,GAAAwO,MAAA;sBAAA;sBAAAF,UAAA,CAAAnI,CAAA;oBAAA;sBAAA,KAAAoI,MAAA,GAAAD,UAAA,CAAAxO,CAAA,IAAAsG,IAAA;wBAAA2I,SAAA,CAAAjP,CAAA;wBAAA;sBAAA;sBAAA,OAAAiP,SAAA,CAAA3E,CAAA,KAAAC,mBAAA,CAAAlS,OAAA,EAAAqW,MAAA;oBAAA;sBAAAO,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAAAiP,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAAAiP,SAAA,CAAAjG,CAAA;sBAAA8F,GAAA,GAAAG,SAAA,CAAA/F,CAAA;sBAAAsF,UAAA,CAAAhI,CAAA,CAAAsI,GAAA;oBAAA;sBAAAG,SAAA,CAAAjG,CAAA;sBAAAwF,UAAA,CAAA/H,CAAA;sBAAA,OAAAwI,SAAA,CAAAxI,CAAA;oBAAA;sBAAAwI,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAGA;sBACAnE,SAAA;sBAAA8S,UAAA,OAAAxI,2BAAA,CAAA9N,OAAA,EAEA6E,UAAA,CAAAM,aAAA;sBAAAyR,SAAA,CAAAjG,CAAA;sBAAA2F,UAAA,CAAAtI,CAAA;oBAAA;sBAAA,KAAAuI,MAAA,GAAAD,UAAA,CAAA3O,CAAA,IAAAsG,IAAA;wBAAA2I,SAAA,CAAAjP,CAAA;wBAAA;sBAAA;sBAAArC,IAAA,GAAAiR,MAAA,CAAAhU,KAAA;sBACAsG,WAAA;wBACArD,MAAA,EAAAF,IAAA,CAAAE,MAAA;wBACAvC,OAAA;wBACAC,QAAA;sBACA;sBAAA0T,SAAA,CAAAjP,CAAA;sBAAA,OAEA,IAAA0M,sBAAA,EAAAxL,WAAA;oBAAA;sBAAA0I,QAAA,GAAAqF,SAAA,CAAA/F,CAAA;sBACA,IAAAU,QAAA,CAAA5H,IAAA,IAAA4H,QAAA,CAAA5H,IAAA,CAAA/D,MAAA;wBACApC,SAAA,GAAAA,SAAA,CAAAoO,MAAA,CAAAL,QAAA,CAAA5H,IAAA;sBACA;oBAAA;sBAAAiN,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAAAiP,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAAAiP,SAAA,CAAAjG,CAAA;sBAAA+F,GAAA,GAAAE,SAAA,CAAA/F,CAAA;sBAAAyF,UAAA,CAAAnI,CAAA,CAAAuI,GAAA;oBAAA;sBAAAE,SAAA,CAAAjG,CAAA;sBAAA2F,UAAA,CAAAlI,CAAA;sBAAA,OAAAwI,SAAA,CAAAxI,CAAA;oBAAA;sBAGA;sBACA,IAAA5K,SAAA,CAAAoC,MAAA;wBACA4Q,QAAA,GAAAd,OAAA,CAAA0B,YAAA,KAAArM,mBAAA,CAAA/K,OAAA,EAAAwD,SAAA;wBACAsC,aAAA,GAAAuR,IAAA,CAAAC,GAAA,CAAAzS,UAAA,CAAAiB,aAAA,EAAA0Q,QAAA,CAAA5Q,MAAA;wBACAlC,iBAAA,GAAA8S,QAAA,CAAAe,KAAA,IAAAzR,aAAA,GAEA;wBACApC,iBAAA,CAAAgC,OAAA,WAAAgB,QAAA;0BACAA,QAAA,CAAApC,KAAA,GAAAO,UAAA,CAAAsG,gBAAA;wBACA;wBAEAyK,YAAA,CAAAjK,IAAA,CAAAsE,KAAA,CAAA2F,YAAA,MAAA7K,mBAAA,CAAA/K,OAAA,EAAA0D,iBAAA;sBACA;oBAAA;sBAAAkT,SAAA,CAAAjP,CAAA;sBAAA;oBAAA;sBAAAiP,SAAA,CAAAjG,CAAA;sBAAAgG,GAAA,GAAAC,SAAA,CAAA/F,CAAA;sBAGAC,OAAA,CAAAC,KAAA,YAAA4F,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAA/O,CAAA;kBAAA;gBAAA,GAAAkO,MAAA;cAAA;cAAAF,UAAA,CAAA7H,CAAA;YAAA;cAAA,KAAA8H,MAAA,GAAAD,UAAA,CAAAlO,CAAA,IAAAsG,IAAA;gBAAAgI,SAAA,CAAAtO,CAAA;gBAAA;cAAA;cAAA,OAAAsO,SAAA,CAAAhE,CAAA,KAAAC,mBAAA,CAAAlS,OAAA,EAAA+V,MAAA;YAAA;cAAAE,SAAA,CAAAtO,CAAA;cAAA;YAAA;cAAAsO,SAAA,CAAAtO,CAAA;cAAA;YAAA;cAAAsO,SAAA,CAAAtF,CAAA;cAAAqF,GAAA,GAAAC,SAAA,CAAApF,CAAA;cAAAgF,UAAA,CAAA1H,CAAA,CAAA6H,GAAA;YAAA;cAAAC,SAAA,CAAAtF,CAAA;cAAAkF,UAAA,CAAAzH,CAAA;cAAA,OAAA6H,SAAA,CAAA7H,CAAA;YAAA;cAAA,OAAA6H,SAAA,CAAApO,CAAA,IAIA+N,YAAA;UAAA;QAAA,GAAAD,QAAA;MAAA;IACA;IAEA,aACAyB,YAAA,WAAAA,aAAAI,KAAA;MACA,SAAAC,CAAA,GAAAD,KAAA,CAAA5R,MAAA,MAAA6R,CAAA,MAAAA,CAAA;QACA,IAAAC,CAAA,GAAAL,IAAA,CAAAM,KAAA,CAAAN,IAAA,CAAAO,MAAA,MAAAH,CAAA;QAAA,IAAAI,KAAA,GACA,CAAAL,KAAA,CAAAE,CAAA,GAAAF,KAAA,CAAAC,CAAA;QAAAD,KAAA,CAAAC,CAAA,IAAAI,KAAA;QAAAL,KAAA,CAAAE,CAAA,IAAAG,KAAA;MACA;MACA,OAAAL,KAAA;IACA;IAEA,SACAM,UAAA,WAAAA,WAAA;MACA,KAAA1P,QAAA,CAAAC,IAAA;IACA;IAEA,aACA0P,mBAAA,WAAAA,oBAAA;MACA,SAAA9V,cAAA,CAAA2D,MAAA;QACA,KAAAwC,QAAA,CAAAoC,OAAA;QACA;MACA;;MAEA;MACA,KAAApG,cAAA;QACAC,SAAA;QACAC,KAAA;QACAjB,YAAA;MACA;MAEA,KAAAc,oBAAA;IACA;IAEA,sBACA6T,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,SAAAhW,cAAA,CAAA2D,MAAA;QACA;MACA;MAEA,aAAAxB,cAAA,CAAAC,SAAA;QACA;UACA,YAAApC,cAAA,CAAA2D,MAAA;QACA;UACA,YAAA3D,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;YAAA,OAAAA,CAAA,CAAAH,QAAA;UAAA,GAAA1M,MAAA;QACA;UACA,UAAAxB,cAAA,CAAAf,YAAA;YACA;UACA;UACA,YAAApB,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;YAAA,OAAAA,CAAA,CAAA3S,IAAA,IAAAmY,OAAA,CAAA7T,cAAA,CAAAf,YAAA;UAAA,GAAAuC,MAAA;QACA;UACA;MACA;IACA;IAEA,eACAsS,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,aAAA,QAAAJ,0BAAA;MAEA,IAAAI,aAAA;QACA,KAAAhQ,QAAA,CAAAoC,OAAA;QACA;MACA;MAEA,IAAA6N,eAAA;MAEA,aAAAjU,cAAA,CAAAC,SAAA;QACA;UACAgU,eAAA,QAAApW,cAAA;UACA;QACA;UACAoW,eAAA,QAAApW,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;YAAA,OAAAA,CAAA,CAAAH,QAAA;UAAA;UACA,IAAA+F,eAAA,CAAAzS,MAAA;YACA,KAAAwC,QAAA,CAAAoC,OAAA;YACA;UACA;UACA;QACA;UACA,UAAApG,cAAA,CAAAf,YAAA;YACA,KAAA+E,QAAA,CAAAoC,OAAA;YACA;UACA;UACA6N,eAAA,QAAApW,cAAA,CAAAoD,MAAA,WAAAoN,CAAA;YAAA,OAAAA,CAAA,CAAA3S,IAAA,IAAAqY,OAAA,CAAA/T,cAAA,CAAAf,YAAA;UAAA;UACA;MACA;;MAEA;MACAgV,eAAA,CAAA3S,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAApC,KAAA,GAAA6T,OAAA,CAAA/T,cAAA,CAAAE,KAAA;MACA;MAEA,KAAAH,oBAAA;MACA,KAAAiE,QAAA,CAAAyC,OAAA,uBAAA+G,MAAA,CAAAwG,aAAA,wDAAAxG,MAAA,MAAAxN,cAAA,CAAAE,KAAA;IACA;IAEA,SACAgU,YAAA,WAAAA,aAAA;MACA,KAAAlQ,QAAA,CAAAC,IAAA;IACA;IAEA,YACAkQ,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAArW,UAAA,SAAAA,UAAA;MACA;MACA,KAAAF,cAAA,CAAAyD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAA+O,QAAA,GAAA+C,OAAA,CAAArW,UAAA;MACA;MACA,KAAAiG,QAAA,CAAAC,IAAA,UAAAuJ,MAAA,MAAAzP,UAAA;IACA;IAEA,aACAsW,6BAAA,WAAAA,8BAAAtM,SAAA;MACA,KAAAvJ,YAAA,CAAAc,iBAAA,GAAAyI,SAAA;MACA,KAAAuM,mBAAA;IACA;IAEA,eACAA,mBAAA,WAAAA,oBAAA;MACA,IAAAjS,KAAA;MACA,KAAA7D,YAAA,CAAAc,iBAAA,CAAAgC,OAAA,WAAAgB,QAAA;QACA,IAAA5G,IAAA,GAAA4G,QAAA,CAAArD,YAAA;QACAoD,KAAA,CAAA3G,IAAA,KAAA2G,KAAA,CAAA3G,IAAA;MACA;MACA,KAAA8C,YAAA,CAAAe,aAAA,GAAA8C,KAAA;IACA;IAEA,aACAkS,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,SAAAhW,YAAA,CAAAc,iBAAA,CAAAkC,MAAA;QACA,KAAAwC,QAAA,CAAAoC,OAAA;QACA;MACA;;MAEA;MACA,KAAA5H,YAAA,CAAAc,iBAAA,CAAAgC,OAAA,WAAAgB,QAAA,EAAA4E,KAAA;QACAsN,OAAA,CAAA3W,cAAA,CAAA0J,IAAA;UACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;UACAgK,UAAA,EAAA5O,QAAA,CAAA4O,UAAA;UACAC,OAAA,EAAA7O,QAAA,CAAA0N,eAAA;UACAtU,IAAA,EAAA4G,QAAA,CAAArD,YAAA;UACAC,UAAA,EAAAoD,QAAA,CAAApD,UAAA;UACAkS,OAAA,EAAA9O,QAAA,CAAA8O,OAAA;UAAA;UACAlR,KAAA;UAAA;UACAgO,QAAA;UACAmD,QAAA;QACA;MACA;MAEA,KAAArT,sBAAA;MACA,KAAAgG,QAAA,CAAAyC,OAAA,6BAAA+G,MAAA,MAAAhP,YAAA,CAAAc,iBAAA,CAAAkC,MAAA;IACA;IAEA,eACAiT,oBAAA,WAAAA,qBAAA7R,GAAA;MACA,KAAApE,YAAA,CAAAI,cAAA,CAAAE,QAAA,GAAA8D,GAAA;MACA,KAAApE,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAA4P,6BAAA;IACA;IAEA,cACAiG,uBAAA,WAAAA,wBAAA9R,GAAA;MACA,KAAApE,YAAA,CAAAI,cAAA,CAAAC,OAAA,GAAA+D,GAAA;MACA,KAAA6L,6BAAA;IACA;IAEA,eACAkG,wBAAA,WAAAA,yBAAA/R,GAAA;MACA,KAAApE,YAAA,CAAAa,kBAAA,CAAAP,QAAA,GAAA8D,GAAA;MACA,KAAApE,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAAiR,aAAA;IACA;IAEA,cACA8E,2BAAA,WAAAA,4BAAAhS,GAAA;MACA,KAAApE,YAAA,CAAAa,kBAAA,CAAAR,OAAA,GAAA+D,GAAA;MACA,KAAAkN,aAAA;IACA;IAEA,aACA+E,iBAAA,WAAAA,kBAAA3V,UAAA;MACA,IAAA4V,aAAA,OAAA7Y,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAkZ,aAAA,CAAA5V,UAAA;IACA;IAEA,aACA6V,mBAAA,WAAAA,oBAAAzS,QAAA;MACA;MACA,KAAA0S,YAAA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAA3S,QAAA;MACAA,QAAA,CAAA+O,QAAA,IAAA/O,QAAA,CAAA+O,QAAA;IACA;IAEA,aACA6D,cAAA,WAAAA,eAAA5S,QAAA,EAAA4E,KAAA;MAAA,IAAAiO,OAAA;MACA,KAAAlK,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzP,IAAA;MACA,GAAAuJ,IAAA;QACAkQ,OAAA,CAAAtX,cAAA,CAAAuK,MAAA,CAAAlB,KAAA;QACAiO,OAAA,CAAAnR,QAAA,CAAAyC,OAAA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,kBACAuP,YAAA,WAAAA,aAAAC,UAAA;MACA;QACA,WAAAA,UAAA;UACA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,UAAA;QACA;QACA,OAAAA,UAAA;MACA,SAAA1I,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACA;MACA;IACA;IAEA,gBACA6I,oBAAA,WAAAA,qBAAAzJ,GAAA;MACA;MACA,aAAAlO,cAAA,CAAA4X,IAAA,WAAAnT,QAAA;QAAA,OAAAA,QAAA,CAAA4O,UAAA,KAAAnF,GAAA,CAAAmF,UAAA;MAAA;IACA;IAEA,iBACAwE,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAA5J,GAAA,GAAA4J,KAAA,CAAA5J,GAAA;MACA;MACA,SAAAlO,cAAA,CAAA4X,IAAA,WAAAnT,QAAA;QAAA,OAAAA,QAAA,CAAA4O,UAAA,KAAAnF,GAAA,CAAAmF,UAAA;MAAA;QACA;MACA;MACA;IACA;IAEA,YACA0E,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAAna,IAAA,CAAAyM,OAAA;MACA,IAAA4N,MAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,OAAA;QACA,KAAA9R,QAAA,CAAA2I,KAAA;QACA;MACA;MACA,KAAAoJ,MAAA;QACA,KAAA/R,QAAA,CAAA2I,KAAA;QACA;MACA;MACA;IACA;IAEA,YACAsJ,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAAva,MAAA,CAAAoa,IAAA,CAAAI,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAA1a,MAAA,CAAAoa,IAAA,CAAAO,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAA5a,MAAA,CAAAoa,IAAA,CAAAS,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAA9a,MAAA,CAAAoa,IAAA,CAAAW,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAhb,MAAA,CAAAoa,IAAA,CAAAa,UAAA,IAAAR,QAAA;MACA,UAAA/I,MAAA,CAAA2I,IAAA,OAAA3I,MAAA,CAAA6I,KAAA,OAAA7I,MAAA,CAAAgJ,GAAA,OAAAhJ,MAAA,CAAAkJ,KAAA,OAAAlJ,MAAA,CAAAoJ,OAAA,OAAApJ,MAAA,CAAAsJ,OAAA;IACA;IAEA,aACAjU,aAAA,WAAAA,cAAA;MACA,SAAAhH,OAAA;QACA;QACA6Q,OAAA,CAAAa,GAAA,iBAAA1R,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}