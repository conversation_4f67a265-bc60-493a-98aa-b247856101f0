{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754445040100}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_questionBank", "require", "_category", "_question", "name", "props", "visible", "type", "Boolean", "default", "paperId", "String", "Number", "data", "_defineProperty2", "rightPanelCollapsed", "rightPanel<PERSON><PERSON>th", "activeCollapse", "paperForm", "paperName", "paperDesc", "paperType", "coverImg", "totalScore", "passScore", "startTime", "endTime", "duration", "lateLimit", "allowEarlySubmit", "earlySubmitTime", "showScore", "showType", "requireAllAnswered", "showResult", "showCorrect", "showAnswer", "showAnalysis", "status", "enableTimeLimit", "durationSeconds", "createTime", "fixedQuestions", "selectAll", "isExpanded", "showManualSelectDialog", "categoryOptions", "cascaderProps", "value", "label", "children", "checkStrictly", "emitPath", "manualSelect", "selectedCate<PERSON><PERSON>", "bankSearchKeyword", "questionBanks", "bankPagination", "pageNum", "pageSize", "total", "selectedBankId", "questionType", "difficulty", "questionSearchKeyword", "questions", "questionPagination", "selectedQuestions", "selectedStats", "showRuleDialog", "rules", "showAddRuleDialog", "ruleForm", "ruleType", "generateType", "selectedItems", "selectedQuestionTypes", "currentOperation", "editingRule", "parentRule", "currentQuestionTypeCount", "questionBankLoading", "bankName", "undefined", "computed", "selectedBanks", "_this", "filter", "bank", "includes", "bankId", "totalQuestions", "for<PERSON>ach", "rule", "length", "child", "selectedCount", "totalRuleScore", "parentRules", "parentId", "usedBankIds", "usedIds", "Set", "add", "Array", "from", "fixedQuestionStats", "stats", "question", "watch", "val", "loadPaperData", "handler", "newVal", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "updateQuestionTypeCount", "a", "deep", "methods", "handleBack", "$emit", "toggleRightPanel", "handleExamEntry", "$message", "info", "handlePageSetting", "handlePublish", "handleInviteStudents", "handleInviteList", "handleAddRule", "handleEditRules", "handleAddFirstRule", "queryParams", "searchKeyword", "loadQuestionBanks", "_this3", "Promise", "all", "listQuestionBank", "listCategory", "then", "_ref2", "_ref3", "_slicedToArray2", "bankResponse", "categoryResponse", "rows", "statisticsPromises", "map", "getQuestionStatistics", "questionCount", "totalCount", "catch", "banksWithStats", "handleSaveRules", "handleSaveRule", "_this4", "_callee2", "_context2", "warning", "updateExistingRule", "addNewRule", "success", "resetRuleForm", "_toConsumableArray2", "maxQuestions", "reduce", "sum", "scorePerQuestion", "_this5", "addSingleRule", "index", "id", "Date", "now", "updateRuleScore", "push", "$set", "handleSearch", "handleReset", "handleSelectionChange", "selection", "item", "removeSelectedBank", "_this6", "indexOf", "splice", "$nextTick", "table", "$refs", "questionBankTable", "rowToDeselect", "find", "toggleRowSelection", "handleCurrentChange", "page", "getCategoryName", "categoryId", "category", "findCategoryById", "categories", "flatCategories", "flattenCategories", "cat", "result", "flatten", "cats", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "err", "e", "f", "getQuestionTypeText", "questionTypes", "questionTypeMap", "isArray", "t", "join", "getChildRule<PERSON>abel", "childRule", "getRuleTypeLabel", "get<PERSON>ule<PERSON><PERSON>nt", "addSubRule", "defaultRuleType", "editRule", "_this7", "deleteRule", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "findIndex", "r", "deleteChildRule", "_this9", "getDialogTitle", "shouldShowRuleTypeSelection", "shouldShowRuleType", "shouldShowQuestionType", "existingQuestionTypes", "apply", "isRowSelectable", "row", "getRowClassName", "_ref4", "_this0", "_callee3", "count", "_t", "_context3", "p", "getQuestionCountByType", "v", "console", "error", "banks", "_callee4", "_iterator2", "_step2", "_loop", "_t3", "_context5", "response", "statistics", "_t2", "_context4", "log", "concat", "code", "singleChoice", "multipleChoice", "judgment", "d", "_regeneratorValues2", "shouldDisableRuleType", "handleSelectAll", "_this1", "selected", "handleDeleteSelected", "_this10", "q", "handleManualSelect", "initManualSelectData", "loadCategoryTree", "loadManualSelectQuestionBanks", "_this11", "buildCategoryTree", "_objectSpread2", "cleanEmptyChildren", "node", "getAllChildCategoryIds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this12", "categoryIds", "allCategories", "loadQuestionBanksByCategories", "_this13", "promises", "responses", "allBanks", "uniqueBanks", "self", "b", "searchQuestionBanks", "selectQuestionBank", "loadQuestions", "_this14", "questionContent", "listQuestion", "searchQuestions", "resetQuestionSearch", "handleRandomSelect", "handleSort", "handleBatchSetScore", "handleExport", "handleToggleExpand", "_this15", "expanded", "handleQuestionSelectionChange", "updateSelectedStats", "confirmManualSelect", "_this16", "questionId", "content", "options", "score", "handleBankSizeChange", "handleBankCurrentChange", "handleQuestionSizeChange", "handleQuestionCurrentChange", "getDifficultyText", "difficultyMap", "updateQuestionScore", "$forceUpdate", "toggleQuestionExpand", "deleteQuestion", "_this17", "parseOptions", "optionsStr", "JSON", "parse", "isQuestionSelectable", "some", "getQuestionRowClassName", "_ref5", "beforeUpload", "file", "isImage", "isLt2M", "size", "formatDate", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/biz/paper/create.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-editor\">\n    <!-- 左侧主要内容区域 -->\n    <div class=\"exam-editor-left\" :class=\"{ collapsed: rightPanelCollapsed }\">\n      <!-- 顶部操作栏 -->\n      <div class=\"editPaper_main_top\">\n        <div class=\"el-button-group\">\n          <el-button type=\"primary\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i>\n            <span>返回试卷</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button @click=\"handleExamEntry\">\n            <i class=\"el-icon-share\"></i>\n            <span>考试入口</span>\n          </el-button>\n          <el-button @click=\"handlePageSetting\">\n            <i class=\"el-icon-picture-outline\"></i>\n            <span>设置考试页面</span>\n          </el-button>\n          <el-button type=\"warning\" @click=\"handlePublish\">\n            <i class=\"el-icon-upload\"></i>\n            <span>发布</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button type=\"success\" @click=\"handleInviteStudents\">\n            邀请考生\n          </el-button>\n          <el-button type=\"warning\" @click=\"handleInviteList\">\n            已邀请列表\n          </el-button>\n        </div>\n        \n        <div class=\"clear_both\"></div>\n      </div>\n\n      <!-- 试卷信息卡片 -->\n      <div class=\"subject_main-wrapper\">\n        <div class=\"subject_main\">\n          <el-card class=\"is-hover-shadow\" style=\"width: 100%;\">\n            <div class=\"subtitle-title\" style=\"padding: 10px;\">\n              <div>\n                <div style=\"float: left;\">\n                  <strong class=\"slgfont\">{{ paperForm.paperName || '新建试卷' }}</strong>\n                </div>\n                <div style=\"float: right; color: #ec661a; width: 130px; text-align: right;\">\n                  <span style=\"font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;\">{{ totalRuleScore }}</span>分\n                </div>\n                <div style=\"clear: both;\"></div>\n                <div class=\"paper-count\" style=\"font-size: 13px; color: #aaa;\">\n                  <span v-if=\"paperForm.createTime\">创建时间：{{ paperForm.createTime }}</span>\n                  <el-tag type=\"warning\" size=\"medium\" effect=\"light\">共 {{ totalQuestions }} 题</el-tag>\n                  <el-tag type=\"success\" size=\"medium\" effect=\"light\">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>\n                  <el-tag size=\"medium\" effect=\"light\">自动出分</el-tag>\n                </div>\n                <div class=\"rich-text\" style=\"display: none;\"></div>\n              </div>\n              <div style=\"clear: both;\"></div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 题目设置区域 -->\n        <div>\n          <div class=\"mb10 mt10\">\n            <div class=\"el-button-group\"></div>\n          </div>\n          \n          <div v-if=\"paperForm.paperType === 1\">\n            <!-- 随机试卷 -->\n            <div class=\"random-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 有规则时显示规则内容 -->\n                <div v-if=\"rules.length > 0\" style=\"padding: 10px;\">\n                  <div class=\"tac pd5\">\n                    <div>\n                      <el-button type=\"primary\" @click=\"handleEditRules\">\n                        <i class=\"el-icon-edit\"></i>\n                        <span>编辑规则</span>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 规则显示区域 -->\n                  <div v-for=\"rule in parentRules\" :key=\"rule.id\" class=\"rule-display-container\">\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule-display\">\n                      <span class=\"rule-label\">{{ getRuleTypeLabel(rule) }}</span>\n                      <span class=\"rule-content\">{{ getRuleContent(rule) }}</span>\n                    </div>\n\n                    <!-- 子规则显示 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules-display\">\n                      <div v-for=\"childRule in rule.children\" :key=\"childRule.id\" class=\"child-rule-display\">\n                        <div class=\"child-rule-left\">\n                          <span class=\"rule-label\">{{ getRuleTypeLabel(childRule) }}</span>\n                          <span class=\"rule-content\">{{ getRuleContent(childRule) }}</span>\n                        </div>\n                        <div class=\"child-rule-right\">\n                          <span class=\"rule-stats\">\n                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，\n                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，\n                            总分 <strong>{{ childRule.totalScore }}</strong> 分\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 没有规则时显示添加按钮 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 10px;\">\n                  <div>\n                    <div class=\"mb10\">点击添加规则设置本试卷的抽题规则</div>\n                    <el-button type=\"primary\" @click=\"handleAddRule\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>添加规则</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n          \n          <div v-else>\n            <!-- 固定试卷 -->\n            <div class=\"fixed-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 操作栏 -->\n                <div class=\"mb10 mt10\">\n                  <div class=\"el-button-group\">\n                    <label class=\"el-checkbox fl el-checkbox--medium is-bordered\" style=\"padding: 7.5px 20px;\">\n                      <span class=\"el-checkbox__input\">\n                        <span class=\"el-checkbox__inner\"></span>\n                        <input type=\"checkbox\" aria-hidden=\"false\" class=\"el-checkbox__original\" value=\"\" v-model=\"selectAll\" @change=\"handleSelectAll\">\n                      </span>\n                      <span class=\"el-checkbox__label\">全选</span>\n                    </label>\n                    <el-tooltip content=\"删除选中题目\" placement=\"top\">\n                      <el-button type=\"danger\" size=\"medium\" @click=\"handleDeleteSelected\">\n                        <i class=\"el-icon-delete\"></i>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n\n                  <div class=\"el-button-group\">\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>手动选题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleRandomSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>随机抽题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleSort\">\n                      <i class=\"el-icon-sort\"></i>\n                      <span>排序</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleBatchSetScore\">\n                      <i class=\"icon_size iconfont icon-batch-add\"></i>\n                      <span>批量设置分数</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleExport\">\n                      <i class=\"icon_size iconfont icon-daochu\"></i>\n                      <span>导出</span>\n                    </el-button>\n                  </div>\n\n                  <el-button type=\"default\" size=\"medium\" style=\"vertical-align: middle;\" @click=\"handleToggleExpand\">\n                    <i class=\"icon_size iconfont icon-zhankai\"></i>\n                    <span>{{ isExpanded ? '收起' : '展开' }}</span>\n                  </el-button>\n                </div>\n\n                <!-- 题目列表区域 -->\n                <div v-if=\"fixedQuestions.length > 0\" class=\"question-list\">\n                  <!-- 这里将显示已添加的题目列表 -->\n                  <div class=\"question-item\" v-for=\"(question, index) in fixedQuestions\" :key=\"question.id\">\n                    <!-- 题目头部 -->\n                    <div class=\"question-header\">\n                      <div class=\"question-header-left\">\n                        <el-checkbox v-model=\"question.selected\"></el-checkbox>\n                        <span class=\"question-number\">{{ index + 1 }}.</span>\n                        <span class=\"question-content\">{{ question.content }}</span>\n                      </div>\n                      <div class=\"question-header-right\">\n                        <!-- 分数设置 -->\n                        <el-input-number\n                          v-model=\"question.score\"\n                          :min=\"0.5\"\n                          :step=\"0.5\"\n                          size=\"mini\"\n                          style=\"width: 100px;\"\n                          @change=\"updateQuestionScore(question)\"\n                        ></el-input-number>\n                        <span style=\"margin-left: 5px; margin-right: 10px;\">分</span>\n\n                        <!-- 展开/收起按钮 -->\n                        <el-tooltip :content=\"question.expanded ? '收起' : '展开'\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"toggleQuestionExpand(question)\"\n                            style=\"margin-right: 5px;\"\n                          >\n                            <i :class=\"question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                          </el-button>\n                        </el-tooltip>\n\n                        <!-- 删除按钮 -->\n                        <el-tooltip content=\"删除\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"deleteQuestion(question, index)\"\n                            style=\"color: #f56c6c;\"\n                          >\n                            <i class=\"el-icon-delete\"></i>\n                          </el-button>\n                        </el-tooltip>\n                      </div>\n                    </div>\n\n                    <!-- 题目详情（展开时显示） -->\n                    <div v-if=\"question.expanded\" class=\"question-details\">\n                      <div class=\"question-info\">\n                        <span class=\"info-item\">题型：{{ getQuestionTypeText(question.type) }}</span>\n                        <span class=\"info-item\">难度：{{ getDifficultyText(question.difficulty) }}</span>\n                      </div>\n\n                      <!-- 选项显示 -->\n                      <div v-if=\"question.options\" class=\"question-options\">\n                        <div class=\"options-title\">选项：</div>\n                        <div\n                          v-for=\"(option, optIndex) in parseOptions(question.options)\"\n                          :key=\"optIndex\"\n                          class=\"option-item\"\n                          :class=\"{ 'correct-option': option.isCorrect }\"\n                        >\n                          <span class=\"option-key\">{{ option.key }}.</span>\n                          <span class=\"option-content\">{{ option.content }}</span>\n                          <span v-if=\"option.isCorrect\" class=\"correct-mark\">✓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 40px 10px;\">\n                  <div>\n                    <div class=\"mb10\">暂无题目，点击上方按钮添加题目到试卷中</div>\n                    <el-button type=\"primary\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>开始添加题目</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧设置面板 -->\n    <div class=\"exam-editor-right\" :style=\"{ width: rightPanelWidth + 'px' }\">\n      <i \n        :class=\"rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'\" \n        class=\"collapse-button\" \n        title=\"收起/展开\"\n        @click=\"toggleRightPanel\"\n      ></i>\n      \n      <div v-show=\"!rightPanelCollapsed\">\n        <div class=\"editor-header editor-header--big\">\n          <span>考试设置</span>\n          <i class=\"el-icon-close close-button\" @click=\"handleBack\" title=\"关闭\"></i>\n        </div>\n        \n        <div class=\"main\">\n          <el-form class=\"h100p\">\n            <el-collapse v-model=\"activeCollapse\" class=\"main-collapse h100p\" accordion>\n              <!-- 基础设置 -->\n              <el-collapse-item title=\"基础设置\" name=\"basic\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-setting\" style=\"color: #67c23a;\"></i>基础设置\n                  </div>\n                </template>\n\n                <div class=\"main-module\">\n                  <!-- 封面图 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>封面图</b>\n                      <div class=\"input-area\">\n                        <div style=\"margin-top: 20px;\">\n                          <span class=\"dpib\" style=\"width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;\">\n                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2\n                          </span>\n                          <div class=\"g-component-cover-uploader dpib\">\n                            <div class=\"avatar-uploader\" style=\"width: 120px; height: 80px;\">\n                              <el-upload\n                                class=\"avatar-uploader\"\n                                action=\"#\"\n                                :show-file-list=\"false\"\n                                :before-upload=\"beforeUpload\"\n                                accept=\".png, .jpeg, .jpg\"\n                              >\n                                <div class=\"image_area\">\n                                  <img v-if=\"paperForm.coverImg\" :src=\"paperForm.coverImg\" class=\"avatar\">\n                                  <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                                </div>\n                              </el-upload>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷名称 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷名称</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperName\"\n                          placeholder=\"请输入试卷名称\"\n                          maxlength=\"100\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷描述 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷描述</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperDesc\"\n                          type=\"textarea\"\n                          :rows=\"3\"\n                          placeholder=\"请输入试卷描述\"\n                          maxlength=\"500\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 出题方式 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>出题方式</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"paper-type-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">出题方式</div>\n                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>\n                            <br>\n                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-radio-group v-model=\"paperForm.paperType\" size=\"mini\">\n                          <el-radio-button :label=\"1\">随机试卷</el-radio-button>\n                          <el-radio-button :label=\"0\">固定试卷</el-radio-button>\n                        </el-radio-group>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n                  <!-- 时长 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>时长(按卷限时)</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">时长</div>\n                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>\n                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.duration\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                        <el-input-number v-model=\"paperForm.durationSeconds\" :min=\"0\" :max=\"59\" size=\"mini\" style=\"width: 85px;\"></el-input-number> 秒\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 及格分 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>及格分</b>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.passScore\" :min=\"0\" :max=\"paperForm.totalScore || 100\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>考试时间</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">考试时间</div>\n                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.enableTimeLimit\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间子项容器 -->\n                  <div class=\"block-expand\" v-if=\"paperForm.enableTimeLimit\">\n                    <!-- 考试开始时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试开始时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.startTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择开始时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 考试结束时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试结束时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.endTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择结束时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n\n                  </div>\n\n                  <!-- 提前交卷 -->\n                  <div class=\"setting-block\" v-if=\"paperForm.enableTimeLimit\">\n                    <div class=\"line block-header\">\n                      <b>提前交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch\n                          v-model=\"paperForm.allowEarlySubmit\"\n                          :active-value=\"1\"\n                          :inactive-value=\"0\"\n                        ></el-switch>\n                        <span v-if=\"paperForm.allowEarlySubmit\" style=\"margin-left: 10px;\">\n                          <el-input-number\n                            v-model=\"paperForm.earlySubmitTime\"\n                            :min=\"1\"\n                            :max=\"60\"\n                            size=\"mini\"\n                            style=\"width: 100px;\"\n                          ></el-input-number> 分钟\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示分值 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示分值</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showScore\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示题型 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示题型</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showType\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 迟到限制 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>迟到限制</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"late-limit-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">迟到限制</div>\n                            <div>设置迟到多少分钟后，禁止再进入考试。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.lateLimit\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分钟\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试中设置 -->\n              <el-collapse-item title=\"考试中\" name=\"during\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-edit-outline\" style=\"color: #409eff;\"></i>考试中\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n\n\n                  <!-- 全部答完才能交卷 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>全部答完才能交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.requireAllAnswered\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试后设置 -->\n              <el-collapse-item title=\"考试后\" name=\"after\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-finished\" style=\"color: #e6a23c;\"></i>考试后\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n                  <!-- 显示成绩 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示成绩</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showResult\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示对错 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示对错</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showCorrect\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示答案 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示答案</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnswer\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示解析 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示解析</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnalysis\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </el-form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 随机规则设置对话框 -->\n    <el-dialog\n      title=\"随机规则设置\"\n      :visible.sync=\"showRuleDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"新版规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFirstRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加一级规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ totalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ totalRuleScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"确定一级规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      按题库、按题型、按难度、按知识点抽题四选一\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"添加子规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      在每个上级规则基础上继续添加子规则\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"保存抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      各级规则设置完成后即可保存规则开始抽题\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"rules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in parentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"addSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}\n                          </div>\n                          <div class=\"rule-content\" v-if=\"childRule.type === 3\">\n                            <span>\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"rules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加一级规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showRuleDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleSaveRules\">保存规则</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 手动选题对话框 -->\n    <el-dialog\n      title=\"选择题目\"\n      :visible.sync=\"showManualSelectDialog\"\n      width=\"1200px\"\n      :close-on-click-modal=\"false\"\n      class=\"checked-question\"\n      top=\"15vh\"\n      :append-to-body=\"true\"\n      :z-index=\"3200\"\n    >\n      <div style=\"display: flex; height: 550px;\">\n        <!-- 左侧：题库列表 -->\n        <div style=\"width: 300px; padding-right: 10px;\">\n          <!-- 搜索区域 -->\n          <div style=\"padding: 5px; height: 42px; display: flex; gap: 10px;\">\n            <el-cascader\n              v-model=\"manualSelect.selectedCategory\"\n              :options=\"categoryOptions\"\n              :props=\"cascaderProps\"\n              placeholder=\"选择题库分类\"\n              size=\"small\"\n              style=\"width: 135px;\"\n              clearable\n              @change=\"searchQuestionBanks\"\n            ></el-cascader>\n\n            <el-input\n              v-model=\"manualSelect.bankSearchKeyword\"\n              placeholder=\"题库名称\"\n              size=\"small\"\n              style=\"width: 150px;\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchQuestionBanks\"></el-button>\n            </el-input>\n          </div>\n\n          <!-- 题库表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questionBanks\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @row-click=\"selectQuestionBank\"\n              highlight-current-row\n            >\n              <el-table-column type=\"index\" width=\"38\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"left\" header-align=\"center\"></el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding-top: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleBankSizeChange\"\n              @current-change=\"handleBankCurrentChange\"\n              :current-page=\"manualSelect.bankPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              :page-size=\"manualSelect.bankPagination.pageSize\"\n              :total=\"manualSelect.bankPagination.total\"\n              layout=\"prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 中间：题目列表 -->\n        <div style=\"width: 700px; padding: 0px 5px;\">\n          <!-- 筛选区域 -->\n          <div style=\"padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;\">\n            <el-select\n              v-model=\"manualSelect.questionType\"\n              placeholder=\"题型\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部题型\" value=\"\"></el-option>\n              <el-option label=\"单选题\" value=\"1\"></el-option>\n              <el-option label=\"多选题\" value=\"2\"></el-option>\n              <el-option label=\"判断题\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-select\n              v-model=\"manualSelect.difficulty\"\n              placeholder=\"难度\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部难度\" value=\"\"></el-option>\n              <el-option label=\"低\" value=\"1\"></el-option>\n              <el-option label=\"中\" value=\"2\"></el-option>\n              <el-option label=\"高\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-input\n              v-model=\"manualSelect.questionSearchKeyword\"\n              placeholder=\"搜索题目\"\n              size=\"small\"\n              style=\"width: 250px;\"\n            >\n              <template slot=\"append\">\n                <el-button @click=\"searchQuestions\" style=\"border-left: 1px solid #dcdfe6;\">搜索</el-button>\n                <el-button @click=\"resetQuestionSearch\" style=\"border-left: 1px solid #dcdfe6;\">重置</el-button>\n              </template>\n            </el-input>\n          </div>\n\n          <!-- 题目表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questions\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @selection-change=\"handleQuestionSelectionChange\"\n              :row-class-name=\"getQuestionRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"40\" :selectable=\"isQuestionSelectable\"></el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题干\" min-width=\"400\" header-align=\"center\" class-name=\"question-content-column\">\n                <template slot-scope=\"scope\">\n                  <div class=\"question-content-text\">\n                    {{ scope.row.questionContent }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionType\" label=\"题目类型\" width=\"78\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getQuestionTypeText(scope.row.questionType) }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"50\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getDifficultyText(scope.row.difficulty) }}\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleQuestionSizeChange\"\n              @current-change=\"handleQuestionCurrentChange\"\n              :current-page=\"manualSelect.questionPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 40, 50, 100]\"\n              :page-size=\"manualSelect.questionPagination.pageSize\"\n              :total=\"manualSelect.questionPagination.total\"\n              layout=\"sizes, prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 右侧：统计信息 -->\n        <div style=\"width: 150px; padding: 0px 5px;\">\n          <div style=\"padding: 7px 0px; height: 42px; font-size: 16px;\">试卷题型统计</div>\n          <div style=\"border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;\">\n            <!-- 显示已添加到试卷的题目统计 -->\n            <div v-for=\"(count, type) in fixedQuestionStats\" :key=\"type\" style=\"margin-bottom: 10px;\">\n              <div style=\"font-size: 14px; color: #606266;\">\n                {{ getQuestionTypeText(type) }}：{{ count }} 题\n              </div>\n            </div>\n\n            <!-- 显示当前选择的题目统计 -->\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;\">\n              <div style=\"font-size: 13px; color: #909399; margin-bottom: 8px;\">本次选择：</div>\n              <div v-for=\"(count, type) in manualSelect.selectedStats\" :key=\"'selected-' + type\" style=\"margin-bottom: 8px;\">\n                <div style=\"font-size: 13px; color: #409eff;\">\n                  {{ getQuestionTypeText(type) }}：{{ count }} 题\n                </div>\n              </div>\n            </div>\n          </div>\n          <div style=\"width: 140px; text-align: right; padding-right: 5px;\">\n            <div style=\"font-size: 14px; font-weight: bold;\">总题数：{{ fixedQuestions.length }} 题</div>\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"font-size: 12px; color: #409eff;\">\n              +{{ manualSelect.selectedQuestions.length }} 题\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showManualSelectDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"confirmManualSelect\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加规则对话框 -->\n    <el-dialog\n      :title=\"getDialogTitle()\"\n      :visible.sync=\"showAddRuleDialog\"\n      width=\"900px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3100\"\n    >\n      <div style=\"margin-bottom: -30px;\">\n        <el-form :model=\"ruleForm\" label-width=\"140px\">\n          <!-- 选择规则类型 -->\n          <el-form-item v-if=\"shouldShowRuleTypeSelection()\" label=\"选择规则类型\">\n            <el-radio-group v-model=\"ruleForm.ruleType\">\n              <el-radio\n                v-if=\"shouldShowRuleType(3)\"\n                :label=\"3\"\n                :disabled=\"shouldDisableRuleType(3)\"\n              >题库</el-radio>\n              <el-radio\n                v-if=\"shouldShowRuleType(1)\"\n                :label=\"1\"\n                :disabled=\"shouldDisableRuleType(1)\"\n              >题型</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 规则生成方式 -->\n          <el-form-item v-if=\"currentOperation !== 'addSub'\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                规则生成方式\n              </div>\n            </template>\n            <el-radio-group v-model=\"ruleForm.generateType\">\n              <el-radio label=\"divided\">分开设置</el-radio>\n              <el-radio label=\"one\">整体设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 已选择的题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"点击选项可取消选择。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                已选择的题库\n              </div>\n            </template>\n            <span v-if=\"ruleForm.selectedItems.length === 0\" class=\"ml20\">\n              暂无选择，请至少选择一项。\n            </span>\n            <div v-else>\n              <el-tag\n                v-for=\"item in selectedBanks\"\n                :key=\"item.bankId\"\n                closable\n                @close=\"removeSelectedBank(item.bankId)\"\n                type=\"info\"\n                size=\"medium\"\n                class=\"selected-bank-tag\"\n              >\n                {{ item.bankName }}\n              </el-tag>\n            </div>\n          </el-form-item>\n\n          <!-- 选择题型 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 1\" label=\"选择题型\">\n            <el-checkbox-group v-model=\"ruleForm.selectedQuestionTypes\">\n              <el-checkbox v-if=\"shouldShowQuestionType('1')\" label=\"1\">单选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('2')\" label=\"2\">多选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('3')\" label=\"3\">判断题</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n\n\n\n          <!-- 选择题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\" label=\"选择题库\">\n            <!-- 搜索区域 -->\n            <div class=\"clearfix\" style=\"margin-bottom: 6px;\">\n              <div style=\"float: right; display: flex; align-items: center;\">\n                <el-input\n                  v-model=\"searchKeyword\"\n                  placeholder=\"题库名称\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 8px;\"\n                  @keyup.enter.native=\"handleSearch\"\n                  clearable\n                >\n                  <el-button slot=\"append\" @click=\"handleSearch\">搜索</el-button>\n                </el-input>\n                <el-button size=\"small\" @click=\"handleReset\">重置</el-button>\n              </div>\n            </div>\n\n            <!-- 题库表格 -->\n            <el-table\n              ref=\"questionBankTable\"\n              :data=\"questionBanks\"\n              border\n              size=\"small\"\n              max-height=\"300\"\n              v-loading=\"questionBankLoading\"\n              @selection-change=\"handleSelectionChange\"\n              :row-class-name=\"getRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"40\" label=\"#\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" :selectable=\"isRowSelectable\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"questionCount\" label=\"题目数量\" width=\"80\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.questionCount || 0 }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分类\" width=\"150\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getCategoryName(scope.row.categoryId) }}\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <!-- 分页 -->\n            <div style=\"text-align: right; margin-top: 10px;\">\n              <el-pagination\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"queryParams.pageNum\"\n                :page-size=\"queryParams.pageSize\"\n                layout=\"total, prev, pager, next\"\n                :total=\"total\"\n                small\n                background\n              >\n              </el-pagination>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showAddRuleDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSaveRule\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n/* 表格垂直对齐优化 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {\n  vertical-align: middle;\n}\n\n/* 多选框垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n/* 序号列垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n/* 规则节点样式 */\n.rule-node-cascade {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.rule-node-cascade:hover {\n  border: 1px dashed #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n/* 左侧题库信息 */\n.rule-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.rule-type-label {\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n  min-width: 50px;\n}\n\n.rule-content {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 右侧操作区域 */\n.rule-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rule-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.control-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  white-space: nowrap;\n}\n\n.control-divider {\n  color: #999;\n  margin: 0 4px;\n}\n\n.total-score {\n  font-weight: 600;\n  color: #333;\n}\n\n.input-number-mini {\n  width: 110px !important;\n}\n\n.rule-actions {\n  display: flex;\n  gap: 2px;\n}\n\n.action-btn {\n  color: #409eff !important;\n  padding: 4px 6px !important;\n  margin-left: 0 !important;\n}\n\n.action-btn:hover {\n  background-color: #ecf5ff !important;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n/* 子规则样式 */\n.children-rules {\n  margin-top: 12px;\n  margin-left: 20px;\n  border-left: 2px dashed #e8e8e8;\n  padding-left: 20px;\n}\n\n.children_component {\n  margin-bottom: 8px !important;\n  border: 1px dashed #d9d9d9 !important;\n  background-color: #f9f9f9 !important;\n}\n\n.children_component:hover {\n  border: 1px dashed #409eff !important;\n  background-color: #ecf5ff !important;\n}\n\n.parent-rule {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 禁用的表格行样式 */\n::v-deep .disabled-row {\n  background-color: #f5f5f5 !important;\n  color: #c0c4cc !important;\n}\n\n::v-deep .disabled-row:hover {\n  background-color: #f5f5f5 !important;\n}\n\n::v-deep .disabled-row td {\n  color: #c0c4cc !important;\n}\n\n/* random-paper 规则显示样式 */\n.rule-display-container {\n  border: 1px dashed #d0d0d0;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #fafbfc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 题库容器悬停效果 */\n.rule-display-container:hover {\n  border-color: #409eff;\n  background-color: #f8fbff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.rule-display-container:hover .parent-rule-display {\n  background-color: #e3f2fd;\n  border-left-color: #1976d2;\n}\n\n.rule-display-container:hover .parent-rule-display .rule-label {\n  color: #1976d2;\n}\n\n.parent-rule-display {\n  font-size: 14px;\n  color: #303133;\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n  border-left: 4px solid #409eff;\n}\n\n.children-rules-display {\n  margin-left: 20px;\n}\n\n.child-rule-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px;\n  margin-bottom: 8px;\n  background-color: #ffffff;\n  border: 1px dashed #d0d0d0;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.child-rule-display:last-child {\n  margin-bottom: 0;\n}\n\n/* 子规则悬停效果 */\n.child-rule-display:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.child-rule-display:hover .rule-label {\n  color: #1976d2;\n}\n\n.child-rule-display:hover .rule-stats {\n  background-color: #e3f2fd;\n  border-color: #90caf9;\n}\n\n.child-rule-left {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.child-rule-right {\n  flex-shrink: 0;\n  margin-left: 20px;\n}\n\n.rule-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.rule-content {\n  color: #303133;\n}\n\n.rule-stats {\n  font-size: 13px;\n  color: #606266;\n  background-color: #f0f9ff;\n  padding: 4px 8px;\n  border-radius: 5px;\n  border: 1px solid #b3d8ff;\n  white-space: nowrap;\n}\n\n.rule-stats strong {\n  color: #409eff;\n  font-weight: 600;\n}\n\n/* 固定试卷样式 */\n.question-list {\n  margin-top: 20px;\n}\n\n.question-item {\n  margin-bottom: 12px;\n  background-color: #fafafa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.question-item:hover {\n  background-color: #f0f9ff;\n  border-color: #409eff;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 15px;\n}\n\n.question-header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.question-header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.question-number {\n  font-weight: bold;\n  color: #409eff;\n  margin-left: 10px;\n  margin-right: 10px;\n  min-width: 30px;\n}\n\n.question-content {\n  flex: 1;\n  color: #303133;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-right: 15px;\n}\n\n.question-details {\n  border-top: 1px solid #e4e7ed;\n  padding: 15px;\n  background-color: #ffffff;\n}\n\n.question-info {\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: inline-block;\n  margin-right: 20px;\n  font-size: 13px;\n  color: #606266;\n}\n\n.question-options {\n  margin-top: 10px;\n}\n\n.options-title {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.option-item.correct-option {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.option-key {\n  font-weight: bold;\n  margin-right: 8px;\n  min-width: 20px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n.el-button-group {\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.icon_size {\n  font-size: 14px;\n}\n\n/* 禁用题目行样式 */\n.disabled-question-row {\n  background-color: #f5f7fa !important;\n  color: #c0c4cc !important;\n}\n\n.disabled-question-row:hover {\n  background-color: #f5f7fa !important;\n}\n\n.disabled-question-row td {\n  color: #c0c4cc !important;\n}\n\n/* 题干内容左对齐样式 */\n.question-content-text {\n  max-height: 60px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: left !important;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n/* 强制题干列内容左对齐 */\n.el-table .question-content-column {\n  text-align: left !important;\n}\n\n.el-table .question-content-column .cell {\n  text-align: left !important;\n  padding-left: 10px !important;\n  padding-right: 10px !important;\n}\n\n/* 已选择题库标签样式 */\n.selected-bank-tag {\n  margin-right: 8px !important;\n  margin-bottom: 4px !important;\n  font-size: 13px !important;\n}\n\n/* 修复标签关闭按钮样式 */\n::v-deep .selected-bank-tag .el-tag__close {\n  color: #909399 !important;\n  font-size: 12px !important;\n  margin-left: 6px !important;\n  cursor: pointer !important;\n  border-radius: 50% !important;\n  width: 16px !important;\n  height: 16px !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  line-height: 1 !important;\n  vertical-align: middle !important;\n}\n\n::v-deep .selected-bank-tag .el-tag__close:hover {\n  background-color: #909399 !important;\n  color: #fff !important;\n}\n</style>\n\n<script>\nimport { listQuestionBank } from \"@/api/biz/questionBank\"\nimport { listCategory } from \"@/api/biz/category\"\nimport { getQuestionStatistics, listQuestion } from \"@/api/biz/question\"\n\nexport default {\n  name: \"PaperCreate\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    paperId: {\n      type: [String, Number],\n      default: null\n    }\n  },\n  data() {\n    return {\n      // 右侧面板状态\n      rightPanelCollapsed: false,\n      rightPanelWidth: 410,\n      \n      // 折叠面板激活项（accordion模式下为字符串）\n      activeCollapse: 'basic',\n      \n      // 试卷表单数据\n      paperForm: {\n        paperId: null,\n        paperName: '',\n        paperDesc: '',\n        paperType: 1, // 0: 固定试卷, 1: 随机试卷\n        coverImg: '',\n        totalScore: 100,\n        passScore: 60,\n        startTime: null,\n        endTime: null,\n        duration: 90, // 考试时长，分钟\n        lateLimit: 0, // 迟到限制，分钟\n        allowEarlySubmit: 0, // 是否允许提前交卷\n        earlySubmitTime: 40, // 提前交卷时间，分钟\n        showScore: 0, // 是否显示分值\n        showType: 0, // 是否显示题型\n        requireAllAnswered: 0, // 是否要求全部答完才能交卷\n        showResult: 0, // 是否显示成绩\n        showCorrect: 0, // 是否显示对错\n        showAnswer: 0, // 是否显示答案\n        showAnalysis: 0, // 是否显示解析\n        status: 0, // 状态：0未发布 1已发布\n        enableTimeLimit: 0, // 是否启用考试时间限制\n        durationSeconds: 0, // 时长秒数\n        createTime: null\n      },\n      \n      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）\n\n      // 固定试卷相关数据\n      fixedQuestions: [], // 固定试卷的题目列表\n      selectAll: false, // 全选状态\n      isExpanded: false, // 展开状态\n\n      // 手动选题对话框\n      showManualSelectDialog: false,\n      categoryOptions: [], // 分类选项数据\n      cascaderProps: {\n        value: 'id',\n        label: 'name',\n        children: 'children',\n        checkStrictly: false,\n        emitPath: false\n      },\n      manualSelect: {\n        selectedCategory: '', // 选择的题库目录\n        bankSearchKeyword: '', // 题库搜索关键词\n        questionBanks: [], // 题库列表\n        bankPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedBankId: null, // 选择的题库ID\n        questionType: '', // 题型筛选\n        difficulty: '', // 难度筛选\n        questionSearchKeyword: '', // 题目搜索关键词\n        questions: [], // 题目列表\n        questionPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedQuestions: [], // 选中的题目\n        selectedStats: {} // 选中题目的统计信息\n      },\n\n      // 随机规则对话框\n      showRuleDialog: false,\n      // 规则列表\n      rules: [],\n\n      // 添加规则对话框\n      showAddRuleDialog: false,\n      ruleForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n      // 当前操作状态\n      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则\n      editingRule: null, // 正在编辑的规则\n      parentRule: null, // 父规则（添加子规则时使用）\n      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量\n      questionBanks: [],\n      questionBankLoading: false,\n      categoryOptions: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n    }\n  },\n  computed: {\n    // 已选择的题库\n    selectedBanks() {\n      return this.questionBanks.filter(bank =>\n        this.ruleForm.selectedItems.includes(bank.bankId)\n      )\n    },\n\n    // 计算总题目数量\n    totalQuestions() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的题目数\n          rule.children.forEach(child => {\n            total += child.selectedCount || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的题目数\n          total += rule.selectedCount || 0\n        }\n      })\n      return total\n    },\n\n    // 计算总分数\n    totalRuleScore() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的总分\n          rule.children.forEach(child => {\n            total += child.totalScore || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的总分\n          total += rule.totalScore || 0\n        }\n      })\n      return total\n    },\n\n    // 获取父规则列表（只包含没有parentId的规则）\n    parentRules() {\n      return this.rules.filter(rule => !rule.parentId)\n    },\n\n    // 获取已使用的题库ID列表\n    usedBankIds() {\n      const usedIds = new Set()\n      this.rules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    },\n\n    // 计算已添加到试卷的题目统计\n    fixedQuestionStats() {\n      const stats = {}\n      this.fixedQuestions.forEach(question => {\n        const type = question.type\n        stats[type] = (stats[type] || 0) + 1\n      })\n      return stats\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val && this.paperId) {\n        this.loadPaperData()\n      }\n    },\n\n    // 监听题型选择变化\n    'ruleForm.selectedQuestionTypes': {\n      async handler(newVal) {\n        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {\n          await this.updateQuestionTypeCount()\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 返回试卷列表 */\n    handleBack() {\n      this.$emit('close')\n    },\n    \n    /** 切换右侧面板 */\n    toggleRightPanel() {\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\n      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400\n    },\n    \n    /** 考试入口 */\n    handleExamEntry() {\n      this.$message.info('考试入口功能开发中...')\n    },\n    \n    /** 设置考试页面 */\n    handlePageSetting() {\n      this.$message.info('设置考试页面功能开发中...')\n    },\n    \n    /** 发布试卷 */\n    handlePublish() {\n      this.$message.info('发布试卷功能开发中...')\n    },\n    \n    /** 邀请考生 */\n    handleInviteStudents() {\n      this.$message.info('邀请考生功能开发中...')\n    },\n    \n    /** 已邀请列表 */\n    handleInviteList() {\n      this.$message.info('已邀请列表功能开发中...')\n    },\n    \n\n    \n    /** 添加规则（随机试卷） */\n    handleAddRule() {\n      this.showRuleDialog = true\n    },\n\n    /** 编辑规则（打开规则编辑界面） */\n    handleEditRules() {\n      this.showRuleDialog = true\n    },\n\n    /** 添加一级规则 */\n    handleAddFirstRule() {\n      this.currentOperation = 'add'\n      this.editingRule = null\n\n      // 如果已有规则，限制只能选择相同类型\n      if (this.rules.length > 0) {\n        this.ruleForm.ruleType = this.rules[0].type\n      } else {\n        this.ruleForm.ruleType = 3 // 默认题库\n      }\n\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 加载题库列表 */\n    loadQuestionBanks() {\n      this.questionBankLoading = true\n      // 同时加载题库和分类数据\n      Promise.all([\n        listQuestionBank(this.queryParams),\n        listCategory({ pageSize: 1000 })\n      ]).then(([bankResponse, categoryResponse]) => {\n        const questionBanks = bankResponse.rows || []\n        this.total = bankResponse.total || 0\n        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []\n\n        // 为每个题库获取题目统计\n        const statisticsPromises = questionBanks.map(bank =>\n          getQuestionStatistics(bank.bankId).then(stats => {\n            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0\n            return bank\n          }).catch(() => {\n            bank.questionCount = 0\n            return bank\n          })\n        )\n\n        Promise.all(statisticsPromises).then(banksWithStats => {\n          this.questionBanks = banksWithStats\n          this.questionBankLoading = false\n        })\n      }).catch(() => {\n        this.questionBankLoading = false\n      })\n    },\n\n    /** 保存规则 */\n    handleSaveRules() {\n      this.$message.info('保存规则功能开发中...')\n      this.showRuleDialog = false\n    },\n\n    /** 保存单个规则 */\n    async handleSaveRule() {\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {\n        this.$message.warning('请至少选择一个题库')\n        return\n      }\n      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {\n        this.$message.warning('请至少选择一个题型')\n        return\n      }\n\n      // 题型规则验证：检查是否有可用题目\n      if (this.ruleForm.ruleType === 1) {\n        if (this.currentQuestionTypeCount === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')\n          return\n        }\n      }\n\n      if (this.currentOperation === 'edit') {\n        // 编辑现有规则\n        this.updateExistingRule()\n      } else {\n        // 添加新规则（包括添加子规则）\n        this.addNewRule()\n      }\n\n      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')\n      this.showAddRuleDialog = false\n\n      // 重置表单\n      this.resetRuleForm()\n    },\n\n    /** 更新现有规则 */\n    updateExistingRule() {\n      const rule = this.editingRule\n      rule.type = this.ruleForm.ruleType\n      rule.generateType = this.ruleForm.generateType\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n\n        // 重新计算总分，确保选取数量不超过最大题目数\n        if (rule.selectedCount > rule.maxQuestions) {\n          rule.selectedCount = rule.maxQuestions\n        }\n        rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n    },\n\n    /** 添加新规则 */\n    addNewRule() {\n      // 如果是添加子规则，按原逻辑处理\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        this.addSingleRule()\n        return\n      }\n\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index, // 确保每个规则有唯一ID\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 0.5,\n            totalScore: 0.5,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateRuleScore(rule)\n          this.rules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleRule()\n      }\n    },\n\n    /** 添加单个规则 */\n    addSingleRule() {\n      const rule = {\n        id: Date.now(), // 临时ID\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1, // 默认选取1题\n        scorePerQuestion: 0.5, // 默认每题0.5分\n        totalScore: 0.5, // 默认总分0.5分\n        maxQuestions: 0 // 最大题目数\n      }\n\n      // 如果是添加子规则\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        rule.parentId = this.parentRule.id\n\n        // 确保父规则有children数组\n        if (!this.parentRule.children) {\n          this.$set(this.parentRule, 'children', [])\n        }\n\n        // 添加到父规则的children中\n        this.parentRule.children.push(rule)\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n\n      // 只有父规则才添加到主规则列表\n      if (this.currentOperation !== 'addSub') {\n        this.rules.push(rule)\n      }\n\n      // 更新规则分数\n      this.updateRuleScore(rule)\n    },\n\n    /** 重置规则表单 */\n    resetRuleForm() {\n      this.ruleForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n      this.currentOperation = 'add'\n      this.editingRule = null\n      this.parentRule = null\n      this.currentQuestionTypeCount = 0\n    },\n\n    /** 搜索题库 */\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = this.searchKeyword || undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 重置搜索 */\n    handleReset() {\n      this.searchKeyword = ''\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 表格选择变化 */\n    handleSelectionChange(selection) {\n      this.ruleForm.selectedItems = selection.map(item => item.bankId)\n    },\n\n    /** 移除已选择的题库 */\n    removeSelectedBank(bankId) {\n      const index = this.ruleForm.selectedItems.indexOf(bankId)\n      if (index > -1) {\n        this.ruleForm.selectedItems.splice(index, 1)\n      }\n\n      // 同步取消表格中的勾选状态\n      this.$nextTick(() => {\n        const table = this.$refs.questionBankTable\n        if (table) {\n          // 找到对应的行数据\n          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)\n          if (rowToDeselect) {\n            table.toggleRowSelection(rowToDeselect, false)\n          }\n        }\n      })\n    },\n\n    /** 分页变化 */\n    handleCurrentChange(page) {\n      this.queryParams.pageNum = page\n      this.loadQuestionBanks()\n    },\n\n    /** 根据分类ID获取分类名称 */\n    getCategoryName(categoryId) {\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\n      return category ? category.name : '未分类'\n    },\n\n    /** 在分类数据中查找分类（支持扁平和树形结构） */\n    findCategoryById(categories, id) {\n      // 创建扁平化的分类列表\n      const flatCategories = this.flattenCategories(categories)\n\n      // 在扁平化列表中查找\n      const category = flatCategories.find(cat => cat.id === id)\n      return category || null\n    },\n\n    /** 将树形分类数据扁平化 */\n    flattenCategories(categories) {\n      let result = []\n\n      function flatten(cats) {\n        for (const cat of cats) {\n          result.push(cat)\n          if (cat.children && cat.children.length > 0) {\n            flatten(cat.children)\n          }\n        }\n      }\n\n      flatten(categories)\n      return result\n    },\n\n    /** 更新规则分数 */\n    updateRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 获取题型文本 */\n    getQuestionTypeText(questionTypes) {\n      const questionTypeMap = {\n        '1': '单选题', '2': '多选题', '3': '判断题',\n        1: '单选题', 2: '多选题', 3: '判断题'\n      }\n\n      // 如果是数组，处理多个题型\n      if (Array.isArray(questionTypes)) {\n        return questionTypes.map(t => questionTypeMap[t]).join('、')\n      }\n\n      // 如果是单个值，直接返回对应文本\n      return questionTypeMap[questionTypes] || '未知'\n    },\n\n\n\n    /** 获取子规则标签 */\n    getChildRuleLabel(childRule) {\n      if (childRule.type === 3) {\n        return '题库：'\n      } else if (childRule.type === 1) {\n        // 题型子规则只显示具体的题型名称，不显示\"题型：\"前缀\n        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return questionTypeMap[childRule.selectedQuestionTypes[0]]\n        } else {\n          return '题型'\n        }\n      }\n      return '规则：'\n    },\n\n    /** 获取规则类型标签（用于random-paper显示） */\n    getRuleTypeLabel(rule) {\n      if (rule.type === 3) {\n        return '题库：'\n      } else if (rule.type === 1) {\n        return '题型：'\n      }\n      return '规则：'\n    },\n\n    /** 获取规则内容（用于random-paper显示） */\n    getRuleContent(rule) {\n      if (rule.type === 3) {\n        // 题库规则显示题库名称\n        if (rule.selectedBanks && rule.selectedBanks.length > 0) {\n          return rule.selectedBanks.map(bank => bank.bankName).join('、')\n        }\n        return '未选择题库'\n      } else if (rule.type === 1) {\n        // 题型规则显示题型名称\n        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')\n        }\n        return '未选择题型'\n      }\n      return '未配置'\n    },\n\n    /** 添加子规则 */\n    addSubRule(rule) {\n      this.currentOperation = 'addSub'\n      this.parentRule = rule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n\n      if (rule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 编辑规则 */\n    editRule(rule) {\n      this.currentOperation = 'edit'\n      this.editingRule = rule\n      this.ruleForm.ruleType = rule.type\n      this.ruleForm.generateType = rule.generateType\n\n      if (rule.type === 3) {\n        // 题库规则\n        this.ruleForm.selectedItems = [...rule.selectedItems]\n      } else if (rule.type === 1) {\n        // 题型规则\n        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]\n        // 编辑题型规则时，更新题目数量\n        this.$nextTick(() => {\n          this.updateQuestionTypeCount()\n        })\n      }\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 删除规则 */\n    deleteRule(rule) {\n      this.$confirm('确定要删除这条规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = this.rules.findIndex(r => r.id === rule.id)\n        if (index > -1) {\n          this.rules.splice(index, 1)\n          this.$message.success('规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 删除子规则 */\n    deleteChildRule(parentRule, childRule) {\n      this.$confirm('确定要删除这条子规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = parentRule.children.findIndex(child => child.id === childRule.id)\n        if (index > -1) {\n          parentRule.children.splice(index, 1)\n          this.$message.success('子规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 获取对话框标题 */\n    getDialogTitle() {\n      switch (this.currentOperation) {\n        case 'add':\n          return '添加规则'\n        case 'edit':\n          return '编辑规则'\n        case 'addSub':\n          return '添加子规则'\n        default:\n          return '添加规则'\n      }\n    },\n\n    /** 是否显示规则类型选择 */\n    shouldShowRuleTypeSelection() {\n      // 所有操作都显示规则类型选择\n      return true\n    },\n\n    /** 是否显示规则类型选项 */\n    shouldShowRuleType(ruleType) {\n      // 编辑时只显示当前规则的类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type === ruleType\n      }\n\n      // 添加子规则时的逻辑\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        // 题型规则的子规则只能是题库规则\n        if (this.parentRule.type === 1) {\n          return ruleType === 3\n        }\n        // 题库规则的子规则只能是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 添加一级规则时的逻辑\n      if (this.currentOperation === 'add') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 其他情况显示所有类型\n      return true\n    },\n\n    /** 是否显示特定题型选项 */\n    shouldShowQuestionType(questionType) {\n      // 如果不是添加子规则，显示所有题型\n      if (this.currentOperation !== 'addSub' || !this.parentRule) {\n        return true\n      }\n\n      // 获取父规则下已有的题型子规则中选择的题型\n      const existingQuestionTypes = []\n      if (this.parentRule.children) {\n        this.parentRule.children.forEach(child => {\n          if (child.type === 1 && child.selectedQuestionTypes) {\n            existingQuestionTypes.push(...child.selectedQuestionTypes)\n          }\n        })\n      }\n\n      // 如果该题型已经被选择过，则隐藏\n      return !existingQuestionTypes.includes(questionType)\n    },\n\n    /** 判断表格行是否可选择 */\n    isRowSelectable(row) {\n      // 如果是编辑操作，允许选择\n      if (this.currentOperation === 'edit') {\n        return true\n      }\n\n      // 如果是添加子规则，允许选择（子规则不涉及题库选择）\n      if (this.currentOperation === 'addSub') {\n        return true\n      }\n\n      // 如果是添加一级规则，检查题库是否已被使用\n      return !this.usedBankIds.includes(row.bankId)\n    },\n\n    /** 获取表格行的样式类名 */\n    getRowClassName({ row }) {\n      // 如果题库已被使用且不是编辑操作，添加禁用样式\n      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {\n        return 'disabled-row'\n      }\n      return ''\n    },\n\n    /** 更新题型题目数量 */\n    async updateQuestionTypeCount() {\n      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {\n        this.currentQuestionTypeCount = 0\n        return\n      }\n\n      // 获取题库信息\n      let selectedBanks = []\n      if (this.currentOperation === 'addSub' && this.parentRule && this.parentRule.type === 3) {\n        // 添加子规则时，使用父规则的题库\n        selectedBanks = this.parentRule.selectedBanks || []\n      } else {\n        // 其他情况使用当前选择的题库\n        selectedBanks = this.selectedBanks\n      }\n\n      if (!selectedBanks.length) {\n        this.currentQuestionTypeCount = 0\n        this.$message.warning('请先选择题库')\n        return\n      }\n\n      try {\n        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)\n        this.currentQuestionTypeCount = count\n\n        if (count === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目')\n        }\n      } catch (error) {\n        console.error('查询题目数量失败:', error)\n        this.currentQuestionTypeCount = 0\n        this.$message.error('查询题目数量失败')\n      }\n    },\n\n    /** 根据题库和题型查询题目数量 */\n    async getQuestionCountByType(banks, questionTypes) {\n      if (!banks.length || !questionTypes.length) {\n        return 0\n      }\n\n      let totalCount = 0\n\n      // 遍历每个题库，查询题目统计\n      for (const bank of banks) {\n        try {\n          // 使用已导入的API方法\n          const response = await getQuestionStatistics(bank.bankId)\n          console.log(`题库${bank.bankId}统计数据:`, response)\n\n          if (response.code === 200 && response.data) {\n            const statistics = response.data\n\n            // 根据选择的题型累加数量\n            questionTypes.forEach(type => {\n              switch (type) {\n                case '1': // 单选题\n                  totalCount += statistics.singleChoice || 0\n                  break\n                case '2': // 多选题\n                  totalCount += statistics.multipleChoice || 0\n                  break\n                case '3': // 判断题\n                  totalCount += statistics.judgment || 0\n                  break\n              }\n            })\n          }\n        } catch (error) {\n          console.error(`查询题库${bank.bankId}统计信息失败:`, error)\n        }\n      }\n\n      console.log(`总题目数量: ${totalCount}`)\n      return totalCount\n    },\n\n    /** 是否禁用规则类型 */\n    shouldDisableRuleType(ruleType) {\n      // 编辑时不能更改类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type !== ruleType\n      }\n\n      // 添加一级规则时，只能选择题库，禁用其他类型\n      if (this.currentOperation === 'add') {\n        return ruleType !== 3\n      }\n\n      return false\n    },\n    \n    /** 全选/取消全选 */\n    handleSelectAll() {\n      this.fixedQuestions.forEach(question => {\n        question.selected = this.selectAll\n      })\n    },\n\n    /** 删除选中题目 */\n    handleDeleteSelected() {\n      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)\n      if (selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)\n        this.selectAll = false\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 手动选题 */\n    handleManualSelect() {\n      this.showManualSelectDialog = true\n      this.initManualSelectData()\n    },\n\n    /** 初始化手动选题数据 */\n    initManualSelectData() {\n      // 重置数据\n      this.manualSelect.selectedCategory = ''\n      this.manualSelect.bankSearchKeyword = ''\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.manualSelect.selectedQuestions = []\n      this.manualSelect.selectedStats = {}\n\n      // 加载分类数据\n      this.loadCategoryTree()\n\n      // 加载题库列表\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 加载分类树数据 */\n    loadCategoryTree() {\n      listCategory({ pageSize: 1000 }).then(response => {\n        const categories = response.rows || []\n        this.categoryOptions = this.buildCategoryTree(categories)\n      }).catch(error => {\n        console.error('加载分类数据失败:', error)\n        this.categoryOptions = []\n      })\n    },\n\n    /** 构建分类树 */\n    buildCategoryTree(categories) {\n      const map = {}\n\n      // 先将所有分类放入map中\n      categories.forEach(category => {\n        map[category.id] = { ...category, children: [] }\n      })\n\n      // 构建完整的树形结构\n      const result = []\n      categories.forEach(category => {\n        if (category.parentId === 0) {\n          // 顶级分类\n          result.push(map[category.id])\n        } else {\n          // 子分类\n          if (map[category.parentId]) {\n            map[category.parentId].children.push(map[category.id])\n          }\n        }\n      })\n\n      // 清理空的children数组\n      const cleanEmptyChildren = (node) => {\n        if (node.children && node.children.length === 0) {\n          delete node.children\n        } else if (node.children && node.children.length > 0) {\n          node.children.forEach(child => cleanEmptyChildren(child))\n        }\n      }\n\n      // 清理所有节点的空children\n      result.forEach(node => cleanEmptyChildren(node))\n\n      return result\n    },\n\n    /** 获取所有子分类ID */\n    getAllChildCategoryIds(categoryId, categories) {\n      const result = [categoryId]\n\n      const findChildren = (parentId) => {\n        categories.forEach(category => {\n          if (category.parentId === parentId) {\n            result.push(category.id)\n            findChildren(category.id)\n          }\n        })\n      }\n\n      findChildren(categoryId)\n      return result\n    },\n\n    /** 加载手动选题的题库列表 */\n    loadManualSelectQuestionBanks() {\n      let categoryIds = []\n\n      // 如果选择了分类，获取该分类及其所有子分类的ID\n      if (this.manualSelect.selectedCategory) {\n        // 获取原始分类数据（包含所有层级）\n        listCategory({ pageSize: 1000 }).then(response => {\n          const allCategories = response.rows || []\n          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)\n\n          // 执行多次查询，因为后端不支持IN查询\n          this.loadQuestionBanksByCategories(categoryIds)\n        }).catch(error => {\n          console.error('加载分类数据失败:', error)\n          this.loadQuestionBanksByCategories([])\n        })\n      } else {\n        // 没有选择分类，加载所有题库\n        this.loadQuestionBanksByCategories([])\n      }\n    },\n\n    /** 根据分类ID列表加载题库 */\n    loadQuestionBanksByCategories(categoryIds) {\n      const queryParams = {\n        pageNum: this.manualSelect.bankPagination.pageNum,\n        pageSize: this.manualSelect.bankPagination.pageSize,\n        bankName: this.manualSelect.bankSearchKeyword || undefined\n      }\n\n      if (categoryIds.length > 0) {\n        // 如果有分类筛选，需要合并多个分类的结果\n        const promises = categoryIds.map(categoryId => {\n          return listQuestionBank({ ...queryParams, categoryId })\n        })\n\n        Promise.all(promises).then(responses => {\n          const allBanks = []\n          let totalCount = 0\n\n          responses.forEach(response => {\n            if (response.rows) {\n              allBanks.push(...response.rows)\n              totalCount += response.total || 0\n            }\n          })\n\n          // 去重（根据bankId）\n          const uniqueBanks = allBanks.filter((bank, index, self) =>\n            index === self.findIndex(b => b.bankId === bank.bankId)\n          )\n\n          this.manualSelect.questionBanks = uniqueBanks\n          this.manualSelect.bankPagination.total = uniqueBanks.length\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      } else {\n        // 没有分类筛选，直接查询\n        listQuestionBank(queryParams).then(response => {\n          this.manualSelect.questionBanks = response.rows || []\n          this.manualSelect.bankPagination.total = response.total || 0\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      }\n    },\n\n    /** 搜索题库 */\n    searchQuestionBanks() {\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 选择题库 */\n    selectQuestionBank(row) {\n      this.manualSelect.selectedBankId = row.bankId\n      this.loadQuestions()\n    },\n\n    /** 加载题目列表 */\n    loadQuestions() {\n      if (!this.manualSelect.selectedBankId) {\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n        return\n      }\n\n      const queryParams = {\n        pageNum: this.manualSelect.questionPagination.pageNum,\n        pageSize: this.manualSelect.questionPagination.pageSize,\n        bankId: this.manualSelect.selectedBankId,\n        questionType: this.manualSelect.questionType || undefined,\n        difficulty: this.manualSelect.difficulty || undefined,\n        questionContent: this.manualSelect.questionSearchKeyword || undefined\n      }\n\n      listQuestion(queryParams).then(response => {\n        this.manualSelect.questions = response.rows || []\n        this.manualSelect.questionPagination.total = response.total || 0\n      }).catch(error => {\n        console.error('加载题目列表失败:', error)\n        this.$message.error('加载题目列表失败')\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n      })\n    },\n\n    /** 搜索题目 */\n    searchQuestions() {\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 重置题目搜索 */\n    resetQuestionSearch() {\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.searchQuestions()\n    },\n\n    /** 随机抽题 */\n    handleRandomSelect() {\n      this.$message.info('随机抽题功能开发中...')\n    },\n\n    /** 排序 */\n    handleSort() {\n      this.$message.info('排序功能开发中...')\n    },\n\n    /** 批量设置分数 */\n    handleBatchSetScore() {\n      this.$message.info('批量设置分数功能开发中...')\n    },\n\n    /** 导出 */\n    handleExport() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    /** 展开/收起 */\n    handleToggleExpand() {\n      this.isExpanded = !this.isExpanded\n      // 批量设置所有题目的展开状态\n      this.fixedQuestions.forEach(question => {\n        question.expanded = this.isExpanded\n      })\n      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)\n    },\n\n    /** 题目选择变化 */\n    handleQuestionSelectionChange(selection) {\n      this.manualSelect.selectedQuestions = selection\n      this.updateSelectedStats()\n    },\n\n    /** 更新选中题目统计 */\n    updateSelectedStats() {\n      const stats = {}\n      this.manualSelect.selectedQuestions.forEach(question => {\n        const type = question.questionType\n        stats[type] = (stats[type] || 0) + 1\n      })\n      this.manualSelect.selectedStats = stats\n    },\n\n    /** 确认手动选题 */\n    confirmManualSelect() {\n      if (this.manualSelect.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择题目')\n        return\n      }\n\n      // 将选中的题目添加到固定试卷中\n      this.manualSelect.selectedQuestions.forEach((question, index) => {\n        this.fixedQuestions.push({\n          id: Date.now() + index,\n          questionId: question.questionId,\n          content: question.questionContent,\n          type: question.questionType,\n          difficulty: question.difficulty,\n          options: question.options, // 保存选项信息\n          score: 1, // 默认1分\n          selected: false,\n          expanded: false // 默认收起状态\n        })\n      })\n\n      this.showManualSelectDialog = false\n      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)\n    },\n\n    /** 题库分页大小变化 */\n    handleBankSizeChange(val) {\n      this.manualSelect.bankPagination.pageSize = val\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题库当前页变化 */\n    handleBankCurrentChange(val) {\n      this.manualSelect.bankPagination.pageNum = val\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题目分页大小变化 */\n    handleQuestionSizeChange(val) {\n      this.manualSelect.questionPagination.pageSize = val\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 题目当前页变化 */\n    handleQuestionCurrentChange(val) {\n      this.manualSelect.questionPagination.pageNum = val\n      this.loadQuestions()\n    },\n\n    /** 获取难度文本 */\n    getDifficultyText(difficulty) {\n      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }\n      return difficultyMap[difficulty] || '未知'\n    },\n\n    /** 更新题目分数 */\n    updateQuestionScore(question) {\n      // 分数更新后可以触发总分重新计算\n      this.$forceUpdate()\n    },\n\n    /** 切换题目展开/收起状态 */\n    toggleQuestionExpand(question) {\n      question.expanded = !question.expanded\n    },\n\n    /** 删除单个题目 */\n    deleteQuestion(question, index) {\n      this.$confirm('确定删除这道题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions.splice(index, 1)\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 解析选项JSON字符串 */\n    parseOptions(optionsStr) {\n      try {\n        if (typeof optionsStr === 'string') {\n          return JSON.parse(optionsStr)\n        }\n        return optionsStr || []\n      } catch (error) {\n        console.error('解析选项失败:', error)\n        return []\n      }\n    },\n\n    /** 判断题目是否可选择 */\n    isQuestionSelectable(row) {\n      // 检查题目是否已经添加到试卷中\n      return !this.fixedQuestions.some(question => question.questionId === row.questionId)\n    },\n\n    /** 获取题目行的样式类名 */\n    getQuestionRowClassName({ row }) {\n      // 如果题目已被添加，添加禁用样式\n      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {\n        return 'disabled-question-row'\n      }\n      return ''\n    },\n    \n    /** 上传前验证 */\n    beforeUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0\n      const isLt2M = file.size / 1024 / 1024 < 2\n      \n      if (!isImage) {\n        this.$message.error('只能上传图片文件!')\n        return false\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!')\n        return false\n      }\n      return true\n    },\n    \n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    \n    /** 加载试卷数据 */\n    loadPaperData() {\n      if (this.paperId) {\n        // TODO: 调用API加载试卷数据\n        console.log('加载试卷数据:', this.paperId)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.exam-editor {\n  display: flex;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.exam-editor-left {\n  flex: 1;\n  padding: 20px;\n  padding-right: 420px; /* 为右侧固定面板留出空间 */\n  overflow-y: auto;\n  transition: padding-right 0.3s ease;\n  background-color: #FFF;\n}\n\n.exam-editor-left.collapsed {\n  padding-right: 70px; /* 右侧面板收起时的空间 */\n}\n\n.exam-editor-right {\n  background: #fff;\n  border-left: 1px solid #e4e7ed;\n  position: fixed;\n  right: 0;\n  top: 0;\n  height: 100vh;\n  transition: width 0.3s ease;\n  z-index: 100;\n}\n\n.collapse-button {\n  position: absolute;\n  left: -15px;\n  top: 20px;\n  width: 30px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e4e7ed;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  font-size: 16px;\n  color: #606266;\n}\n\n.collapse-button:hover {\n  background: #f5f7fa;\n  color: #409eff;\n}\n\n.editPaper_main_top {\n  background: #fff;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.editPaper_main_top .el-button-group {\n  margin-right: 15px;\n  display: inline-block;\n}\n\n.clear_both {\n  clear: both;\n}\n\n.subject_main-wrapper {\n  max-width: 100%;\n}\n\n/* 左侧卡片模块悬停效果 */\n.subject_main-wrapper .el-card {\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.subject_main-wrapper .el-card:hover {\n  border: 2px dashed #409eff;\n  background-color: #fafbff;\n}\n\n.subtitle-title {\n  position: relative;\n}\n\n.slgfont {\n  font-size: 24px;\n  color: #303133;\n}\n\n.paper-count {\n  margin-top: 10px;\n}\n\n.paper-count .el-tag {\n  margin-left: 8px;\n}\n\n\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.tac {\n  text-align: center;\n}\n\n.pd5 {\n  padding: 5px;\n}\n\n.editor-header {\n  padding: 20px;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.editor-header--big {\n  font-size: 20px;\n}\n\n.close-button {\n  font-size: 20px;\n  color: #909399;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  color: #f56c6c;\n  background-color: #fef0f0;\n}\n\n.main {\n  height: calc(100vh - 80px);\n  overflow-y: auto;\n  padding: 0;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.main-collapse {\n  border: none;\n}\n\n.main-collapse .el-collapse-item__header {\n  padding: 0 20px;\n  height: 60px;\n  line-height: 60px;\n  background: #f8f8f8;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* 使用深度选择器覆盖Element UI默认样式 */\n.main-collapse >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n.editor-collapse-item >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n/* Vue 3 深度选择器语法 */\n.main-collapse :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.editor-collapse-item :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.main-module {\n  padding: 20px;\n}\n\n.setting-block {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.setting-block:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n\n/* 二级设置块容器 */\n.block-expand {\n  margin-top: 10px;\n  padding-left: 20px;\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 二级设置块样式 */\n.sub-setting {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #e8e8e8;\n}\n\n.sub-setting:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n}\n\n.line {\n  display: flex;\n  align-items: flex-start;\n}\n\n.block-header {\n  justify-content: space-between;\n}\n\n.block-header b {\n  font-size: 14px;\n  color: #303133;\n  min-width: 93px;\n  line-height: 32px;\n}\n\n.input-area {\n  flex: 1;\n  margin-left: 20px;\n}\n\n.input--number {\n  width: 120px;\n}\n\n.dpib {\n  display: inline-block;\n}\n\n.avatar-uploader {\n  border: none !important;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 120px;\n  height: 80px;\n  box-sizing: border-box;\n}\n\n\n\n/* 使用伪元素创建统一的虚线边框 */\n.avatar-uploader::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.avatar-uploader:hover::before {\n  border-color: #409eff;\n}\n\n/* 确保Element UI组件没有边框 */\n.avatar-uploader .el-upload {\n  border: none !important;\n  width: 100%;\n  height: 100%;\n  background: transparent !important;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 120px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n}\n\n.avatar {\n  width: 120px;\n  height: 80px;\n  display: block;\n  object-fit: cover;\n}\n\n.image_area {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bold {\n  font-weight: bold;\n}\n\n.mr10 {\n  margin-right: 10px;\n}\n\n/* 折叠面板标题样式 */\n.collapse-title {\n  margin-left: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.collapse-title i {\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.el-popover__reference {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n}\n\n.el-popover__title {\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n/* 设置标题容器样式 */\n.setting-title {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n}\n\n.setting-title b {\n  font-size: 14px;\n  color: #303133;\n}\n\n/* 出题方式问号图标样式 */\n.paper-type-question {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n  position: relative;\n  z-index: 10;\n  font-size: 14px;\n  line-height: 1;\n}\n\n.paper-type-question:hover {\n  color: #409eff;\n}\n\n/* 出题方式提示框样式 */\n.paper-type-tooltip {\n  max-width: 320px !important;\n  z-index: 2000 !important;\n}\n\n/* 迟到限制提示框样式 */\n.late-limit-tooltip {\n  max-width: 280px !important;\n  z-index: 2000 !important;\n}\n\n/* 随机规则设置对话框样式 */\n.cascade-random-rules {\n  min-height: 400px;\n}\n\n/* 确保对话框层级正确 */\n::v-deep .el-dialog__wrapper {\n  z-index: 3000 !important;\n}\n\n::v-deep .el-dialog {\n  z-index: 3001 !important;\n  position: relative !important;\n}\n\n::v-deep .el-overlay {\n  z-index: 2999 !important;\n}\n\n.topbar {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.topbar .fl {\n  float: left;\n}\n\n.topbar .summary {\n  margin-left: 15px;\n  color: #666;\n  font-size: 14px;\n}\n\n.topbar .total_score {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.guide-steps-list {\n  margin: 30px 0;\n  padding: 0 20px;\n}\n\n/* 步骤组件样式优化 */\n::v-deep .guide-steps-list .el-step {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__head {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__main {\n  text-align: center;\n  margin-top: 10px;\n}\n\n::v-deep .guide-steps-list .el-step__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n::v-deep .guide-steps-list .el-step__description {\n  margin-top: 8px;\n  padding: 0 10px;\n}\n\n.step-content {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 5px;\n}\n\n.rules-content {\n  min-height: 200px;\n  margin: 20px 0;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 60px 0;\n  color: #999;\n  font-size: 14px;\n}\n\n.bottom-panel {\n  text-align: center;\n  padding: 20px 0;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n/* 强制覆盖Element UI折叠面板背景色 */\n.el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n  font-size: 16px !important;\n  font-weight: bold !important;\n}\n\n.el-collapse-item__header.is-active {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 使用属性选择器强制覆盖 */\n[class*=\"el-collapse-item__header\"] {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 深度选择器 */\n.exam-editor >>> .el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .exam-editor-left {\n    padding-right: 370px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 70px;\n  }\n}\n\n@media (max-width: 768px) {\n  .exam-editor {\n    flex-direction: column;\n  }\n\n  .exam-editor-left {\n    padding-right: 20px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 20px;\n  }\n\n  .exam-editor-right {\n    position: relative !important;\n    width: 100% !important;\n    height: 50vh;\n    border-left: none;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .collapse-button {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAomDA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;MACA;MACAM,mBAAA;MACAC,eAAA;MAEA;MACAC,cAAA;MAEA;MACAC,SAAA;QACAR,OAAA;QACAS,SAAA;QACAC,SAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,SAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QAAA;QACAC,MAAA;QAAA;QACAC,eAAA;QAAA;QACAC,eAAA;QAAA;QACAC,UAAA;MACA;MAEA;;MAEA;MACAC,cAAA;MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;;MAEA;MACAC,sBAAA;MACAC,eAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACAC,YAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;UACAC,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAC,cAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,qBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,kBAAA;UACAR,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAO,iBAAA;QAAA;QACAC,aAAA;MACA;MAEA;MACAC,cAAA;MACA;MACAC,KAAA;MAEA;MACAC,iBAAA;MACAC,QAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACAC,gBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,wBAAA;MAAA;MACAxB,aAAA;MACAyB,mBAAA;IAAA,sBACA,sBACA,oBACA,gBACA,cACA,mBACA;MACAvB,OAAA;MACAC,QAAA;MACAuB,QAAA,EAAAC;IACA;EAEA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,YAAA9B,aAAA,CAAA+B,MAAA,WAAAC,IAAA;QAAA,OACAF,KAAA,CAAAd,QAAA,CAAAG,aAAA,CAAAc,QAAA,CAAAD,IAAA,CAAAE,MAAA;MAAA,CACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAA/B,KAAA;MACA,KAAAU,KAAA,CAAAsB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAA4C,MAAA;UACA;UACAD,IAAA,CAAA3C,QAAA,CAAA0C,OAAA,WAAAG,KAAA;YACAnC,KAAA,IAAAmC,KAAA,CAAAC,aAAA;UACA;QACA;UACA;UACApC,KAAA,IAAAiC,IAAA,CAAAG,aAAA;QACA;MACA;MACA,OAAApC,KAAA;IACA;IAEA;IACAqC,cAAA,WAAAA,eAAA;MACA,IAAArC,KAAA;MACA,KAAAU,KAAA,CAAAsB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAA4C,MAAA;UACA;UACAD,IAAA,CAAA3C,QAAA,CAAA0C,OAAA,WAAAG,KAAA;YACAnC,KAAA,IAAAmC,KAAA,CAAAxE,UAAA;UACA;QACA;UACA;UACAqC,KAAA,IAAAiC,IAAA,CAAAtE,UAAA;QACA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACAsC,WAAA,WAAAA,YAAA;MACA,YAAA5B,KAAA,CAAAiB,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,OAAA,OAAAC,GAAA;MACA,KAAAhC,KAAA,CAAAsB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAtF,IAAA,UAAAsF,IAAA,CAAAlB,aAAA;UACAkB,IAAA,CAAAlB,aAAA,CAAAiB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAA;MACA,IAAAC,KAAA;MACA,KAAAjE,cAAA,CAAAkD,OAAA,WAAAgB,QAAA;QACA,IAAArG,IAAA,GAAAqG,QAAA,CAAArG,IAAA;QACAoG,KAAA,CAAApG,IAAA,KAAAoG,KAAA,CAAApG,IAAA;MACA;MACA,OAAAoG,KAAA;IACA;EACA;EACAE,KAAA;IACAvG,OAAA,WAAAA,QAAAwG,GAAA;MACA,IAAAA,GAAA,SAAApG,OAAA;QACA,KAAAqG,aAAA;MACA;IACA;IAEA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QAAA,WAAAC,kBAAA,CAAA1G,OAAA,mBAAA2G,aAAA,CAAA3G,OAAA,IAAA4G,CAAA,UAAAC,QAAA;UAAA,WAAAF,aAAA,CAAA3G,OAAA,IAAA8G,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBAAA,MACAP,MAAA,CAAA1C,QAAA,CAAAC,QAAA,UAAAwC,MAAA,IAAAA,MAAA,CAAAnB,MAAA;kBAAA0B,QAAA,CAAAC,CAAA;kBAAA;gBAAA;gBAAAD,QAAA,CAAAC,CAAA;gBAAA,OACAP,MAAA,CAAAQ,uBAAA;cAAA;gBAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;UAAA,GAAAL,OAAA;QAAA;MAEA;MACAM,IAAA;IACA;EACA;EACAC,OAAA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA;IACA;IAEA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAjH,mBAAA,SAAAA,mBAAA;MACA,KAAAC,eAAA,QAAAD,mBAAA;IACA;IAEA,WACAkH,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,QAAA,CAAAC,IAAA;IACA;IAEA,WACAE,aAAA,WAAAA,cAAA;MACA,KAAAH,QAAA,CAAAC,IAAA;IACA;IAEA,WACAG,oBAAA,WAAAA,qBAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;IACA;IAEA,YACAI,gBAAA,WAAAA,iBAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;IAIA,iBACAK,aAAA,WAAAA,cAAA;MACA,KAAAnE,cAAA;IACA;IAEA,qBACAoE,eAAA,WAAAA,gBAAA;MACA,KAAApE,cAAA;IACA;IAEA,aACAqE,kBAAA,WAAAA,mBAAA;MACA,KAAA7D,gBAAA;MACA,KAAAC,WAAA;;MAEA;MACA,SAAAR,KAAA,CAAAwB,MAAA;QACA,KAAAtB,QAAA,CAAAC,QAAA,QAAAH,KAAA,IAAA/D,IAAA;MACA;QACA,KAAAiE,QAAA,CAAAC,QAAA;MACA;MAEA,KAAAF,iBAAA;MACA;MACA,KAAAoE,WAAA;QACAjF,OAAA;QACAC,QAAA;QACAuB,QAAA,EAAAC;MACA;MACA,KAAAyD,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAA7D,mBAAA;MACA;MACA8D,OAAA,CAAAC,GAAA,EACA,IAAAC,8BAAA,OAAAN,WAAA,GACA,IAAAO,sBAAA;QAAAvF,QAAA;MAAA,GACA,EAAAwF,IAAA,WAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAA7I,OAAA,EAAA2I,KAAA;UAAAG,YAAA,GAAAF,KAAA;UAAAG,gBAAA,GAAAH,KAAA;QACA,IAAA7F,aAAA,GAAA+F,YAAA,CAAAE,IAAA;QACAX,MAAA,CAAAlF,KAAA,GAAA2F,YAAA,CAAA3F,KAAA;QACAkF,MAAA,CAAAhG,eAAA,GAAA0G,gBAAA,CAAAC,IAAA,IAAAD,gBAAA,CAAA3I,IAAA;;QAEA;QACA,IAAA6I,kBAAA,GAAAlG,aAAA,CAAAmG,GAAA,WAAAnE,IAAA;UAAA,OACA,IAAAoE,+BAAA,EAAApE,IAAA,CAAAE,MAAA,EAAAyD,IAAA,WAAAxC,KAAA;YACAnB,IAAA,CAAAqE,aAAA,GAAAlD,KAAA,CAAA9F,IAAA,GAAA8F,KAAA,CAAA9F,IAAA,CAAAiJ,UAAA,IAAAnD,KAAA,CAAA9F,IAAA,CAAA+C,KAAA;YACA,OAAA4B,IAAA;UACA,GAAAuE,KAAA;YACAvE,IAAA,CAAAqE,aAAA;YACA,OAAArE,IAAA;UACA;QAAA,CACA;QAEAuD,OAAA,CAAAC,GAAA,CAAAU,kBAAA,EAAAP,IAAA,WAAAa,cAAA;UACAlB,MAAA,CAAAtF,aAAA,GAAAwG,cAAA;UACAlB,MAAA,CAAA7D,mBAAA;QACA;MACA,GAAA8E,KAAA;QACAjB,MAAA,CAAA7D,mBAAA;MACA;IACA;IAEA,WACAgF,eAAA,WAAAA,gBAAA;MACA,KAAA/B,QAAA,CAAAC,IAAA;MACA,KAAA9D,cAAA;IACA;IAEA,aACA6F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhD,kBAAA,CAAA1G,OAAA,mBAAA2G,aAAA,CAAA3G,OAAA,IAAA4G,CAAA,UAAA+C,SAAA;QAAA,WAAAhD,aAAA,CAAA3G,OAAA,IAAA8G,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAAA,MACA0C,MAAA,CAAA3F,QAAA,CAAAC,QAAA,UAAA0F,MAAA,CAAA3F,QAAA,CAAAG,aAAA,CAAAmB,MAAA;gBAAAuE,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAGAwC,MAAA,CAAA3F,QAAA,CAAAC,QAAA,UAAA0F,MAAA,CAAA3F,QAAA,CAAAI,qBAAA,CAAAkB,MAAA;gBAAAuE,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAKAwC,MAAA,CAAA3F,QAAA,CAAAC,QAAA;gBAAA4F,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA,MACA0C,MAAA,CAAAnF,wBAAA;gBAAAqF,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAKA,IAAAwC,MAAA,CAAAtF,gBAAA;gBACA;gBACAsF,MAAA,CAAAI,kBAAA;cACA;gBACA;gBACAJ,MAAA,CAAAK,UAAA;cACA;cAEAL,MAAA,CAAAjC,QAAA,CAAAuC,OAAA,CAAAN,MAAA,CAAAtF,gBAAA;cACAsF,MAAA,CAAA5F,iBAAA;;cAEA;cACA4F,MAAA,CAAAO,aAAA;YAAA;cAAA,OAAAL,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,IAAA1E,IAAA,QAAAf,WAAA;MACAe,IAAA,CAAAtF,IAAA,QAAAiE,QAAA,CAAAC,QAAA;MACAoB,IAAA,CAAAnB,YAAA,QAAAF,QAAA,CAAAE,YAAA;MAEA,SAAAF,QAAA,CAAAC,QAAA;QACA;QACAoB,IAAA,CAAAlB,aAAA,OAAAgG,mBAAA,CAAAlK,OAAA,OAAA+D,QAAA,CAAAG,aAAA;QACAkB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAAsE,GAAA,WAAAnE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA2E,aAAA,EAAArE,IAAA,CAAAqE;UACA;QAAA;QACAhE,IAAA,CAAA+E,YAAA,GAAA/E,IAAA,CAAAR,aAAA,CAAAwF,MAAA,WAAAC,GAAA,EAAAtF,IAAA;UAAA,OAAAsF,GAAA,IAAAtF,IAAA,CAAAqE,aAAA;QAAA;;QAEA;QACA,IAAAhE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAA+E,YAAA;UACA/E,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAA+E,YAAA;QACA;QACA/E,IAAA,CAAAtE,UAAA,GAAAsE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAkF,gBAAA;MACA,gBAAAvG,QAAA,CAAAC,QAAA;QACA;QACAoB,IAAA,CAAAjB,qBAAA,OAAA+F,mBAAA,CAAAlK,OAAA,OAAA+D,QAAA,CAAAI,qBAAA;QACAiB,IAAA,CAAA+E,YAAA,QAAA5F,wBAAA;MACA;IACA;IAEA,YACAwF,UAAA,WAAAA,WAAA;MAAA,IAAAQ,MAAA;MACA;MACA,SAAAnG,gBAAA,sBAAAE,UAAA;QACA,KAAAkG,aAAA;QACA;MACA;;MAEA;MACA,SAAAzG,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAE,YAAA,uBAAAW,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAA0F,KAAA;UACA,IAAArF,IAAA;YACAsF,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YAAA;YACA3K,IAAA,EAAAyK,MAAA,CAAAxG,QAAA,CAAAC,QAAA;YACAC,YAAA,EAAAsG,MAAA,CAAAxG,QAAA,CAAAE,YAAA;YACAsB,aAAA;YACA+E,gBAAA;YACAxJ,UAAA;YACAoD,aAAA,GAAAa,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA2E,aAAA,EAAArE,IAAA,CAAAqE;YACA;YACAe,YAAA,EAAApF,IAAA,CAAAqE,aAAA;UACA;UACAmB,MAAA,CAAAM,eAAA,CAAAzF,IAAA;UACAmF,MAAA,CAAA1G,KAAA,CAAAiH,IAAA,CAAA1F,IAAA;QACA;MACA;QACA;QACA,KAAAoF,aAAA;MACA;IACA;IAEA,aACAA,aAAA,WAAAA,cAAA;MACA,IAAApF,IAAA;QACAsF,EAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;QACA9K,IAAA,OAAAiE,QAAA,CAAAC,QAAA;QACAC,YAAA,OAAAF,QAAA,CAAAE,YAAA;QACAsB,aAAA;QAAA;QACA+E,gBAAA;QAAA;QACAxJ,UAAA;QAAA;QACAqJ,YAAA;MACA;;MAEA;MACA,SAAA/F,gBAAA,sBAAAE,UAAA;QACAc,IAAA,CAAAM,QAAA,QAAApB,UAAA,CAAAoG,EAAA;;QAEA;QACA,UAAApG,UAAA,CAAA7B,QAAA;UACA,KAAAsI,IAAA,MAAAzG,UAAA;QACA;;QAEA;QACA,KAAAA,UAAA,CAAA7B,QAAA,CAAAqI,IAAA,CAAA1F,IAAA;MACA;MAEA,SAAArB,QAAA,CAAAC,QAAA;QACA;QACAoB,IAAA,CAAAlB,aAAA,OAAAgG,mBAAA,CAAAlK,OAAA,OAAA+D,QAAA,CAAAG,aAAA;QACAkB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAAsE,GAAA,WAAAnE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA2E,aAAA,EAAArE,IAAA,CAAAqE;UACA;QAAA;QACAhE,IAAA,CAAA+E,YAAA,GAAA/E,IAAA,CAAAR,aAAA,CAAAwF,MAAA,WAAAC,GAAA,EAAAtF,IAAA;UAAA,OAAAsF,GAAA,IAAAtF,IAAA,CAAAqE,aAAA;QAAA;MACA,gBAAArF,QAAA,CAAAC,QAAA;QACA;QACAoB,IAAA,CAAAjB,qBAAA,OAAA+F,mBAAA,CAAAlK,OAAA,OAAA+D,QAAA,CAAAI,qBAAA;QACAiB,IAAA,CAAA+E,YAAA,QAAA5F,wBAAA;MACA;;MAEA;MACA,SAAAH,gBAAA;QACA,KAAAP,KAAA,CAAAiH,IAAA,CAAA1F,IAAA;MACA;;MAEA;MACA,KAAAyF,eAAA,CAAAzF,IAAA;IACA;IAEA,aACA6E,aAAA,WAAAA,cAAA;MACA,KAAAlG,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA,KAAAC,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,UAAA;MACA,KAAAC,wBAAA;IACA;IAEA,WACAyG,YAAA,WAAAA,aAAA;MACA,KAAA9C,WAAA,CAAAjF,OAAA;MACA,KAAAiF,WAAA,CAAAzD,QAAA,QAAA0D,aAAA,IAAAzD,SAAA;MACA,KAAA0D,iBAAA;IACA;IAEA,WACA6C,WAAA,WAAAA,YAAA;MACA,KAAA9C,aAAA;MACA,KAAAD,WAAA,CAAAjF,OAAA;MACA,KAAAiF,WAAA,CAAAzD,QAAA,GAAAC,SAAA;MACA,KAAA0D,iBAAA;IACA;IAEA,aACA8C,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApH,QAAA,CAAAG,aAAA,GAAAiH,SAAA,CAAAjC,GAAA,WAAAkC,IAAA;QAAA,OAAAA,IAAA,CAAAnG,MAAA;MAAA;IACA;IAEA,eACAoG,kBAAA,WAAAA,mBAAApG,MAAA;MAAA,IAAAqG,MAAA;MACA,IAAAb,KAAA,QAAA1G,QAAA,CAAAG,aAAA,CAAAqH,OAAA,CAAAtG,MAAA;MACA,IAAAwF,KAAA;QACA,KAAA1G,QAAA,CAAAG,aAAA,CAAAsH,MAAA,CAAAf,KAAA;MACA;;MAEA;MACA,KAAAgB,SAAA;QACA,IAAAC,KAAA,GAAAJ,MAAA,CAAAK,KAAA,CAAAC,iBAAA;QACA,IAAAF,KAAA;UACA;UACA,IAAAG,aAAA,GAAAP,MAAA,CAAAvI,aAAA,CAAA+I,IAAA,WAAA/G,IAAA;YAAA,OAAAA,IAAA,CAAAE,MAAA,KAAAA,MAAA;UAAA;UACA,IAAA4G,aAAA;YACAH,KAAA,CAAAK,kBAAA,CAAAF,aAAA;UACA;QACA;MACA;IACA;IAEA,WACAG,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAA/D,WAAA,CAAAjF,OAAA,GAAAgJ,IAAA;MACA,KAAA7D,iBAAA;IACA;IAEA,mBACA8D,eAAA,WAAAA,gBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAC,gBAAA,MAAAhK,eAAA,EAAA8J,UAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAzM,IAAA;IACA;IAEA,4BACA0M,gBAAA,WAAAA,iBAAAC,UAAA,EAAA5B,EAAA;MACA;MACA,IAAA6B,cAAA,QAAAC,iBAAA,CAAAF,UAAA;;MAEA;MACA,IAAAF,QAAA,GAAAG,cAAA,CAAAT,IAAA,WAAAW,GAAA;QAAA,OAAAA,GAAA,CAAA/B,EAAA,KAAAA,EAAA;MAAA;MACA,OAAA0B,QAAA;IACA;IAEA,iBACAI,iBAAA,WAAAA,kBAAAF,UAAA;MACA,IAAAI,MAAA;MAEA,SAAAC,QAAAC,IAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA9M,OAAA,EACA4M,IAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA7F,CAAA,IAAAiG,IAAA;YAAA,IAAAR,GAAA,GAAAM,KAAA,CAAAxK,KAAA;YACAmK,MAAA,CAAA5B,IAAA,CAAA2B,GAAA;YACA,IAAAA,GAAA,CAAAhK,QAAA,IAAAgK,GAAA,CAAAhK,QAAA,CAAA4C,MAAA;cACAsH,OAAA,CAAAF,GAAA,CAAAhK,QAAA;YACA;UACA;QAAA,SAAAyK,GAAA;UAAAL,SAAA,CAAAM,CAAA,CAAAD,GAAA;QAAA;UAAAL,SAAA,CAAAO,CAAA;QAAA;MACA;MAEAT,OAAA,CAAAL,UAAA;MACA,OAAAI,MAAA;IACA;IAEA,aACA7B,eAAA,WAAAA,gBAAAzF,IAAA;MACAA,IAAA,CAAAtE,UAAA,GAAAsE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAkF,gBAAA;IACA;IAEA,aACA+C,mBAAA,WAAAA,oBAAAC,aAAA;MACA,IAAAC,eAAA,OAAAlN,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QACA;QAAA;QAAA;MAAA,QACA,gCACA;;MAEA;MACA,IAAA+F,KAAA,CAAAyH,OAAA,CAAAF,aAAA;QACA,OAAAA,aAAA,CAAApE,GAAA,WAAAuE,CAAA;UAAA,OAAAF,eAAA,CAAAE,CAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,OAAAH,eAAA,CAAAD,aAAA;IACA;IAIA,cACAK,iBAAA,WAAAA,kBAAAC,SAAA;MACA,IAAAA,SAAA,CAAA9N,IAAA;QACA;MACA,WAAA8N,SAAA,CAAA9N,IAAA;QACA;QACA,IAAA8N,SAAA,CAAAzJ,qBAAA,IAAAyJ,SAAA,CAAAzJ,qBAAA,CAAAkB,MAAA;UACA,IAAAkI,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAA,eAAA,CAAAK,SAAA,CAAAzJ,qBAAA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA,iCACA0J,gBAAA,WAAAA,iBAAAzI,IAAA;MACA,IAAAA,IAAA,CAAAtF,IAAA;QACA;MACA,WAAAsF,IAAA,CAAAtF,IAAA;QACA;MACA;MACA;IACA;IAEA,+BACAgO,cAAA,WAAAA,eAAA1I,IAAA;MACA,IAAAA,IAAA,CAAAtF,IAAA;QACA;QACA,IAAAsF,IAAA,CAAAR,aAAA,IAAAQ,IAAA,CAAAR,aAAA,CAAAS,MAAA;UACA,OAAAD,IAAA,CAAAR,aAAA,CAAAsE,GAAA,WAAAnE,IAAA;YAAA,OAAAA,IAAA,CAAAN,QAAA;UAAA,GAAAiJ,IAAA;QACA;QACA;MACA,WAAAtI,IAAA,CAAAtF,IAAA;QACA;QACA,IAAAsF,IAAA,CAAAjB,qBAAA,IAAAiB,IAAA,CAAAjB,qBAAA,CAAAkB,MAAA;UACA,IAAAkI,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAnI,IAAA,CAAAjB,qBAAA,CAAA+E,GAAA,WAAApJ,IAAA;YAAA,OAAAyN,eAAA,CAAAzN,IAAA;UAAA,GAAA4N,IAAA;QACA;QACA;MACA;MACA;IACA;IAEA,YACAK,UAAA,WAAAA,WAAA3I,IAAA;MACA,KAAAhB,gBAAA;MACA,KAAAE,UAAA,GAAAc,IAAA;;MAEA;MACA,IAAA4I,eAAA;MAEA,IAAA5I,IAAA,CAAAtF,IAAA;QACA;QACAkO,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAjK,QAAA,CAAAC,QAAA,GAAAgK,eAAA;MACA,KAAAjK,QAAA,CAAAE,YAAA;MACA,KAAAF,QAAA,CAAAG,aAAA;MACA,KAAAH,QAAA,CAAAI,qBAAA;MAEA,KAAAL,iBAAA;MACA,KAAAoE,WAAA;QACAjF,OAAA;QACAC,QAAA;QACAuB,QAAA,EAAAC;MACA;MACA,KAAAyD,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACA6F,QAAA,WAAAA,SAAA7I,IAAA;MAAA,IAAA8I,MAAA;MACA,KAAA9J,gBAAA;MACA,KAAAC,WAAA,GAAAe,IAAA;MACA,KAAArB,QAAA,CAAAC,QAAA,GAAAoB,IAAA,CAAAtF,IAAA;MACA,KAAAiE,QAAA,CAAAE,YAAA,GAAAmB,IAAA,CAAAnB,YAAA;MAEA,IAAAmB,IAAA,CAAAtF,IAAA;QACA;QACA,KAAAiE,QAAA,CAAAG,aAAA,OAAAgG,mBAAA,CAAAlK,OAAA,EAAAoF,IAAA,CAAAlB,aAAA;MACA,WAAAkB,IAAA,CAAAtF,IAAA;QACA;QACA,KAAAiE,QAAA,CAAAI,qBAAA,OAAA+F,mBAAA,CAAAlK,OAAA,EAAAoF,IAAA,CAAAjB,qBAAA;QACA;QACA,KAAAsH,SAAA;UACAyC,MAAA,CAAAjH,uBAAA;QACA;MACA;MAEA,KAAAnD,iBAAA;MACA,KAAAoE,WAAA;QACAjF,OAAA;QACAC,QAAA;QACAuB,QAAA,EAAAC;MACA;MACA,KAAAyD,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACA+F,UAAA,WAAAA,WAAA/I,IAAA;MAAA,IAAAgJ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzO,IAAA;MACA,GAAA4I,IAAA;QACA,IAAA+B,KAAA,GAAA2D,MAAA,CAAAvK,KAAA,CAAA2K,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA/D,EAAA,KAAAtF,IAAA,CAAAsF,EAAA;QAAA;QACA,IAAAD,KAAA;UACA2D,MAAA,CAAAvK,KAAA,CAAA2H,MAAA,CAAAf,KAAA;UACA2D,MAAA,CAAA3G,QAAA,CAAAuC,OAAA;QACA;MACA,GAAAV,KAAA;QACA;MAAA,CACA;IACA;IAEA,YACAoF,eAAA,WAAAA,gBAAApK,UAAA,EAAAsJ,SAAA;MAAA,IAAAe,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzO,IAAA;MACA,GAAA4I,IAAA;QACA,IAAA+B,KAAA,GAAAnG,UAAA,CAAA7B,QAAA,CAAA+L,SAAA,WAAAlJ,KAAA;UAAA,OAAAA,KAAA,CAAAoF,EAAA,KAAAkD,SAAA,CAAAlD,EAAA;QAAA;QACA,IAAAD,KAAA;UACAnG,UAAA,CAAA7B,QAAA,CAAA+I,MAAA,CAAAf,KAAA;UACAkE,MAAA,CAAAlH,QAAA,CAAAuC,OAAA;QACA;MACA,GAAAV,KAAA;QACA;MAAA,CACA;IACA;IAEA,cACAsF,cAAA,WAAAA,eAAA;MACA,aAAAxK,gBAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA,iBACAyK,2BAAA,WAAAA,4BAAA;MACA;MACA;IACA;IAEA,iBACAC,kBAAA,WAAAA,mBAAA9K,QAAA;MACA;MACA,SAAAI,gBAAA;QACA,YAAAC,WAAA,CAAAvE,IAAA,KAAAkE,QAAA;MACA;;MAEA;MACA,SAAAI,gBAAA,sBAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAAxE,IAAA;UACA,OAAAkE,QAAA;QACA;QACA;QACA,SAAAM,UAAA,CAAAxE,IAAA;UACA,OAAAkE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAM,UAAA,CAAAxE,IAAA;MACA;;MAEA;MACA,SAAAsE,gBAAA;QACA;QACA,OAAAJ,QAAA;MACA;;MAEA;MACA;IACA;IAEA,iBACA+K,sBAAA,WAAAA,uBAAA1L,YAAA;MACA;MACA,SAAAe,gBAAA,uBAAAE,UAAA;QACA;MACA;;MAEA;MACA,IAAA0K,qBAAA;MACA,SAAA1K,UAAA,CAAA7B,QAAA;QACA,KAAA6B,UAAA,CAAA7B,QAAA,CAAA0C,OAAA,WAAAG,KAAA;UACA,IAAAA,KAAA,CAAAxF,IAAA,UAAAwF,KAAA,CAAAnB,qBAAA;YACA6K,qBAAA,CAAAlE,IAAA,CAAAmE,KAAA,CAAAD,qBAAA,MAAA9E,mBAAA,CAAAlK,OAAA,EAAAsF,KAAA,CAAAnB,qBAAA;UACA;QACA;MACA;;MAEA;MACA,QAAA6K,qBAAA,CAAAhK,QAAA,CAAA3B,YAAA;IACA;IAEA,iBACA6L,eAAA,WAAAA,gBAAAC,GAAA;MACA;MACA,SAAA/K,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA;QACA;MACA;;MAEA;MACA,aAAAuB,WAAA,CAAAX,QAAA,CAAAmK,GAAA,CAAAlK,MAAA;IACA;IAEA,iBACAmK,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;MACA;MACA,SAAA/K,gBAAA,oBAAAA,gBAAA,sBAAAuB,WAAA,CAAAX,QAAA,CAAAmK,GAAA,CAAAlK,MAAA;QACA;MACA;MACA;IACA;IAEA,eACAgC,uBAAA,WAAAA,wBAAA;MAAA,IAAAqI,MAAA;MAAA,WAAA5I,kBAAA,CAAA1G,OAAA,mBAAA2G,aAAA,CAAA3G,OAAA,IAAA4G,CAAA,UAAA2I,SAAA;QAAA,IAAA3K,aAAA,EAAA4K,KAAA,EAAAC,EAAA;QAAA,WAAA9I,aAAA,CAAA3G,OAAA,IAAA8G,CAAA,WAAA4I,SAAA;UAAA,kBAAAA,SAAA,CAAA1I,CAAA;YAAA;cAAA,MACAsI,MAAA,CAAAvL,QAAA,CAAAC,QAAA,WAAAsL,MAAA,CAAAvL,QAAA,CAAAI,qBAAA,CAAAkB,MAAA;gBAAAqK,SAAA,CAAA1I,CAAA;gBAAA;cAAA;cACAsI,MAAA,CAAA/K,wBAAA;cAAA,OAAAmL,SAAA,CAAAxI,CAAA;YAAA;cAIA;cACAtC,aAAA;cACA,IAAA0K,MAAA,CAAAlL,gBAAA,iBAAAkL,MAAA,CAAAhL,UAAA,IAAAgL,MAAA,CAAAhL,UAAA,CAAAxE,IAAA;gBACA;gBACA8E,aAAA,GAAA0K,MAAA,CAAAhL,UAAA,CAAAM,aAAA;cACA;gBACA;gBACAA,aAAA,GAAA0K,MAAA,CAAA1K,aAAA;cACA;cAAA,IAEAA,aAAA,CAAAS,MAAA;gBAAAqK,SAAA,CAAA1I,CAAA;gBAAA;cAAA;cACAsI,MAAA,CAAA/K,wBAAA;cACA+K,MAAA,CAAA7H,QAAA,CAAAoC,OAAA;cAAA,OAAA6F,SAAA,CAAAxI,CAAA;YAAA;cAAAwI,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAA1I,CAAA;cAAA,OAKAsI,MAAA,CAAAM,sBAAA,CAAAhL,aAAA,EAAA0K,MAAA,CAAAvL,QAAA,CAAAI,qBAAA;YAAA;cAAAqL,KAAA,GAAAE,SAAA,CAAAG,CAAA;cACAP,MAAA,CAAA/K,wBAAA,GAAAiL,KAAA;cAEA,IAAAA,KAAA;gBACAF,MAAA,CAAA7H,QAAA,CAAAoC,OAAA;cACA;cAAA6F,SAAA,CAAA1I,CAAA;cAAA;YAAA;cAAA0I,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAG,CAAA;cAEAC,OAAA,CAAAC,KAAA,cAAAN,EAAA;cACAH,MAAA,CAAA/K,wBAAA;cACA+K,MAAA,CAAA7H,QAAA,CAAAsI,KAAA;YAAA;cAAA,OAAAL,SAAA,CAAAxI,CAAA;UAAA;QAAA,GAAAqI,QAAA;MAAA;IAEA;IAEA,oBACAK,sBAAA,WAAAA,uBAAAI,KAAA,EAAA1C,aAAA;MAAA,WAAA5G,kBAAA,CAAA1G,OAAA,mBAAA2G,aAAA,CAAA3G,OAAA,IAAA4G,CAAA,UAAAqJ,SAAA;QAAA,IAAA5G,UAAA,EAAA6G,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAA1J,aAAA,CAAA3G,OAAA,IAAA8G,CAAA,WAAAwJ,SAAA;UAAA,kBAAAA,SAAA,CAAAtJ,CAAA;YAAA;cAAA,MACA,CAAAgJ,KAAA,CAAA3K,MAAA,KAAAiI,aAAA,CAAAjI,MAAA;gBAAAiL,SAAA,CAAAtJ,CAAA;gBAAA;cAAA;cAAA,OAAAsJ,SAAA,CAAApJ,CAAA,IACA;YAAA;cAGAmC,UAAA,MAEA;cAAA6G,UAAA,OAAApD,2BAAA,CAAA9M,OAAA,EACAgQ,KAAA;cAAAM,SAAA,CAAAX,CAAA;cAAAS,KAAA,oBAAAzJ,aAAA,CAAA3G,OAAA,IAAA4G,CAAA,UAAAwJ,MAAA;gBAAA,IAAArL,IAAA,EAAAwL,QAAA,EAAAC,UAAA,EAAAC,GAAA;gBAAA,WAAA9J,aAAA,CAAA3G,OAAA,IAAA8G,CAAA,WAAA4J,SAAA;kBAAA,kBAAAA,SAAA,CAAA1J,CAAA;oBAAA;sBAAAjC,IAAA,GAAAoL,MAAA,CAAA5N,KAAA;sBAAAmO,SAAA,CAAAf,CAAA;sBAAAe,SAAA,CAAA1J,CAAA;sBAAA,OAGA,IAAAmC,+BAAA,EAAApE,IAAA,CAAAE,MAAA;oBAAA;sBAAAsL,QAAA,GAAAG,SAAA,CAAAb,CAAA;sBACAC,OAAA,CAAAa,GAAA,gBAAAC,MAAA,CAAA7L,IAAA,CAAAE,MAAA,gCAAAsL,QAAA;sBAEA,IAAAA,QAAA,CAAAM,IAAA,YAAAN,QAAA,CAAAnQ,IAAA;wBACAoQ,UAAA,GAAAD,QAAA,CAAAnQ,IAAA,EAEA;wBACAkN,aAAA,CAAAnI,OAAA,WAAArF,IAAA;0BACA,QAAAA,IAAA;4BACA;8BAAA;8BACAuJ,UAAA,IAAAmH,UAAA,CAAAM,YAAA;8BACA;4BACA;8BAAA;8BACAzH,UAAA,IAAAmH,UAAA,CAAAO,cAAA;8BACA;4BACA;8BAAA;8BACA1H,UAAA,IAAAmH,UAAA,CAAAQ,QAAA;8BACA;0BACA;wBACA;sBACA;sBAAAN,SAAA,CAAA1J,CAAA;sBAAA;oBAAA;sBAAA0J,SAAA,CAAAf,CAAA;sBAAAc,GAAA,GAAAC,SAAA,CAAAb,CAAA;sBAEAC,OAAA,CAAAC,KAAA,4BAAAa,MAAA,CAAA7L,IAAA,CAAAE,MAAA,4CAAAwL,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAAxJ,CAAA;kBAAA;gBAAA,GAAAkJ,KAAA;cAAA;cAAAF,UAAA,CAAAlD,CAAA;YAAA;cAAA,KAAAmD,MAAA,GAAAD,UAAA,CAAAlJ,CAAA,IAAAiG,IAAA;gBAAAqD,SAAA,CAAAtJ,CAAA;gBAAA;cAAA;cAAA,OAAAsJ,SAAA,CAAAW,CAAA,KAAAC,mBAAA,CAAAlR,OAAA,EAAAoQ,KAAA;YAAA;cAAAE,SAAA,CAAAtJ,CAAA;cAAA;YAAA;cAAAsJ,SAAA,CAAAtJ,CAAA;cAAA;YAAA;cAAAsJ,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAAAK,UAAA,CAAA/C,CAAA,CAAAkD,GAAA;YAAA;cAAAC,SAAA,CAAAX,CAAA;cAAAO,UAAA,CAAA9C,CAAA;cAAA,OAAAkD,SAAA,CAAAlD,CAAA;YAAA;cAIA0C,OAAA,CAAAa,GAAA,oCAAAC,MAAA,CAAAvH,UAAA;cAAA,OAAAiH,SAAA,CAAApJ,CAAA,IACAmC,UAAA;UAAA;QAAA,GAAA4G,QAAA;MAAA;IACA;IAEA,eACAkB,qBAAA,WAAAA,sBAAAnN,QAAA;MACA;MACA,SAAAI,gBAAA;QACA,YAAAC,WAAA,CAAAvE,IAAA,KAAAkE,QAAA;MACA;;MAEA;MACA,SAAAI,gBAAA;QACA,OAAAJ,QAAA;MACA;MAEA;IACA;IAEA,cACAoN,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAApP,cAAA,CAAAkD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAAmL,QAAA,GAAAD,MAAA,CAAAnP,SAAA;MACA;IACA;IAEA,aACAqP,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAA9N,iBAAA,QAAAzB,cAAA,CAAA6C,MAAA,WAAA2M,CAAA;QAAA,OAAAA,CAAA,CAAAH,QAAA;MAAA;MACA,IAAA5N,iBAAA,CAAA2B,MAAA;QACA,KAAAoC,QAAA,CAAAoC,OAAA;QACA;MACA;MAEA,KAAAwE,QAAA,+CAAAuC,MAAA,CAAAlN,iBAAA,CAAA2B,MAAA;QACAiJ,iBAAA;QACAC,gBAAA;QACAzO,IAAA;MACA,GAAA4I,IAAA;QACA8I,OAAA,CAAAvP,cAAA,GAAAuP,OAAA,CAAAvP,cAAA,CAAA6C,MAAA,WAAA2M,CAAA;UAAA,QAAAA,CAAA,CAAAH,QAAA;QAAA;QACAE,OAAA,CAAAtP,SAAA;QACAsP,OAAA,CAAA/J,QAAA,CAAAuC,OAAA;MACA,GAAAV,KAAA;QACA;MAAA,CACA;IACA;IAEA,WACAoI,kBAAA,WAAAA,mBAAA;MACA,KAAAtP,sBAAA;MACA,KAAAuP,oBAAA;IACA;IAEA,gBACAA,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA/O,YAAA,CAAAC,gBAAA;MACA,KAAAD,YAAA,CAAAE,iBAAA;MACA,KAAAF,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAAX,YAAA,CAAAc,iBAAA;MACA,KAAAd,YAAA,CAAAe,aAAA;;MAEA;MACA,KAAAiO,gBAAA;;MAEA;MACA,KAAAC,6BAAA;IACA;IAEA,cACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,OAAA;MACA,IAAArJ,sBAAA;QAAAvF,QAAA;MAAA,GAAAwF,IAAA,WAAA6H,QAAA;QACA,IAAAjE,UAAA,GAAAiE,QAAA,CAAAvH,IAAA;QACA8I,OAAA,CAAAzP,eAAA,GAAAyP,OAAA,CAAAC,iBAAA,CAAAzF,UAAA;MACA,GAAAhD,KAAA,WAAAyG,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA+B,OAAA,CAAAzP,eAAA;MACA;IACA;IAEA,YACA0P,iBAAA,WAAAA,kBAAAzF,UAAA;MACA,IAAApD,GAAA;;MAEA;MACAoD,UAAA,CAAAnH,OAAA,WAAAiH,QAAA;QACAlD,GAAA,CAAAkD,QAAA,CAAA1B,EAAA,QAAAsH,cAAA,CAAAhS,OAAA,MAAAgS,cAAA,CAAAhS,OAAA,MAAAoM,QAAA;UAAA3J,QAAA;QAAA;MACA;;MAEA;MACA,IAAAiK,MAAA;MACAJ,UAAA,CAAAnH,OAAA,WAAAiH,QAAA;QACA,IAAAA,QAAA,CAAA1G,QAAA;UACA;UACAgH,MAAA,CAAA5B,IAAA,CAAA5B,GAAA,CAAAkD,QAAA,CAAA1B,EAAA;QACA;UACA;UACA,IAAAxB,GAAA,CAAAkD,QAAA,CAAA1G,QAAA;YACAwD,GAAA,CAAAkD,QAAA,CAAA1G,QAAA,EAAAjD,QAAA,CAAAqI,IAAA,CAAA5B,GAAA,CAAAkD,QAAA,CAAA1B,EAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAuH,mBAAA,YAAAA,mBAAAC,IAAA;QACA,IAAAA,IAAA,CAAAzP,QAAA,IAAAyP,IAAA,CAAAzP,QAAA,CAAA4C,MAAA;UACA,OAAA6M,IAAA,CAAAzP,QAAA;QACA,WAAAyP,IAAA,CAAAzP,QAAA,IAAAyP,IAAA,CAAAzP,QAAA,CAAA4C,MAAA;UACA6M,IAAA,CAAAzP,QAAA,CAAA0C,OAAA,WAAAG,KAAA;YAAA,OAAA2M,mBAAA,CAAA3M,KAAA;UAAA;QACA;MACA;;MAEA;MACAoH,MAAA,CAAAvH,OAAA,WAAA+M,IAAA;QAAA,OAAAD,mBAAA,CAAAC,IAAA;MAAA;MAEA,OAAAxF,MAAA;IACA;IAEA,gBACAyF,sBAAA,WAAAA,uBAAAhG,UAAA,EAAAG,UAAA;MACA,IAAAI,MAAA,IAAAP,UAAA;MAEA,IAAAiG,aAAA,YAAAA,aAAA1M,QAAA;QACA4G,UAAA,CAAAnH,OAAA,WAAAiH,QAAA;UACA,IAAAA,QAAA,CAAA1G,QAAA,KAAAA,QAAA;YACAgH,MAAA,CAAA5B,IAAA,CAAAsB,QAAA,CAAA1B,EAAA;YACA0H,aAAA,CAAAhG,QAAA,CAAA1B,EAAA;UACA;QACA;MACA;MAEA0H,aAAA,CAAAjG,UAAA;MACA,OAAAO,MAAA;IACA;IAEA,kBACAmF,6BAAA,WAAAA,8BAAA;MAAA,IAAAQ,OAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAA1P,YAAA,CAAAC,gBAAA;QACA;QACA,IAAA4F,sBAAA;UAAAvF,QAAA;QAAA,GAAAwF,IAAA,WAAA6H,QAAA;UACA,IAAAgC,aAAA,GAAAhC,QAAA,CAAAvH,IAAA;UACAsJ,WAAA,GAAAD,OAAA,CAAAF,sBAAA,CAAAE,OAAA,CAAAzP,YAAA,CAAAC,gBAAA,EAAA0P,aAAA;;UAEA;UACAF,OAAA,CAAAG,6BAAA,CAAAF,WAAA;QACA,GAAAhJ,KAAA,WAAAyG,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACAsC,OAAA,CAAAG,6BAAA;QACA;MACA;QACA;QACA,KAAAA,6BAAA;MACA;IACA;IAEA,mBACAA,6BAAA,WAAAA,8BAAAF,WAAA;MAAA,IAAAG,OAAA;MACA,IAAAvK,WAAA;QACAjF,OAAA,OAAAL,YAAA,CAAAI,cAAA,CAAAC,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAI,cAAA,CAAAE,QAAA;QACAuB,QAAA,OAAA7B,YAAA,CAAAE,iBAAA,IAAA4B;MACA;MAEA,IAAA4N,WAAA,CAAAjN,MAAA;QACA;QACA,IAAAqN,QAAA,GAAAJ,WAAA,CAAApJ,GAAA,WAAAiD,UAAA;UACA,WAAA3D,8BAAA,MAAAwJ,cAAA,CAAAhS,OAAA,MAAAgS,cAAA,CAAAhS,OAAA,MAAAkI,WAAA;YAAAiE,UAAA,EAAAA;UAAA;QACA;QAEA7D,OAAA,CAAAC,GAAA,CAAAmK,QAAA,EAAAhK,IAAA,WAAAiK,SAAA;UACA,IAAAC,QAAA;UACA,IAAAvJ,UAAA;UAEAsJ,SAAA,CAAAxN,OAAA,WAAAoL,QAAA;YACA,IAAAA,QAAA,CAAAvH,IAAA;cACA4J,QAAA,CAAA9H,IAAA,CAAAmE,KAAA,CAAA2D,QAAA,MAAA1I,mBAAA,CAAAlK,OAAA,EAAAuQ,QAAA,CAAAvH,IAAA;cACAK,UAAA,IAAAkH,QAAA,CAAApN,KAAA;YACA;UACA;;UAEA;UACA,IAAA0P,WAAA,GAAAD,QAAA,CAAA9N,MAAA,WAAAC,IAAA,EAAA0F,KAAA,EAAAqI,IAAA;YAAA,OACArI,KAAA,KAAAqI,IAAA,CAAAtE,SAAA,WAAAuE,CAAA;cAAA,OAAAA,CAAA,CAAA9N,MAAA,KAAAF,IAAA,CAAAE,MAAA;YAAA;UAAA,CACA;UAEAwN,OAAA,CAAA7P,YAAA,CAAAG,aAAA,GAAA8P,WAAA;UACAJ,OAAA,CAAA7P,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAA0P,WAAA,CAAAxN,MAAA;QACA,GAAAiE,KAAA,WAAAyG,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAAhL,QAAA,CAAAsI,KAAA;UACA0C,OAAA,CAAA7P,YAAA,CAAAG,aAAA;UACA0P,OAAA,CAAA7P,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;QACA;QACA,IAAAqF,8BAAA,EAAAN,WAAA,EAAAQ,IAAA,WAAA6H,QAAA;UACAkC,OAAA,CAAA7P,YAAA,CAAAG,aAAA,GAAAwN,QAAA,CAAAvH,IAAA;UACAyJ,OAAA,CAAA7P,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAAoN,QAAA,CAAApN,KAAA;QACA,GAAAmG,KAAA,WAAAyG,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAAhL,QAAA,CAAAsI,KAAA;UACA0C,OAAA,CAAA7P,YAAA,CAAAG,aAAA;UACA0P,OAAA,CAAA7P,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEA,WACA6P,mBAAA,WAAAA,oBAAA;MACA,KAAApQ,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAA4O,6BAAA;IACA;IAEA,WACAoB,kBAAA,WAAAA,mBAAA9D,GAAA;MACA,KAAAvM,YAAA,CAAAQ,cAAA,GAAA+L,GAAA,CAAAlK,MAAA;MACA,KAAAiO,aAAA;IACA;IAEA,aACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAAvQ,YAAA,CAAAQ,cAAA;QACA,KAAAR,YAAA,CAAAY,SAAA;QACA,KAAAZ,YAAA,CAAAa,kBAAA,CAAAN,KAAA;QACA;MACA;MAEA,IAAA+E,WAAA;QACAjF,OAAA,OAAAL,YAAA,CAAAa,kBAAA,CAAAR,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAa,kBAAA,CAAAP,QAAA;QACA+B,MAAA,OAAArC,YAAA,CAAAQ,cAAA;QACAC,YAAA,OAAAT,YAAA,CAAAS,YAAA,IAAAqB,SAAA;QACApB,UAAA,OAAAV,YAAA,CAAAU,UAAA,IAAAoB,SAAA;QACA0O,eAAA,OAAAxQ,YAAA,CAAAW,qBAAA,IAAAmB;MACA;MAEA,IAAA2O,sBAAA,EAAAnL,WAAA,EAAAQ,IAAA,WAAA6H,QAAA;QACA4C,OAAA,CAAAvQ,YAAA,CAAAY,SAAA,GAAA+M,QAAA,CAAAvH,IAAA;QACAmK,OAAA,CAAAvQ,YAAA,CAAAa,kBAAA,CAAAN,KAAA,GAAAoN,QAAA,CAAApN,KAAA;MACA,GAAAmG,KAAA,WAAAyG,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAoD,OAAA,CAAA1L,QAAA,CAAAsI,KAAA;QACAoD,OAAA,CAAAvQ,YAAA,CAAAY,SAAA;QACA2P,OAAA,CAAAvQ,YAAA,CAAAa,kBAAA,CAAAN,KAAA;MACA;IACA;IAEA,WACAmQ,eAAA,WAAAA,gBAAA;MACA,KAAA1Q,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAAiQ,aAAA;IACA;IAEA,aACAK,mBAAA,WAAAA,oBAAA;MACA,KAAA3Q,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAA+P,eAAA;IACA;IAEA,WACAE,kBAAA,WAAAA,mBAAA;MACA,KAAA/L,QAAA,CAAAC,IAAA;IACA;IAEA,SACA+L,UAAA,WAAAA,WAAA;MACA,KAAAhM,QAAA,CAAAC,IAAA;IACA;IAEA,aACAgM,mBAAA,WAAAA,oBAAA;MACA,KAAAjM,QAAA,CAAAC,IAAA;IACA;IAEA,SACAiM,YAAA,WAAAA,aAAA;MACA,KAAAlM,QAAA,CAAAC,IAAA;IACA;IAEA,YACAkM,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAA1R,UAAA,SAAAA,UAAA;MACA;MACA,KAAAF,cAAA,CAAAkD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAA2N,QAAA,GAAAD,OAAA,CAAA1R,UAAA;MACA;MACA,KAAAsF,QAAA,CAAAC,IAAA,UAAAkJ,MAAA,MAAAzO,UAAA;IACA;IAEA,aACA4R,6BAAA,WAAAA,8BAAA5I,SAAA;MACA,KAAAvI,YAAA,CAAAc,iBAAA,GAAAyH,SAAA;MACA,KAAA6I,mBAAA;IACA;IAEA,eACAA,mBAAA,WAAAA,oBAAA;MACA,IAAA9N,KAAA;MACA,KAAAtD,YAAA,CAAAc,iBAAA,CAAAyB,OAAA,WAAAgB,QAAA;QACA,IAAArG,IAAA,GAAAqG,QAAA,CAAA9C,YAAA;QACA6C,KAAA,CAAApG,IAAA,KAAAoG,KAAA,CAAApG,IAAA;MACA;MACA,KAAA8C,YAAA,CAAAe,aAAA,GAAAuC,KAAA;IACA;IAEA,aACA+N,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,SAAAtR,YAAA,CAAAc,iBAAA,CAAA2B,MAAA;QACA,KAAAoC,QAAA,CAAAoC,OAAA;QACA;MACA;;MAEA;MACA,KAAAjH,YAAA,CAAAc,iBAAA,CAAAyB,OAAA,WAAAgB,QAAA,EAAAsE,KAAA;QACAyJ,OAAA,CAAAjS,cAAA,CAAA6I,IAAA;UACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;UACA0J,UAAA,EAAAhO,QAAA,CAAAgO,UAAA;UACAC,OAAA,EAAAjO,QAAA,CAAAiN,eAAA;UACAtT,IAAA,EAAAqG,QAAA,CAAA9C,YAAA;UACAC,UAAA,EAAA6C,QAAA,CAAA7C,UAAA;UACA+Q,OAAA,EAAAlO,QAAA,CAAAkO,OAAA;UAAA;UACAC,KAAA;UAAA;UACAhD,QAAA;UACAwC,QAAA;QACA;MACA;MAEA,KAAA1R,sBAAA;MACA,KAAAqF,QAAA,CAAAuC,OAAA,6BAAA4G,MAAA,MAAAhO,YAAA,CAAAc,iBAAA,CAAA2B,MAAA;IACA;IAEA,eACAkP,oBAAA,WAAAA,qBAAAlO,GAAA;MACA,KAAAzD,YAAA,CAAAI,cAAA,CAAAE,QAAA,GAAAmD,GAAA;MACA,KAAAzD,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAA4O,6BAAA;IACA;IAEA,cACA2C,uBAAA,WAAAA,wBAAAnO,GAAA;MACA,KAAAzD,YAAA,CAAAI,cAAA,CAAAC,OAAA,GAAAoD,GAAA;MACA,KAAAwL,6BAAA;IACA;IAEA,eACA4C,wBAAA,WAAAA,yBAAApO,GAAA;MACA,KAAAzD,YAAA,CAAAa,kBAAA,CAAAP,QAAA,GAAAmD,GAAA;MACA,KAAAzD,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAAiQ,aAAA;IACA;IAEA,cACAwB,2BAAA,WAAAA,4BAAArO,GAAA;MACA,KAAAzD,YAAA,CAAAa,kBAAA,CAAAR,OAAA,GAAAoD,GAAA;MACA,KAAA6M,aAAA;IACA;IAEA,aACAyB,iBAAA,WAAAA,kBAAArR,UAAA;MACA,IAAAsR,aAAA,OAAAvU,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAA4U,aAAA,CAAAtR,UAAA;IACA;IAEA,aACAuR,mBAAA,WAAAA,oBAAA1O,QAAA;MACA;MACA,KAAA2O,YAAA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAA5O,QAAA;MACAA,QAAA,CAAA2N,QAAA,IAAA3N,QAAA,CAAA2N,QAAA;IACA;IAEA,aACAkB,cAAA,WAAAA,eAAA7O,QAAA,EAAAsE,KAAA;MAAA,IAAAwK,OAAA;MACA,KAAA5G,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzO,IAAA;MACA,GAAA4I,IAAA;QACAuM,OAAA,CAAAhT,cAAA,CAAAuJ,MAAA,CAAAf,KAAA;QACAwK,OAAA,CAAAxN,QAAA,CAAAuC,OAAA;MACA,GAAAV,KAAA;QACA;MAAA,CACA;IACA;IAEA,kBACA4L,YAAA,WAAAA,aAAAC,UAAA;MACA;QACA,WAAAA,UAAA;UACA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,UAAA;QACA;QACA,OAAAA,UAAA;MACA,SAAApF,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACA;MACA;IACA;IAEA,gBACAuF,oBAAA,WAAAA,qBAAAnG,GAAA;MACA;MACA,aAAAlN,cAAA,CAAAsT,IAAA,WAAApP,QAAA;QAAA,OAAAA,QAAA,CAAAgO,UAAA,KAAAhF,GAAA,CAAAgF,UAAA;MAAA;IACA;IAEA,iBACAqB,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAtG,GAAA,GAAAsG,KAAA,CAAAtG,GAAA;MACA;MACA,SAAAlN,cAAA,CAAAsT,IAAA,WAAApP,QAAA;QAAA,OAAAA,QAAA,CAAAgO,UAAA,KAAAhF,GAAA,CAAAgF,UAAA;MAAA;QACA;MACA;MACA;IACA;IAEA,YACAuB,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAA7V,IAAA,CAAAyL,OAAA;MACA,IAAAsK,MAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,OAAA;QACA,KAAAnO,QAAA,CAAAsI,KAAA;QACA;MACA;MACA,KAAA8F,MAAA;QACA,KAAApO,QAAA,CAAAsI,KAAA;QACA;MACA;MACA;IACA;IAEA,YACAgG,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAAjW,MAAA,CAAA8V,IAAA,CAAAI,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAApW,MAAA,CAAA8V,IAAA,CAAAO,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAtW,MAAA,CAAA8V,IAAA,CAAAS,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAxW,MAAA,CAAA8V,IAAA,CAAAW,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAA1W,MAAA,CAAA8V,IAAA,CAAAa,UAAA,IAAAR,QAAA;MACA,UAAAzF,MAAA,CAAAqF,IAAA,OAAArF,MAAA,CAAAuF,KAAA,OAAAvF,MAAA,CAAA0F,GAAA,OAAA1F,MAAA,CAAA4F,KAAA,OAAA5F,MAAA,CAAA8F,OAAA,OAAA9F,MAAA,CAAAgG,OAAA;IACA;IAEA,aACAtQ,aAAA,WAAAA,cAAA;MACA,SAAArG,OAAA;QACA;QACA6P,OAAA,CAAAa,GAAA,iBAAA1Q,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}