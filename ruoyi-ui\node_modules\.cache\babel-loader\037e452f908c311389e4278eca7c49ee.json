{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754446684938}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_questionBank", "require", "_category", "_question", "name", "props", "visible", "type", "Boolean", "default", "paperId", "String", "Number", "data", "_defineProperty2", "rightPanelCollapsed", "rightPanel<PERSON><PERSON>th", "activeCollapse", "paperForm", "paperName", "paperDesc", "paperType", "coverImg", "totalScore", "passScore", "startTime", "endTime", "duration", "lateLimit", "allowEarlySubmit", "earlySubmitTime", "showScore", "showType", "requireAllAnswered", "showResult", "showCorrect", "showAnswer", "showAnalysis", "status", "enableTimeLimit", "durationSeconds", "createTime", "fixedQuestions", "selectAll", "isExpanded", "showManualSelectDialog", "categoryOptions", "cascaderProps", "value", "label", "children", "checkStrictly", "emitPath", "manualSelect", "selectedCate<PERSON><PERSON>", "bankSearchKeyword", "questionBanks", "bankPagination", "pageNum", "pageSize", "total", "selectedBankId", "questionType", "difficulty", "questionSearchKeyword", "questions", "questionPagination", "selectedQuestions", "selectedStats", "showFixedRandomDialog", "fixedRandomRules", "fixedRandomForm", "ruleType", "generateType", "selectedItems", "selectedQuestionTypes", "showRuleDialog", "rules", "showAddRuleDialog", "ruleForm", "currentOperation", "editingRule", "parentRule", "currentQuestionTypeCount", "questionBankLoading", "bankName", "undefined", "computed", "selectedBanks", "_this", "filter", "bank", "includes", "bankId", "totalQuestions", "for<PERSON>ach", "rule", "length", "child", "selectedCount", "totalRuleScore", "parentRules", "parentId", "usedBankIds", "usedIds", "Set", "add", "Array", "from", "fixedQuestionStats", "stats", "question", "fixedRandomTotalQuestions", "fixedRandomTotalScore", "fixedRandomParentRules", "usedFixedRandomBankIds", "watch", "val", "loadPaperData", "handler", "newVal", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "updateQuestionTypeCount", "a", "deep", "methods", "handleBack", "$emit", "toggleRightPanel", "handleExamEntry", "$message", "info", "handlePageSetting", "handlePublish", "handleInviteStudents", "handleInviteList", "handleAddRule", "handleEditRules", "handleAddFirstRule", "queryParams", "searchKeyword", "loadQuestionBanks", "_this3", "Promise", "all", "listQuestionBank", "listCategory", "then", "_ref2", "_ref3", "_slicedToArray2", "bankResponse", "categoryResponse", "rows", "statisticsPromises", "map", "getQuestionStatistics", "questionCount", "totalCount", "catch", "banksWithStats", "handleSaveRules", "handleSaveRule", "_this4", "_callee2", "_context2", "warning", "updateExistingRule", "addFixedRandomRule", "addFixedRandomSubRule", "addNewRule", "success", "resetRuleForm", "_toConsumableArray2", "maxQuestions", "reduce", "sum", "scorePerQuestion", "_this5", "addSingleRule", "index", "id", "Date", "now", "updateRuleScore", "push", "_this6", "updateFixedRandomRuleScore", "addSingleFixedRandomRule", "$set", "handleSearch", "handleReset", "handleSelectionChange", "selection", "item", "removeSelectedBank", "_this7", "indexOf", "splice", "$nextTick", "table", "$refs", "questionBankTable", "rowToDeselect", "find", "toggleRowSelection", "handleCurrentChange", "page", "getCategoryName", "categoryId", "category", "findCategoryById", "categories", "flatCategories", "flattenCategories", "cat", "result", "flatten", "cats", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "err", "e", "f", "getQuestionTypeText", "questionTypes", "questionTypeMap", "isArray", "t", "join", "getChildRule<PERSON>abel", "childRule", "getRuleTypeLabel", "get<PERSON>ule<PERSON><PERSON>nt", "addSubRule", "defaultRuleType", "editRule", "_this8", "deleteRule", "_this9", "$confirm", "confirmButtonText", "cancelButtonText", "findIndex", "r", "deleteChildRule", "_this0", "getDialogTitle", "shouldShowRuleTypeSelection", "shouldShowRuleType", "shouldShowQuestionType", "existingQuestionTypes", "apply", "isRowSelectable", "row", "getRowClassName", "_ref4", "_this1", "_callee3", "count", "_t", "_context3", "p", "getQuestionCountByType", "v", "console", "error", "banks", "_callee4", "_iterator2", "_step2", "_loop", "_t3", "_context5", "response", "statistics", "_t2", "_context4", "log", "concat", "code", "singleChoice", "multipleChoice", "judgment", "d", "_regeneratorValues2", "shouldDisableRuleType", "handleSelectAll", "_this10", "selected", "handleDeleteSelected", "_this11", "q", "handleManualSelect", "initManualSelectData", "loadCategoryTree", "loadManualSelectQuestionBanks", "_this12", "buildCategoryTree", "_objectSpread2", "cleanEmptyChildren", "node", "getAllChildCategoryIds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this13", "categoryIds", "allCategories", "loadQuestionBanksByCategories", "_this14", "promises", "responses", "allBanks", "uniqueBanks", "self", "b", "searchQuestionBanks", "selectQuestionBank", "loadQuestions", "_this15", "questionContent", "listQuestion", "searchQuestions", "resetQuestionSearch", "handleRandomSelect", "resetFixedRandomForm", "handleAddFixedRandomRule", "handleAddFixedRandomSubRule", "deleteFixedRandomRule", "_this16", "childIndex", "deleteFixedRandomChildRule", "handleConfirmFixedRandomSelect", "_this17", "_callee5", "_t4", "_context6", "extractQuestionsFromRules", "questionId", "content", "options", "score", "expanded", "_this18", "_callee6", "allQuestions", "_iterator3", "_step3", "_loop2", "_t0", "_context9", "childRules", "_iterator4", "_step4", "_loop3", "_iterator7", "_step7", "shuffled", "_t7", "_t8", "_t9", "_context8", "_iterator5", "_step5", "_iterator6", "_step6", "_t5", "_t6", "_context7", "shuffle<PERSON><PERSON><PERSON>", "Math", "min", "slice", "array", "i", "j", "floor", "random", "_ref5", "handleSort", "handleBatchSetScore", "handleExport", "handleToggleExpand", "_this19", "handleQuestionSelectionChange", "updateSelectedStats", "confirmManualSelect", "_this20", "handleBankSizeChange", "handleBankCurrentChange", "handleQuestionSizeChange", "handleQuestionCurrentChange", "getDifficultyText", "difficultyMap", "updateQuestionScore", "$forceUpdate", "toggleQuestionExpand", "deleteQuestion", "_this21", "parseOptions", "optionsStr", "JSON", "parse", "isQuestionSelectable", "some", "getQuestionRowClassName", "_ref6", "beforeUpload", "file", "isImage", "isLt2M", "size", "formatDate", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/biz/paper/create.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-editor\">\n    <!-- 左侧主要内容区域 -->\n    <div class=\"exam-editor-left\" :class=\"{ collapsed: rightPanelCollapsed }\">\n      <!-- 顶部操作栏 -->\n      <div class=\"editPaper_main_top\">\n        <div class=\"el-button-group\">\n          <el-button type=\"primary\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i>\n            <span>返回试卷</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button @click=\"handleExamEntry\">\n            <i class=\"el-icon-share\"></i>\n            <span>考试入口</span>\n          </el-button>\n          <el-button @click=\"handlePageSetting\">\n            <i class=\"el-icon-picture-outline\"></i>\n            <span>设置考试页面</span>\n          </el-button>\n          <el-button type=\"warning\" @click=\"handlePublish\">\n            <i class=\"el-icon-upload\"></i>\n            <span>发布</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button type=\"success\" @click=\"handleInviteStudents\">\n            邀请考生\n          </el-button>\n          <el-button type=\"warning\" @click=\"handleInviteList\">\n            已邀请列表\n          </el-button>\n        </div>\n        \n        <div class=\"clear_both\"></div>\n      </div>\n\n      <!-- 试卷信息卡片 -->\n      <div class=\"subject_main-wrapper\">\n        <div class=\"subject_main\">\n          <el-card class=\"is-hover-shadow\" style=\"width: 100%;\">\n            <div class=\"subtitle-title\" style=\"padding: 10px;\">\n              <div>\n                <div style=\"float: left;\">\n                  <strong class=\"slgfont\">{{ paperForm.paperName || '新建试卷' }}</strong>\n                </div>\n                <div style=\"float: right; color: #ec661a; width: 130px; text-align: right;\">\n                  <span style=\"font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;\">{{ totalRuleScore }}</span>分\n                </div>\n                <div style=\"clear: both;\"></div>\n                <div class=\"paper-count\" style=\"font-size: 13px; color: #aaa;\">\n                  <span v-if=\"paperForm.createTime\">创建时间：{{ paperForm.createTime }}</span>\n                  <el-tag type=\"warning\" size=\"medium\" effect=\"light\">共 {{ totalQuestions }} 题</el-tag>\n                  <el-tag type=\"success\" size=\"medium\" effect=\"light\">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>\n                  <el-tag size=\"medium\" effect=\"light\">自动出分</el-tag>\n                </div>\n                <div class=\"rich-text\" style=\"display: none;\"></div>\n              </div>\n              <div style=\"clear: both;\"></div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 题目设置区域 -->\n        <div>\n          <div class=\"mb10 mt10\">\n            <div class=\"el-button-group\"></div>\n          </div>\n          \n          <div v-if=\"paperForm.paperType === 1\">\n            <!-- 随机试卷 -->\n            <div class=\"random-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 有规则时显示规则内容 -->\n                <div v-if=\"rules.length > 0\" style=\"padding: 10px;\">\n                  <div class=\"tac pd5\">\n                    <div>\n                      <el-button type=\"primary\" @click=\"handleEditRules\">\n                        <i class=\"el-icon-edit\"></i>\n                        <span>编辑规则</span>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 规则显示区域 -->\n                  <div v-for=\"rule in parentRules\" :key=\"rule.id\" class=\"rule-display-container\">\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule-display\">\n                      <span class=\"rule-label\">{{ getRuleTypeLabel(rule) }}</span>\n                      <span class=\"rule-content\">{{ getRuleContent(rule) }}</span>\n                    </div>\n\n                    <!-- 子规则显示 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules-display\">\n                      <div v-for=\"childRule in rule.children\" :key=\"childRule.id\" class=\"child-rule-display\">\n                        <div class=\"child-rule-left\">\n                          <span class=\"rule-label\">{{ getRuleTypeLabel(childRule) }}</span>\n                          <span class=\"rule-content\">{{ getRuleContent(childRule) }}</span>\n                        </div>\n                        <div class=\"child-rule-right\">\n                          <span class=\"rule-stats\">\n                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，\n                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，\n                            总分 <strong>{{ childRule.totalScore }}</strong> 分\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 没有规则时显示添加按钮 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 10px;\">\n                  <div>\n                    <div class=\"mb10\">点击添加规则设置本试卷的抽题规则</div>\n                    <el-button type=\"primary\" @click=\"handleAddRule\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>添加规则</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n          \n          <div v-else>\n            <!-- 固定试卷 -->\n            <div class=\"fixed-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 操作栏 -->\n                <div class=\"mb10 mt10\">\n                  <div class=\"el-button-group\">\n                    <label class=\"el-checkbox fl el-checkbox--medium is-bordered\" style=\"padding: 7.5px 20px;\">\n                      <span class=\"el-checkbox__input\">\n                        <span class=\"el-checkbox__inner\"></span>\n                        <input type=\"checkbox\" aria-hidden=\"false\" class=\"el-checkbox__original\" value=\"\" v-model=\"selectAll\" @change=\"handleSelectAll\">\n                      </span>\n                      <span class=\"el-checkbox__label\">全选</span>\n                    </label>\n                    <el-tooltip content=\"删除选中题目\" placement=\"top\">\n                      <el-button type=\"danger\" size=\"medium\" @click=\"handleDeleteSelected\">\n                        <i class=\"el-icon-delete\"></i>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n\n                  <div class=\"el-button-group\">\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>手动选题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleRandomSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>随机抽题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleSort\">\n                      <i class=\"el-icon-sort\"></i>\n                      <span>排序</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleBatchSetScore\">\n                      <i class=\"icon_size iconfont icon-batch-add\"></i>\n                      <span>批量设置分数</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleExport\">\n                      <i class=\"icon_size iconfont icon-daochu\"></i>\n                      <span>导出</span>\n                    </el-button>\n                  </div>\n\n                  <el-button type=\"default\" size=\"medium\" style=\"vertical-align: middle;\" @click=\"handleToggleExpand\">\n                    <i class=\"icon_size iconfont icon-zhankai\"></i>\n                    <span>{{ isExpanded ? '收起' : '展开' }}</span>\n                  </el-button>\n                </div>\n\n                <!-- 题目列表区域 -->\n                <div v-if=\"fixedQuestions.length > 0\" class=\"question-list\">\n                  <!-- 这里将显示已添加的题目列表 -->\n                  <div class=\"question-item\" v-for=\"(question, index) in fixedQuestions\" :key=\"question.id\">\n                    <!-- 题目头部 -->\n                    <div class=\"question-header\">\n                      <div class=\"question-header-left\">\n                        <el-checkbox v-model=\"question.selected\"></el-checkbox>\n                        <span class=\"question-number\">{{ index + 1 }}.</span>\n                        <span class=\"question-content\">{{ question.content }}</span>\n                      </div>\n                      <div class=\"question-header-right\">\n                        <!-- 分数设置 -->\n                        <el-input-number\n                          v-model=\"question.score\"\n                          :min=\"0.5\"\n                          :step=\"0.5\"\n                          size=\"mini\"\n                          style=\"width: 100px;\"\n                          @change=\"updateQuestionScore(question)\"\n                        ></el-input-number>\n                        <span style=\"margin-left: 5px; margin-right: 10px;\">分</span>\n\n                        <!-- 展开/收起按钮 -->\n                        <el-tooltip :content=\"question.expanded ? '收起' : '展开'\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"toggleQuestionExpand(question)\"\n                            style=\"margin-right: 5px;\"\n                          >\n                            <i :class=\"question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                          </el-button>\n                        </el-tooltip>\n\n                        <!-- 删除按钮 -->\n                        <el-tooltip content=\"删除\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"deleteQuestion(question, index)\"\n                            style=\"color: #f56c6c;\"\n                          >\n                            <i class=\"el-icon-delete\"></i>\n                          </el-button>\n                        </el-tooltip>\n                      </div>\n                    </div>\n\n                    <!-- 题目详情（展开时显示） -->\n                    <div v-if=\"question.expanded\" class=\"question-details\">\n                      <div class=\"question-info\">\n                        <span class=\"info-item\">题型：{{ getQuestionTypeText(question.type) }}</span>\n                        <span class=\"info-item\">难度：{{ getDifficultyText(question.difficulty) }}</span>\n                      </div>\n\n                      <!-- 选项显示 -->\n                      <div v-if=\"question.options\" class=\"question-options\">\n                        <div class=\"options-title\">选项：</div>\n                        <div\n                          v-for=\"(option, optIndex) in parseOptions(question.options)\"\n                          :key=\"optIndex\"\n                          class=\"option-item\"\n                          :class=\"{ 'correct-option': option.isCorrect }\"\n                        >\n                          <span class=\"option-key\">{{ option.key }}.</span>\n                          <span class=\"option-content\">{{ option.content }}</span>\n                          <span v-if=\"option.isCorrect\" class=\"correct-mark\">✓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 40px 10px;\">\n                  <div>\n                    <div class=\"mb10\">暂无题目，点击上方按钮添加题目到试卷中</div>\n                    <el-button type=\"primary\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>开始添加题目</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧设置面板 -->\n    <div class=\"exam-editor-right\" :style=\"{ width: rightPanelWidth + 'px' }\">\n      <i \n        :class=\"rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'\" \n        class=\"collapse-button\" \n        title=\"收起/展开\"\n        @click=\"toggleRightPanel\"\n      ></i>\n      \n      <div v-show=\"!rightPanelCollapsed\">\n        <div class=\"editor-header editor-header--big\">\n          <span>考试设置</span>\n          <i class=\"el-icon-close close-button\" @click=\"handleBack\" title=\"关闭\"></i>\n        </div>\n        \n        <div class=\"main\">\n          <el-form class=\"h100p\">\n            <el-collapse v-model=\"activeCollapse\" class=\"main-collapse h100p\" accordion>\n              <!-- 基础设置 -->\n              <el-collapse-item title=\"基础设置\" name=\"basic\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-setting\" style=\"color: #67c23a;\"></i>基础设置\n                  </div>\n                </template>\n\n                <div class=\"main-module\">\n                  <!-- 封面图 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>封面图</b>\n                      <div class=\"input-area\">\n                        <div style=\"margin-top: 20px;\">\n                          <span class=\"dpib\" style=\"width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;\">\n                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2\n                          </span>\n                          <div class=\"g-component-cover-uploader dpib\">\n                            <div class=\"avatar-uploader\" style=\"width: 120px; height: 80px;\">\n                              <el-upload\n                                class=\"avatar-uploader\"\n                                action=\"#\"\n                                :show-file-list=\"false\"\n                                :before-upload=\"beforeUpload\"\n                                accept=\".png, .jpeg, .jpg\"\n                              >\n                                <div class=\"image_area\">\n                                  <img v-if=\"paperForm.coverImg\" :src=\"paperForm.coverImg\" class=\"avatar\">\n                                  <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                                </div>\n                              </el-upload>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷名称 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷名称</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperName\"\n                          placeholder=\"请输入试卷名称\"\n                          maxlength=\"100\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷描述 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷描述</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperDesc\"\n                          type=\"textarea\"\n                          :rows=\"3\"\n                          placeholder=\"请输入试卷描述\"\n                          maxlength=\"500\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 出题方式 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>出题方式</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"paper-type-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">出题方式</div>\n                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>\n                            <br>\n                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-radio-group v-model=\"paperForm.paperType\" size=\"mini\">\n                          <el-radio-button :label=\"1\">随机试卷</el-radio-button>\n                          <el-radio-button :label=\"0\">固定试卷</el-radio-button>\n                        </el-radio-group>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n                  <!-- 时长 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>时长(按卷限时)</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">时长</div>\n                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>\n                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.duration\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                        <el-input-number v-model=\"paperForm.durationSeconds\" :min=\"0\" :max=\"59\" size=\"mini\" style=\"width: 85px;\"></el-input-number> 秒\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 及格分 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>及格分</b>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.passScore\" :min=\"0\" :max=\"paperForm.totalScore || 100\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>考试时间</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">考试时间</div>\n                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.enableTimeLimit\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间子项容器 -->\n                  <div class=\"block-expand\" v-if=\"paperForm.enableTimeLimit\">\n                    <!-- 考试开始时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试开始时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.startTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择开始时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 考试结束时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试结束时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.endTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择结束时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n\n                  </div>\n\n                  <!-- 提前交卷 -->\n                  <div class=\"setting-block\" v-if=\"paperForm.enableTimeLimit\">\n                    <div class=\"line block-header\">\n                      <b>提前交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch\n                          v-model=\"paperForm.allowEarlySubmit\"\n                          :active-value=\"1\"\n                          :inactive-value=\"0\"\n                        ></el-switch>\n                        <span v-if=\"paperForm.allowEarlySubmit\" style=\"margin-left: 10px;\">\n                          <el-input-number\n                            v-model=\"paperForm.earlySubmitTime\"\n                            :min=\"1\"\n                            :max=\"60\"\n                            size=\"mini\"\n                            style=\"width: 100px;\"\n                          ></el-input-number> 分钟\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示分值 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示分值</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showScore\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示题型 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示题型</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showType\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 迟到限制 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>迟到限制</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"late-limit-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">迟到限制</div>\n                            <div>设置迟到多少分钟后，禁止再进入考试。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.lateLimit\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分钟\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试中设置 -->\n              <el-collapse-item title=\"考试中\" name=\"during\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-edit-outline\" style=\"color: #409eff;\"></i>考试中\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n\n\n                  <!-- 全部答完才能交卷 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>全部答完才能交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.requireAllAnswered\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试后设置 -->\n              <el-collapse-item title=\"考试后\" name=\"after\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-finished\" style=\"color: #e6a23c;\"></i>考试后\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n                  <!-- 显示成绩 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示成绩</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showResult\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示对错 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示对错</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showCorrect\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示答案 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示答案</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnswer\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示解析 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示解析</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnalysis\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </el-form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 随机规则设置对话框 -->\n    <el-dialog\n      title=\"随机规则设置\"\n      :visible.sync=\"showRuleDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"新版规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFirstRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加一级规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ totalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ totalRuleScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"确定一级规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      按题库、按题型、按难度、按知识点抽题四选一\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"添加子规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      在每个上级规则基础上继续添加子规则\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"保存抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      各级规则设置完成后即可保存规则开始抽题\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"rules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in parentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"addSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}\n                          </div>\n                          <div class=\"rule-content\" v-if=\"childRule.type === 3\">\n                            <span>\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"rules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加一级规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showRuleDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleSaveRules\">保存规则</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 手动选题对话框 -->\n    <el-dialog\n      title=\"选择题目\"\n      :visible.sync=\"showManualSelectDialog\"\n      width=\"1200px\"\n      :close-on-click-modal=\"false\"\n      class=\"checked-question\"\n      top=\"15vh\"\n      :append-to-body=\"true\"\n      :z-index=\"3200\"\n    >\n      <div style=\"display: flex; height: 550px;\">\n        <!-- 左侧：题库列表 -->\n        <div style=\"width: 300px; padding-right: 10px;\">\n          <!-- 搜索区域 -->\n          <div style=\"padding: 5px; height: 42px; display: flex; gap: 10px;\">\n            <el-cascader\n              v-model=\"manualSelect.selectedCategory\"\n              :options=\"categoryOptions\"\n              :props=\"cascaderProps\"\n              placeholder=\"选择题库分类\"\n              size=\"small\"\n              style=\"width: 135px;\"\n              clearable\n              @change=\"searchQuestionBanks\"\n            ></el-cascader>\n\n            <el-input\n              v-model=\"manualSelect.bankSearchKeyword\"\n              placeholder=\"题库名称\"\n              size=\"small\"\n              style=\"width: 150px;\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchQuestionBanks\"></el-button>\n            </el-input>\n          </div>\n\n          <!-- 题库表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questionBanks\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @row-click=\"selectQuestionBank\"\n              highlight-current-row\n            >\n              <el-table-column type=\"index\" width=\"38\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"left\" header-align=\"center\"></el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding-top: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleBankSizeChange\"\n              @current-change=\"handleBankCurrentChange\"\n              :current-page=\"manualSelect.bankPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              :page-size=\"manualSelect.bankPagination.pageSize\"\n              :total=\"manualSelect.bankPagination.total\"\n              layout=\"prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 中间：题目列表 -->\n        <div style=\"width: 700px; padding: 0px 5px;\">\n          <!-- 筛选区域 -->\n          <div style=\"padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;\">\n            <el-select\n              v-model=\"manualSelect.questionType\"\n              placeholder=\"题型\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部题型\" value=\"\"></el-option>\n              <el-option label=\"单选题\" value=\"1\"></el-option>\n              <el-option label=\"多选题\" value=\"2\"></el-option>\n              <el-option label=\"判断题\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-select\n              v-model=\"manualSelect.difficulty\"\n              placeholder=\"难度\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部难度\" value=\"\"></el-option>\n              <el-option label=\"低\" value=\"1\"></el-option>\n              <el-option label=\"中\" value=\"2\"></el-option>\n              <el-option label=\"高\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-input\n              v-model=\"manualSelect.questionSearchKeyword\"\n              placeholder=\"搜索题目\"\n              size=\"small\"\n              style=\"width: 250px;\"\n            >\n              <template slot=\"append\">\n                <el-button @click=\"searchQuestions\" style=\"border-left: 1px solid #dcdfe6;\">搜索</el-button>\n                <el-button @click=\"resetQuestionSearch\" style=\"border-left: 1px solid #dcdfe6;\">重置</el-button>\n              </template>\n            </el-input>\n          </div>\n\n          <!-- 题目表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questions\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @selection-change=\"handleQuestionSelectionChange\"\n              :row-class-name=\"getQuestionRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"40\" :selectable=\"isQuestionSelectable\"></el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题干\" min-width=\"400\" header-align=\"center\" class-name=\"question-content-column\">\n                <template slot-scope=\"scope\">\n                  <div class=\"question-content-text\">\n                    {{ scope.row.questionContent }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionType\" label=\"题目类型\" width=\"78\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getQuestionTypeText(scope.row.questionType) }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"50\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getDifficultyText(scope.row.difficulty) }}\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleQuestionSizeChange\"\n              @current-change=\"handleQuestionCurrentChange\"\n              :current-page=\"manualSelect.questionPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 40, 50, 100]\"\n              :page-size=\"manualSelect.questionPagination.pageSize\"\n              :total=\"manualSelect.questionPagination.total\"\n              layout=\"sizes, prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 右侧：统计信息 -->\n        <div style=\"width: 150px; padding: 0px 5px;\">\n          <div style=\"padding: 7px 0px; height: 42px; font-size: 16px;\">试卷题型统计</div>\n          <div style=\"border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;\">\n            <!-- 显示已添加到试卷的题目统计 -->\n            <div v-for=\"(count, type) in fixedQuestionStats\" :key=\"type\" style=\"margin-bottom: 10px;\">\n              <div style=\"font-size: 14px; color: #606266;\">\n                {{ getQuestionTypeText(type) }}：{{ count }} 题\n              </div>\n            </div>\n\n            <!-- 显示当前选择的题目统计 -->\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;\">\n              <div style=\"font-size: 13px; color: #909399; margin-bottom: 8px;\">本次选择：</div>\n              <div v-for=\"(count, type) in manualSelect.selectedStats\" :key=\"'selected-' + type\" style=\"margin-bottom: 8px;\">\n                <div style=\"font-size: 13px; color: #409eff;\">\n                  {{ getQuestionTypeText(type) }}：{{ count }} 题\n                </div>\n              </div>\n            </div>\n          </div>\n          <div style=\"width: 140px; text-align: right; padding-right: 5px;\">\n            <div style=\"font-size: 14px; font-weight: bold;\">总题数：{{ fixedQuestions.length }} 题</div>\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"font-size: 12px; color: #409eff;\">\n              +{{ manualSelect.selectedQuestions.length }} 题\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showManualSelectDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"confirmManualSelect\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加规则对话框 -->\n    <el-dialog\n      :title=\"getDialogTitle()\"\n      :visible.sync=\"showAddRuleDialog\"\n      width=\"900px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3100\"\n    >\n      <div style=\"margin-bottom: -30px;\">\n        <el-form :model=\"ruleForm\" label-width=\"140px\">\n          <!-- 选择规则类型 -->\n          <el-form-item v-if=\"shouldShowRuleTypeSelection()\" label=\"选择规则类型\">\n            <el-radio-group v-model=\"ruleForm.ruleType\">\n              <el-radio\n                v-if=\"shouldShowRuleType(3)\"\n                :label=\"3\"\n                :disabled=\"shouldDisableRuleType(3)\"\n              >题库</el-radio>\n              <el-radio\n                v-if=\"shouldShowRuleType(1)\"\n                :label=\"1\"\n                :disabled=\"shouldDisableRuleType(1)\"\n              >题型</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 规则生成方式 -->\n          <el-form-item v-if=\"currentOperation !== 'addSub'\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                规则生成方式\n              </div>\n            </template>\n            <el-radio-group v-model=\"ruleForm.generateType\">\n              <el-radio label=\"divided\">分开设置</el-radio>\n              <el-radio label=\"one\">整体设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 已选择的题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"点击选项可取消选择。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                已选择的题库\n              </div>\n            </template>\n            <span v-if=\"ruleForm.selectedItems.length === 0\" class=\"ml20\">\n              暂无选择，请至少选择一项。\n            </span>\n            <div v-else>\n              <el-tag\n                v-for=\"item in selectedBanks\"\n                :key=\"item.bankId\"\n                closable\n                @close=\"removeSelectedBank(item.bankId)\"\n                type=\"info\"\n                size=\"medium\"\n                class=\"selected-bank-tag\"\n              >\n                {{ item.bankName }}\n              </el-tag>\n            </div>\n          </el-form-item>\n\n          <!-- 选择题型 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 1\" label=\"选择题型\">\n            <el-checkbox-group v-model=\"ruleForm.selectedQuestionTypes\">\n              <el-checkbox v-if=\"shouldShowQuestionType('1')\" label=\"1\">单选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('2')\" label=\"2\">多选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('3')\" label=\"3\">判断题</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n\n\n\n          <!-- 选择题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\" label=\"选择题库\">\n            <!-- 搜索区域 -->\n            <div class=\"clearfix\" style=\"margin-bottom: 6px;\">\n              <div style=\"float: right; display: flex; align-items: center;\">\n                <el-input\n                  v-model=\"searchKeyword\"\n                  placeholder=\"题库名称\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 8px;\"\n                  @keyup.enter.native=\"handleSearch\"\n                  clearable\n                >\n                  <el-button slot=\"append\" @click=\"handleSearch\">搜索</el-button>\n                </el-input>\n                <el-button size=\"small\" @click=\"handleReset\">重置</el-button>\n              </div>\n            </div>\n\n            <!-- 题库表格 -->\n            <el-table\n              ref=\"questionBankTable\"\n              :data=\"questionBanks\"\n              border\n              size=\"small\"\n              max-height=\"300\"\n              v-loading=\"questionBankLoading\"\n              @selection-change=\"handleSelectionChange\"\n              :row-class-name=\"getRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"40\" label=\"#\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" :selectable=\"isRowSelectable\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"questionCount\" label=\"题目数量\" width=\"80\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.questionCount || 0 }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分类\" width=\"150\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getCategoryName(scope.row.categoryId) }}\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <!-- 分页 -->\n            <div style=\"text-align: right; margin-top: 10px;\">\n              <el-pagination\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"queryParams.pageNum\"\n                :page-size=\"queryParams.pageSize\"\n                layout=\"total, prev, pager, next\"\n                :total=\"total\"\n                small\n                background\n              >\n              </el-pagination>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showAddRuleDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSaveRule\">保 存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 固定试卷随机抽题对话框 -->\n    <el-dialog\n      title=\"随机抽题规则设置\"\n      :visible.sync=\"showFixedRandomDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"随机抽题规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFixedRandomRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加抽题规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ fixedRandomTotalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ fixedRandomTotalScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"设置抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      选择题库和题型，设置抽题数量和分数\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"确认抽题\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      根据规则随机抽取题目并添加到试卷\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"fixedRandomParentRules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in fixedRandomParentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          题库：\n                        </div>\n                        <div class=\"rule-content\">\n                          <span>\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"handleAddFixedRandomSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}：\n                          </div>\n                          <div class=\"rule-content\">\n                            <span v-if=\"childRule.type === 3\">\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                            <span v-else-if=\"childRule.type === 1\">\n                              选取\n                            </span>\n                          </div>\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"fixedRandomRules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加抽题规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showFixedRandomDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleConfirmFixedRandomSelect\">确认抽题</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n/* 表格垂直对齐优化 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {\n  vertical-align: middle;\n}\n\n/* 多选框垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n/* 序号列垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n/* 规则节点样式 */\n.rule-node-cascade {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.rule-node-cascade:hover {\n  border: 1px dashed #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n/* 左侧题库信息 */\n.rule-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.rule-type-label {\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n  min-width: 50px;\n}\n\n.rule-content {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 右侧操作区域 */\n.rule-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rule-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.control-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  white-space: nowrap;\n}\n\n.control-divider {\n  color: #999;\n  margin: 0 4px;\n}\n\n.total-score {\n  font-weight: 600;\n  color: #333;\n}\n\n.input-number-mini {\n  width: 110px !important;\n}\n\n.rule-actions {\n  display: flex;\n  gap: 2px;\n}\n\n.action-btn {\n  color: #409eff !important;\n  padding: 4px 6px !important;\n  margin-left: 0 !important;\n}\n\n.action-btn:hover {\n  background-color: #ecf5ff !important;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n/* 子规则样式 */\n.children-rules {\n  margin-top: 12px;\n  margin-left: 20px;\n  border-left: 2px dashed #e8e8e8;\n  padding-left: 20px;\n}\n\n.children_component {\n  margin-bottom: 8px !important;\n  border: 1px dashed #d9d9d9 !important;\n  background-color: #f9f9f9 !important;\n}\n\n.children_component:hover {\n  border: 1px dashed #409eff !important;\n  background-color: #ecf5ff !important;\n}\n\n.parent-rule {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 禁用的表格行样式 */\n::v-deep .disabled-row {\n  background-color: #f5f5f5 !important;\n  color: #c0c4cc !important;\n}\n\n::v-deep .disabled-row:hover {\n  background-color: #f5f5f5 !important;\n}\n\n::v-deep .disabled-row td {\n  color: #c0c4cc !important;\n}\n\n/* random-paper 规则显示样式 */\n.rule-display-container {\n  border: 1px dashed #d0d0d0;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #fafbfc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 题库容器悬停效果 */\n.rule-display-container:hover {\n  border-color: #409eff;\n  background-color: #f8fbff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.rule-display-container:hover .parent-rule-display {\n  background-color: #e3f2fd;\n  border-left-color: #1976d2;\n}\n\n.rule-display-container:hover .parent-rule-display .rule-label {\n  color: #1976d2;\n}\n\n.parent-rule-display {\n  font-size: 14px;\n  color: #303133;\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n  border-left: 4px solid #409eff;\n}\n\n.children-rules-display {\n  margin-left: 20px;\n}\n\n.child-rule-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px;\n  margin-bottom: 8px;\n  background-color: #ffffff;\n  border: 1px dashed #d0d0d0;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.child-rule-display:last-child {\n  margin-bottom: 0;\n}\n\n/* 子规则悬停效果 */\n.child-rule-display:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.child-rule-display:hover .rule-label {\n  color: #1976d2;\n}\n\n.child-rule-display:hover .rule-stats {\n  background-color: #e3f2fd;\n  border-color: #90caf9;\n}\n\n.child-rule-left {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.child-rule-right {\n  flex-shrink: 0;\n  margin-left: 20px;\n}\n\n.rule-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.rule-content {\n  color: #303133;\n}\n\n.rule-stats {\n  font-size: 13px;\n  color: #606266;\n  background-color: #f0f9ff;\n  padding: 4px 8px;\n  border-radius: 5px;\n  border: 1px solid #b3d8ff;\n  white-space: nowrap;\n}\n\n.rule-stats strong {\n  color: #409eff;\n  font-weight: 600;\n}\n\n/* 固定试卷样式 */\n.question-list {\n  margin-top: 20px;\n}\n\n.question-item {\n  margin-bottom: 12px;\n  background-color: #fafafa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.question-item:hover {\n  background-color: #f0f9ff;\n  border-color: #409eff;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 15px;\n}\n\n.question-header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.question-header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.question-number {\n  font-weight: bold;\n  color: #409eff;\n  margin-left: 10px;\n  margin-right: 10px;\n  min-width: 30px;\n}\n\n.question-content {\n  flex: 1;\n  color: #303133;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-right: 15px;\n}\n\n.question-details {\n  border-top: 1px solid #e4e7ed;\n  padding: 15px;\n  background-color: #ffffff;\n}\n\n.question-info {\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: inline-block;\n  margin-right: 20px;\n  font-size: 13px;\n  color: #606266;\n}\n\n.question-options {\n  margin-top: 10px;\n}\n\n.options-title {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.option-item.correct-option {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.option-key {\n  font-weight: bold;\n  margin-right: 8px;\n  min-width: 20px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n.el-button-group {\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.icon_size {\n  font-size: 14px;\n}\n\n/* 禁用题目行样式 */\n.disabled-question-row {\n  background-color: #f5f7fa !important;\n  color: #c0c4cc !important;\n}\n\n.disabled-question-row:hover {\n  background-color: #f5f7fa !important;\n}\n\n.disabled-question-row td {\n  color: #c0c4cc !important;\n}\n\n/* 题干内容左对齐样式 */\n.question-content-text {\n  max-height: 60px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: left !important;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n/* 强制题干列内容左对齐 */\n.el-table .question-content-column {\n  text-align: left !important;\n}\n\n.el-table .question-content-column .cell {\n  text-align: left !important;\n  padding-left: 10px !important;\n  padding-right: 10px !important;\n}\n\n/* 已选择题库标签样式 */\n.selected-bank-tag {\n  margin-right: 8px !important;\n  margin-bottom: 4px !important;\n  font-size: 13px !important;\n}\n\n/* 修复标签关闭按钮样式 */\n::v-deep .selected-bank-tag .el-tag__close {\n  color: #909399 !important;\n  font-size: 12px !important;\n  margin-left: 6px !important;\n  cursor: pointer !important;\n  border-radius: 50% !important;\n  width: 16px !important;\n  height: 16px !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  line-height: 1 !important;\n  vertical-align: middle !important;\n}\n\n::v-deep .selected-bank-tag .el-tag__close:hover {\n  background-color: #909399 !important;\n  color: #fff !important;\n}\n</style>\n\n<script>\nimport { listQuestionBank } from \"@/api/biz/questionBank\"\nimport { listCategory } from \"@/api/biz/category\"\nimport { getQuestionStatistics, listQuestion } from \"@/api/biz/question\"\n\nexport default {\n  name: \"PaperCreate\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    paperId: {\n      type: [String, Number],\n      default: null\n    }\n  },\n  data() {\n    return {\n      // 右侧面板状态\n      rightPanelCollapsed: false,\n      rightPanelWidth: 410,\n      \n      // 折叠面板激活项（accordion模式下为字符串）\n      activeCollapse: 'basic',\n      \n      // 试卷表单数据\n      paperForm: {\n        paperId: null,\n        paperName: '',\n        paperDesc: '',\n        paperType: 1, // 0: 固定试卷, 1: 随机试卷\n        coverImg: '',\n        totalScore: 100,\n        passScore: 60,\n        startTime: null,\n        endTime: null,\n        duration: 90, // 考试时长，分钟\n        lateLimit: 0, // 迟到限制，分钟\n        allowEarlySubmit: 0, // 是否允许提前交卷\n        earlySubmitTime: 40, // 提前交卷时间，分钟\n        showScore: 0, // 是否显示分值\n        showType: 0, // 是否显示题型\n        requireAllAnswered: 0, // 是否要求全部答完才能交卷\n        showResult: 0, // 是否显示成绩\n        showCorrect: 0, // 是否显示对错\n        showAnswer: 0, // 是否显示答案\n        showAnalysis: 0, // 是否显示解析\n        status: 0, // 状态：0未发布 1已发布\n        enableTimeLimit: 0, // 是否启用考试时间限制\n        durationSeconds: 0, // 时长秒数\n        createTime: null\n      },\n      \n      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）\n\n      // 固定试卷相关数据\n      fixedQuestions: [], // 固定试卷的题目列表\n      selectAll: false, // 全选状态\n      isExpanded: false, // 展开状态\n\n      // 手动选题对话框\n      showManualSelectDialog: false,\n      categoryOptions: [], // 分类选项数据\n      cascaderProps: {\n        value: 'id',\n        label: 'name',\n        children: 'children',\n        checkStrictly: false,\n        emitPath: false\n      },\n      manualSelect: {\n        selectedCategory: '', // 选择的题库目录\n        bankSearchKeyword: '', // 题库搜索关键词\n        questionBanks: [], // 题库列表\n        bankPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedBankId: null, // 选择的题库ID\n        questionType: '', // 题型筛选\n        difficulty: '', // 难度筛选\n        questionSearchKeyword: '', // 题目搜索关键词\n        questions: [], // 题目列表\n        questionPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedQuestions: [], // 选中的题目\n        selectedStats: {} // 选中题目的统计信息\n      },\n\n      // 固定试卷随机抽题对话框\n      showFixedRandomDialog: false,\n\n      // 固定试卷随机抽题相关数据\n      fixedRandomRules: [], // 临时规则列表，用于固定试卷随机抽题\n      fixedRandomForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n\n      // 随机规则对话框\n      showRuleDialog: false,\n      // 规则列表\n      rules: [],\n\n      // 添加规则对话框\n      showAddRuleDialog: false,\n      ruleForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n      // 当前操作状态\n      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则\n      editingRule: null, // 正在编辑的规则\n      parentRule: null, // 父规则（添加子规则时使用）\n      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量\n      questionBanks: [],\n      questionBankLoading: false,\n      categoryOptions: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n    }\n  },\n  computed: {\n    // 已选择的题库\n    selectedBanks() {\n      return this.questionBanks.filter(bank =>\n        this.ruleForm.selectedItems.includes(bank.bankId)\n      )\n    },\n\n    // 计算总题目数量\n    totalQuestions() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的题目数\n          rule.children.forEach(child => {\n            total += child.selectedCount || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的题目数\n          total += rule.selectedCount || 0\n        }\n      })\n      return total\n    },\n\n    // 计算总分数\n    totalRuleScore() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的总分\n          rule.children.forEach(child => {\n            total += child.totalScore || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的总分\n          total += rule.totalScore || 0\n        }\n      })\n      return total\n    },\n\n    // 获取父规则列表（只包含没有parentId的规则）\n    parentRules() {\n      return this.rules.filter(rule => !rule.parentId)\n    },\n\n    // 获取已使用的题库ID列表\n    usedBankIds() {\n      const usedIds = new Set()\n      this.rules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    },\n\n    // 计算已添加到试卷的题目统计\n    fixedQuestionStats() {\n      const stats = {}\n      this.fixedQuestions.forEach(question => {\n        const type = question.type\n        stats[type] = (stats[type] || 0) + 1\n      })\n      return stats\n    },\n\n    // 固定试卷随机抽题总题数\n    fixedRandomTotalQuestions() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.selectedCount || 0\n      })\n      return total\n    },\n\n    // 固定试卷随机抽题总分数\n    fixedRandomTotalScore() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.totalScore || 0\n      })\n      return total\n    },\n\n    // 获取固定试卷随机抽题父规则列表（只包含没有parentId的规则）\n    fixedRandomParentRules() {\n      return this.fixedRandomRules.filter(rule => !rule.parentId)\n    },\n\n    // 获取固定试卷随机抽题已使用的题库ID列表\n    usedFixedRandomBankIds() {\n      const usedIds = new Set()\n      this.fixedRandomRules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val && this.paperId) {\n        this.loadPaperData()\n      }\n    },\n\n    // 监听题型选择变化\n    'ruleForm.selectedQuestionTypes': {\n      async handler(newVal) {\n        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {\n          await this.updateQuestionTypeCount()\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 返回试卷列表 */\n    handleBack() {\n      this.$emit('close')\n    },\n    \n    /** 切换右侧面板 */\n    toggleRightPanel() {\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\n      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400\n    },\n    \n    /** 考试入口 */\n    handleExamEntry() {\n      this.$message.info('考试入口功能开发中...')\n    },\n    \n    /** 设置考试页面 */\n    handlePageSetting() {\n      this.$message.info('设置考试页面功能开发中...')\n    },\n    \n    /** 发布试卷 */\n    handlePublish() {\n      this.$message.info('发布试卷功能开发中...')\n    },\n    \n    /** 邀请考生 */\n    handleInviteStudents() {\n      this.$message.info('邀请考生功能开发中...')\n    },\n    \n    /** 已邀请列表 */\n    handleInviteList() {\n      this.$message.info('已邀请列表功能开发中...')\n    },\n    \n\n    \n    /** 添加规则（随机试卷） */\n    handleAddRule() {\n      this.showRuleDialog = true\n    },\n\n    /** 编辑规则（打开规则编辑界面） */\n    handleEditRules() {\n      this.showRuleDialog = true\n    },\n\n    /** 添加一级规则 */\n    handleAddFirstRule() {\n      this.currentOperation = 'add'\n      this.editingRule = null\n\n      // 如果已有规则，限制只能选择相同类型\n      if (this.rules.length > 0) {\n        this.ruleForm.ruleType = this.rules[0].type\n      } else {\n        this.ruleForm.ruleType = 3 // 默认题库\n      }\n\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 加载题库列表 */\n    loadQuestionBanks() {\n      this.questionBankLoading = true\n      // 同时加载题库和分类数据\n      Promise.all([\n        listQuestionBank(this.queryParams),\n        listCategory({ pageSize: 1000 })\n      ]).then(([bankResponse, categoryResponse]) => {\n        const questionBanks = bankResponse.rows || []\n        this.total = bankResponse.total || 0\n        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []\n\n        // 为每个题库获取题目统计\n        const statisticsPromises = questionBanks.map(bank =>\n          getQuestionStatistics(bank.bankId).then(stats => {\n            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0\n            return bank\n          }).catch(() => {\n            bank.questionCount = 0\n            return bank\n          })\n        )\n\n        Promise.all(statisticsPromises).then(banksWithStats => {\n          this.questionBanks = banksWithStats\n          this.questionBankLoading = false\n        })\n      }).catch(() => {\n        this.questionBankLoading = false\n      })\n    },\n\n    /** 保存规则 */\n    handleSaveRules() {\n      this.$message.info('保存规则功能开发中...')\n      this.showRuleDialog = false\n    },\n\n    /** 保存单个规则 */\n    async handleSaveRule() {\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {\n        this.$message.warning('请至少选择一个题库')\n        return\n      }\n      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {\n        this.$message.warning('请至少选择一个题型')\n        return\n      }\n\n      // 题型规则验证：检查是否有可用题目\n      if (this.ruleForm.ruleType === 1) {\n        if (this.currentQuestionTypeCount === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')\n          return\n        }\n      }\n\n      if (this.currentOperation === 'edit') {\n        // 编辑现有规则\n        this.updateExistingRule()\n      } else if (this.currentOperation === 'addFixedRandom') {\n        // 添加固定试卷随机抽题规则\n        this.addFixedRandomRule()\n      } else if (this.currentOperation === 'addFixedRandomSub') {\n        // 添加固定试卷随机抽题子规则\n        this.addFixedRandomSubRule()\n      } else {\n        // 添加新规则（包括添加子规则）\n        this.addNewRule()\n      }\n\n      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')\n      this.showAddRuleDialog = false\n\n      // 重置表单\n      this.resetRuleForm()\n    },\n\n    /** 更新现有规则 */\n    updateExistingRule() {\n      const rule = this.editingRule\n      rule.type = this.ruleForm.ruleType\n      rule.generateType = this.ruleForm.generateType\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n\n        // 重新计算总分，确保选取数量不超过最大题目数\n        if (rule.selectedCount > rule.maxQuestions) {\n          rule.selectedCount = rule.maxQuestions\n        }\n        rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n    },\n\n    /** 添加新规则 */\n    addNewRule() {\n      // 如果是添加子规则，按原逻辑处理\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        this.addSingleRule()\n        return\n      }\n\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index, // 确保每个规则有唯一ID\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 0.5,\n            totalScore: 0.5,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateRuleScore(rule)\n          this.rules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleRule()\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    addFixedRandomRule() {\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index,\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 1,\n            totalScore: 1,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateFixedRandomRuleScore(rule)\n          this.fixedRandomRules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleFixedRandomRule()\n      }\n    },\n\n    /** 添加单个固定试卷随机抽题规则 */\n    addSingleFixedRandomRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      }\n\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    addFixedRandomSubRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        parentId: this.parentRule.id,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      } else if (this.ruleForm.ruleType === 3) {\n        // 题库规则（虽然在固定试卷随机抽题中子规则主要是题型，但保持兼容性）\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      }\n\n      // 确保父规则有children数组\n      if (!this.parentRule.children) {\n        this.$set(this.parentRule, 'children', [])\n      }\n\n      // 添加到父规则的children中\n      this.parentRule.children.push(rule)\n\n      // 同时添加到主规则列表中（用于统计和抽题）\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加单个规则 */\n    addSingleRule() {\n      const rule = {\n        id: Date.now(), // 临时ID\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1, // 默认选取1题\n        scorePerQuestion: 0.5, // 默认每题0.5分\n        totalScore: 0.5, // 默认总分0.5分\n        maxQuestions: 0 // 最大题目数\n      }\n\n      // 如果是添加子规则\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        rule.parentId = this.parentRule.id\n\n        // 确保父规则有children数组\n        if (!this.parentRule.children) {\n          this.$set(this.parentRule, 'children', [])\n        }\n\n        // 添加到父规则的children中\n        this.parentRule.children.push(rule)\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n\n      // 只有父规则才添加到主规则列表\n      if (this.currentOperation !== 'addSub') {\n        this.rules.push(rule)\n      }\n\n      // 更新规则分数\n      this.updateRuleScore(rule)\n    },\n\n    /** 重置规则表单 */\n    resetRuleForm() {\n      this.ruleForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n      // 只有不是固定试卷随机抽题时才重置操作状态\n      if (this.currentOperation !== 'addFixedRandom' && this.currentOperation !== 'addFixedRandomSub') {\n        this.currentOperation = 'add'\n        this.editingRule = null\n        this.parentRule = null\n      }\n      this.currentQuestionTypeCount = 0\n    },\n\n    /** 搜索题库 */\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = this.searchKeyword || undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 重置搜索 */\n    handleReset() {\n      this.searchKeyword = ''\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 表格选择变化 */\n    handleSelectionChange(selection) {\n      this.ruleForm.selectedItems = selection.map(item => item.bankId)\n    },\n\n    /** 移除已选择的题库 */\n    removeSelectedBank(bankId) {\n      const index = this.ruleForm.selectedItems.indexOf(bankId)\n      if (index > -1) {\n        this.ruleForm.selectedItems.splice(index, 1)\n      }\n\n      // 同步取消表格中的勾选状态\n      this.$nextTick(() => {\n        const table = this.$refs.questionBankTable\n        if (table) {\n          // 找到对应的行数据\n          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)\n          if (rowToDeselect) {\n            table.toggleRowSelection(rowToDeselect, false)\n          }\n        }\n      })\n    },\n\n    /** 分页变化 */\n    handleCurrentChange(page) {\n      this.queryParams.pageNum = page\n      this.loadQuestionBanks()\n    },\n\n    /** 根据分类ID获取分类名称 */\n    getCategoryName(categoryId) {\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\n      return category ? category.name : '未分类'\n    },\n\n    /** 在分类数据中查找分类（支持扁平和树形结构） */\n    findCategoryById(categories, id) {\n      // 创建扁平化的分类列表\n      const flatCategories = this.flattenCategories(categories)\n\n      // 在扁平化列表中查找\n      const category = flatCategories.find(cat => cat.id === id)\n      return category || null\n    },\n\n    /** 将树形分类数据扁平化 */\n    flattenCategories(categories) {\n      let result = []\n\n      function flatten(cats) {\n        for (const cat of cats) {\n          result.push(cat)\n          if (cat.children && cat.children.length > 0) {\n            flatten(cat.children)\n          }\n        }\n      }\n\n      flatten(categories)\n      return result\n    },\n\n    /** 更新规则分数 */\n    updateRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 获取题型文本 */\n    getQuestionTypeText(questionTypes) {\n      const questionTypeMap = {\n        '1': '单选题', '2': '多选题', '3': '判断题',\n        1: '单选题', 2: '多选题', 3: '判断题'\n      }\n\n      // 如果是数组，处理多个题型\n      if (Array.isArray(questionTypes)) {\n        return questionTypes.map(t => questionTypeMap[t]).join('、')\n      }\n\n      // 如果是单个值，直接返回对应文本\n      return questionTypeMap[questionTypes] || '未知'\n    },\n\n\n\n    /** 获取子规则标签 */\n    getChildRuleLabel(childRule) {\n      if (childRule.type === 3) {\n        return '题库：'\n      } else if (childRule.type === 1) {\n        // 题型子规则只显示具体的题型名称，不显示\"题型：\"前缀\n        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return questionTypeMap[childRule.selectedQuestionTypes[0]]\n        } else {\n          return '题型'\n        }\n      }\n      return '规则：'\n    },\n\n    /** 获取规则类型标签（用于random-paper显示） */\n    getRuleTypeLabel(rule) {\n      if (rule.type === 3) {\n        return '题库：'\n      } else if (rule.type === 1) {\n        return '题型：'\n      }\n      return '规则：'\n    },\n\n    /** 获取规则内容（用于random-paper显示） */\n    getRuleContent(rule) {\n      if (rule.type === 3) {\n        // 题库规则显示题库名称\n        if (rule.selectedBanks && rule.selectedBanks.length > 0) {\n          return rule.selectedBanks.map(bank => bank.bankName).join('、')\n        }\n        return '未选择题库'\n      } else if (rule.type === 1) {\n        // 题型规则显示题型名称\n        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')\n        }\n        return '未选择题型'\n      }\n      return '未配置'\n    },\n\n    /** 添加子规则 */\n    addSubRule(rule) {\n      this.currentOperation = 'addSub'\n      this.parentRule = rule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n\n      if (rule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 编辑规则 */\n    editRule(rule) {\n      this.currentOperation = 'edit'\n      this.editingRule = rule\n      this.ruleForm.ruleType = rule.type\n      this.ruleForm.generateType = rule.generateType\n\n      if (rule.type === 3) {\n        // 题库规则\n        this.ruleForm.selectedItems = [...rule.selectedItems]\n      } else if (rule.type === 1) {\n        // 题型规则\n        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]\n        // 编辑题型规则时，更新题目数量\n        this.$nextTick(() => {\n          this.updateQuestionTypeCount()\n        })\n      }\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 删除规则 */\n    deleteRule(rule) {\n      this.$confirm('确定要删除这条规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = this.rules.findIndex(r => r.id === rule.id)\n        if (index > -1) {\n          this.rules.splice(index, 1)\n          this.$message.success('规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 删除子规则 */\n    deleteChildRule(parentRule, childRule) {\n      this.$confirm('确定要删除这条子规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = parentRule.children.findIndex(child => child.id === childRule.id)\n        if (index > -1) {\n          parentRule.children.splice(index, 1)\n          this.$message.success('子规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 获取对话框标题 */\n    getDialogTitle() {\n      switch (this.currentOperation) {\n        case 'add':\n          return '添加规则'\n        case 'edit':\n          return '编辑规则'\n        case 'addSub':\n          return '添加子规则'\n        case 'addFixedRandom':\n          return '添加抽题规则'\n        case 'addFixedRandomSub':\n          return '添加题型规则'\n        default:\n          return '添加规则'\n      }\n    },\n\n    /** 是否显示规则类型选择 */\n    shouldShowRuleTypeSelection() {\n      // 固定试卷随机抽题的一级规则只能选择题库，但仍显示选择界面\n      if (this.currentOperation === 'addFixedRandom') {\n        return true\n      }\n      // 固定试卷随机抽题的子规则可以选择规则类型\n      if (this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n      // 其他操作都显示规则类型选择\n      return true\n    },\n\n    /** 是否显示规则类型选项 */\n    shouldShowRuleType(ruleType) {\n      // 编辑时只显示当前规则的类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type === ruleType\n      }\n\n      // 添加子规则时的逻辑\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        // 题型规则的子规则只能是题库规则\n        if (this.parentRule.type === 1) {\n          return ruleType === 3\n        }\n        // 题库规则的子规则只能是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 添加一级规则时的逻辑\n      if (this.currentOperation === 'add') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题一级规则时的逻辑\n      if (this.currentOperation === 'addFixedRandom') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题子规则时的逻辑\n      if (this.currentOperation === 'addFixedRandomSub' && this.parentRule) {\n        // 题库规则的子规则可以是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 其他情况显示所有类型\n      return true\n    },\n\n    /** 是否显示特定题型选项 */\n    shouldShowQuestionType(questionType) {\n      // 如果不是添加子规则，显示所有题型\n      if (this.currentOperation !== 'addSub' && this.currentOperation !== 'addFixedRandomSub') {\n        return true\n      }\n\n      if (!this.parentRule) {\n        return true\n      }\n\n      // 获取父规则下已有的题型子规则中选择的题型\n      const existingQuestionTypes = []\n      if (this.parentRule.children) {\n        this.parentRule.children.forEach(child => {\n          if (child.type === 1 && child.selectedQuestionTypes) {\n            existingQuestionTypes.push(...child.selectedQuestionTypes)\n          }\n        })\n      }\n\n      // 如果该题型已经被选择过，则隐藏\n      return !existingQuestionTypes.includes(questionType)\n    },\n\n    /** 判断表格行是否可选择 */\n    isRowSelectable(row) {\n      // 如果是编辑操作，允许选择\n      if (this.currentOperation === 'edit') {\n        return true\n      }\n\n      // 如果是添加子规则，允许选择（子规则不涉及题库选择）\n      if (this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n\n      // 如果是固定试卷随机抽题，检查该题库是否已经被使用\n      if (this.currentOperation === 'addFixedRandom') {\n        return !this.usedFixedRandomBankIds.includes(row.bankId)\n      }\n\n      // 如果是添加一级规则，检查题库是否已被使用\n      return !this.usedBankIds.includes(row.bankId)\n    },\n\n    /** 获取表格行的样式类名 */\n    getRowClassName({ row }) {\n      // 如果题库已被使用且不是编辑操作，添加禁用样式\n      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {\n        return 'disabled-row'\n      }\n      return ''\n    },\n\n    /** 更新题型题目数量 */\n    async updateQuestionTypeCount() {\n      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {\n        this.currentQuestionTypeCount = 0\n        return\n      }\n\n      // 获取题库信息\n      let selectedBanks = []\n      if ((this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') && this.parentRule && this.parentRule.type === 3) {\n        // 添加子规则时，使用父规则的题库\n        selectedBanks = this.parentRule.selectedBanks || []\n      } else {\n        // 其他情况使用当前选择的题库\n        selectedBanks = this.selectedBanks\n      }\n\n      if (!selectedBanks.length) {\n        this.currentQuestionTypeCount = 0\n        this.$message.warning('请先选择题库')\n        return\n      }\n\n      try {\n        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)\n        this.currentQuestionTypeCount = count\n\n        if (count === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目')\n        }\n      } catch (error) {\n        console.error('查询题目数量失败:', error)\n        this.currentQuestionTypeCount = 0\n        this.$message.error('查询题目数量失败')\n      }\n    },\n\n    /** 根据题库和题型查询题目数量 */\n    async getQuestionCountByType(banks, questionTypes) {\n      if (!banks.length || !questionTypes.length) {\n        return 0\n      }\n\n      let totalCount = 0\n\n      // 遍历每个题库，查询题目统计\n      for (const bank of banks) {\n        try {\n          // 使用已导入的API方法\n          const response = await getQuestionStatistics(bank.bankId)\n          console.log(`题库${bank.bankId}统计数据:`, response)\n\n          if (response.code === 200 && response.data) {\n            const statistics = response.data\n\n            // 根据选择的题型累加数量\n            questionTypes.forEach(type => {\n              switch (type) {\n                case '1': // 单选题\n                  totalCount += statistics.singleChoice || 0\n                  break\n                case '2': // 多选题\n                  totalCount += statistics.multipleChoice || 0\n                  break\n                case '3': // 判断题\n                  totalCount += statistics.judgment || 0\n                  break\n              }\n            })\n          }\n        } catch (error) {\n          console.error(`查询题库${bank.bankId}统计信息失败:`, error)\n        }\n      }\n\n      console.log(`总题目数量: ${totalCount}`)\n      return totalCount\n    },\n\n    /** 是否禁用规则类型 */\n    shouldDisableRuleType(ruleType) {\n      // 编辑时不能更改类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type !== ruleType\n      }\n\n      // 添加一级规则时，只能选择题库，禁用其他类型\n      if (this.currentOperation === 'add') {\n        return ruleType !== 3\n      }\n\n      return false\n    },\n    \n    /** 全选/取消全选 */\n    handleSelectAll() {\n      this.fixedQuestions.forEach(question => {\n        question.selected = this.selectAll\n      })\n    },\n\n    /** 删除选中题目 */\n    handleDeleteSelected() {\n      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)\n      if (selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)\n        this.selectAll = false\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 手动选题 */\n    handleManualSelect() {\n      this.showManualSelectDialog = true\n      this.initManualSelectData()\n    },\n\n    /** 初始化手动选题数据 */\n    initManualSelectData() {\n      // 重置数据\n      this.manualSelect.selectedCategory = ''\n      this.manualSelect.bankSearchKeyword = ''\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.manualSelect.selectedQuestions = []\n      this.manualSelect.selectedStats = {}\n\n      // 加载分类数据\n      this.loadCategoryTree()\n\n      // 加载题库列表\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 加载分类树数据 */\n    loadCategoryTree() {\n      listCategory({ pageSize: 1000 }).then(response => {\n        const categories = response.rows || []\n        this.categoryOptions = this.buildCategoryTree(categories)\n      }).catch(error => {\n        console.error('加载分类数据失败:', error)\n        this.categoryOptions = []\n      })\n    },\n\n    /** 构建分类树 */\n    buildCategoryTree(categories) {\n      const map = {}\n\n      // 先将所有分类放入map中\n      categories.forEach(category => {\n        map[category.id] = { ...category, children: [] }\n      })\n\n      // 构建完整的树形结构\n      const result = []\n      categories.forEach(category => {\n        if (category.parentId === 0) {\n          // 顶级分类\n          result.push(map[category.id])\n        } else {\n          // 子分类\n          if (map[category.parentId]) {\n            map[category.parentId].children.push(map[category.id])\n          }\n        }\n      })\n\n      // 清理空的children数组\n      const cleanEmptyChildren = (node) => {\n        if (node.children && node.children.length === 0) {\n          delete node.children\n        } else if (node.children && node.children.length > 0) {\n          node.children.forEach(child => cleanEmptyChildren(child))\n        }\n      }\n\n      // 清理所有节点的空children\n      result.forEach(node => cleanEmptyChildren(node))\n\n      return result\n    },\n\n    /** 获取所有子分类ID */\n    getAllChildCategoryIds(categoryId, categories) {\n      const result = [categoryId]\n\n      const findChildren = (parentId) => {\n        categories.forEach(category => {\n          if (category.parentId === parentId) {\n            result.push(category.id)\n            findChildren(category.id)\n          }\n        })\n      }\n\n      findChildren(categoryId)\n      return result\n    },\n\n    /** 加载手动选题的题库列表 */\n    loadManualSelectQuestionBanks() {\n      let categoryIds = []\n\n      // 如果选择了分类，获取该分类及其所有子分类的ID\n      if (this.manualSelect.selectedCategory) {\n        // 获取原始分类数据（包含所有层级）\n        listCategory({ pageSize: 1000 }).then(response => {\n          const allCategories = response.rows || []\n          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)\n\n          // 执行多次查询，因为后端不支持IN查询\n          this.loadQuestionBanksByCategories(categoryIds)\n        }).catch(error => {\n          console.error('加载分类数据失败:', error)\n          this.loadQuestionBanksByCategories([])\n        })\n      } else {\n        // 没有选择分类，加载所有题库\n        this.loadQuestionBanksByCategories([])\n      }\n    },\n\n    /** 根据分类ID列表加载题库 */\n    loadQuestionBanksByCategories(categoryIds) {\n      const queryParams = {\n        pageNum: this.manualSelect.bankPagination.pageNum,\n        pageSize: this.manualSelect.bankPagination.pageSize,\n        bankName: this.manualSelect.bankSearchKeyword || undefined\n      }\n\n      if (categoryIds.length > 0) {\n        // 如果有分类筛选，需要合并多个分类的结果\n        const promises = categoryIds.map(categoryId => {\n          return listQuestionBank({ ...queryParams, categoryId })\n        })\n\n        Promise.all(promises).then(responses => {\n          const allBanks = []\n          let totalCount = 0\n\n          responses.forEach(response => {\n            if (response.rows) {\n              allBanks.push(...response.rows)\n              totalCount += response.total || 0\n            }\n          })\n\n          // 去重（根据bankId）\n          const uniqueBanks = allBanks.filter((bank, index, self) =>\n            index === self.findIndex(b => b.bankId === bank.bankId)\n          )\n\n          this.manualSelect.questionBanks = uniqueBanks\n          this.manualSelect.bankPagination.total = uniqueBanks.length\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      } else {\n        // 没有分类筛选，直接查询\n        listQuestionBank(queryParams).then(response => {\n          this.manualSelect.questionBanks = response.rows || []\n          this.manualSelect.bankPagination.total = response.total || 0\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      }\n    },\n\n    /** 搜索题库 */\n    searchQuestionBanks() {\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 选择题库 */\n    selectQuestionBank(row) {\n      this.manualSelect.selectedBankId = row.bankId\n      this.loadQuestions()\n    },\n\n    /** 加载题目列表 */\n    loadQuestions() {\n      if (!this.manualSelect.selectedBankId) {\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n        return\n      }\n\n      const queryParams = {\n        pageNum: this.manualSelect.questionPagination.pageNum,\n        pageSize: this.manualSelect.questionPagination.pageSize,\n        bankId: this.manualSelect.selectedBankId,\n        questionType: this.manualSelect.questionType || undefined,\n        difficulty: this.manualSelect.difficulty || undefined,\n        questionContent: this.manualSelect.questionSearchKeyword || undefined\n      }\n\n      listQuestion(queryParams).then(response => {\n        this.manualSelect.questions = response.rows || []\n        this.manualSelect.questionPagination.total = response.total || 0\n      }).catch(error => {\n        console.error('加载题目列表失败:', error)\n        this.$message.error('加载题目列表失败')\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n      })\n    },\n\n    /** 搜索题目 */\n    searchQuestions() {\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 重置题目搜索 */\n    resetQuestionSearch() {\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.searchQuestions()\n    },\n\n    /** 随机抽题 */\n    handleRandomSelect() {\n      // 为固定试卷打开随机规则设置对话框\n      this.showFixedRandomDialog = true\n      this.resetFixedRandomForm()\n      this.loadQuestionBanks()\n    },\n\n    /** 重置固定试卷随机抽题表单 */\n    resetFixedRandomForm() {\n      this.fixedRandomRules = []\n      this.fixedRandomForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    handleAddFixedRandomRule() {\n      this.currentOperation = 'addFixedRandom'\n      this.editingRule = null\n      this.ruleForm.ruleType = 3 // 固定为题库类型\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    handleAddFixedRandomSubRule(parentRule) {\n      this.currentOperation = 'addFixedRandomSub'\n      this.editingRule = null\n      this.parentRule = parentRule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n      if (parentRule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 更新固定试卷随机抽题规则分数 */\n    updateFixedRandomRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 删除固定试卷随机抽题规则 */\n    deleteFixedRandomRule(rule) {\n      // 如果是父规则，需要同时删除所有子规则\n      if (rule.children && rule.children.length > 0) {\n        // 删除所有子规则\n        rule.children.forEach(childRule => {\n          const childIndex = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n          if (childIndex > -1) {\n            this.fixedRandomRules.splice(childIndex, 1)\n          }\n        })\n      }\n\n      // 删除父规则\n      const index = this.fixedRandomRules.findIndex(r => r.id === rule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 删除固定试卷随机抽题子规则 */\n    deleteFixedRandomChildRule(parentRule, childRule) {\n      // 从父规则的children中删除\n      if (parentRule.children) {\n        const childIndex = parentRule.children.findIndex(r => r.id === childRule.id)\n        if (childIndex > -1) {\n          parentRule.children.splice(childIndex, 1)\n        }\n      }\n\n      // 从主规则列表中删除\n      const index = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 确认固定试卷随机抽题 */\n    async handleConfirmFixedRandomSelect() {\n      if (this.fixedRandomRules.length === 0) {\n        this.$message.warning('请先添加抽题规则')\n        return\n      }\n\n      try {\n        // 根据规则随机抽取题目\n        const selectedQuestions = await this.extractQuestionsFromRules(this.fixedRandomRules)\n\n        if (selectedQuestions.length === 0) {\n          this.$message.warning('根据当前规则未能抽取到题目')\n          return\n        }\n\n        // 将抽取的题目添加到固定试卷中\n        selectedQuestions.forEach((question, index) => {\n          this.fixedQuestions.push({\n            id: Date.now() + index,\n            questionId: question.questionId,\n            content: question.questionContent,\n            type: question.questionType,\n            difficulty: question.difficulty,\n            options: question.options,\n            score: question.score || 1, // 使用规则中设置的分数\n            selected: false,\n            expanded: false\n          })\n        })\n\n        this.showFixedRandomDialog = false\n        this.$message.success(`成功随机抽取并添加 ${selectedQuestions.length} 道题目`)\n      } catch (error) {\n        console.error('随机抽题失败:', error)\n        this.$message.error('随机抽题失败，请重试')\n      }\n    },\n\n    /** 根据规则抽取题目 */\n    async extractQuestionsFromRules(rules) {\n      const allQuestions = []\n\n      // 处理父规则（题库规则）\n      const parentRules = rules.filter(rule => !rule.parentId)\n\n      for (const parentRule of parentRules) {\n        try {\n          // 获取子规则\n          const childRules = rules.filter(rule => rule.parentId === parentRule.id)\n\n          if (childRules.length > 0) {\n            // 有子规则：按子规则的题型从父规则的题库中抽取\n            for (const childRule of childRules) {\n              let questions = []\n\n              // 从父规则的题库中按子规则的题型抽取题目\n              for (const bank of parentRule.selectedBanks) {\n                for (const questionType of childRule.selectedQuestionTypes) {\n                  const queryParams = {\n                    bankId: bank.bankId,\n                    questionType: questionType,\n                    pageNum: 1,\n                    pageSize: 1000\n                  }\n\n                  const response = await listQuestion(queryParams)\n                  if (response.rows && response.rows.length > 0) {\n                    questions = questions.concat(response.rows)\n                  }\n                }\n              }\n\n              // 随机选择指定数量的题目\n              if (questions.length > 0) {\n                const shuffled = this.shuffleArray([...questions])\n                const selectedCount = Math.min(childRule.selectedCount, shuffled.length)\n                const selectedQuestions = shuffled.slice(0, selectedCount)\n\n                // 为每个题目设置分数\n                selectedQuestions.forEach(question => {\n                  question.score = childRule.scorePerQuestion\n                })\n\n                allQuestions.push(...selectedQuestions)\n              }\n            }\n          } else {\n            // 没有子规则：直接从父规则的题库中抽取题目\n            let questions = []\n\n            for (const bank of parentRule.selectedBanks) {\n              const queryParams = {\n                bankId: bank.bankId,\n                pageNum: 1,\n                pageSize: 1000\n              }\n\n              const response = await listQuestion(queryParams)\n              if (response.rows && response.rows.length > 0) {\n                questions = questions.concat(response.rows)\n              }\n            }\n\n            // 随机选择指定数量的题目\n            if (questions.length > 0) {\n              const shuffled = this.shuffleArray([...questions])\n              const selectedCount = Math.min(parentRule.selectedCount, shuffled.length)\n              const selectedQuestions = shuffled.slice(0, selectedCount)\n\n              // 为每个题目设置分数\n              selectedQuestions.forEach(question => {\n                question.score = parentRule.scorePerQuestion\n              })\n\n              allQuestions.push(...selectedQuestions)\n            }\n          }\n        } catch (error) {\n          console.error('抽取题目失败:', error)\n        }\n      }\n\n      return allQuestions\n    },\n\n    /** 数组随机排序 */\n    shuffleArray(array) {\n      for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]]\n      }\n      return array\n    },\n\n    /** 排序 */\n    handleSort() {\n      this.$message.info('排序功能开发中...')\n    },\n\n    /** 批量设置分数 */\n    handleBatchSetScore() {\n      this.$message.info('批量设置分数功能开发中...')\n    },\n\n    /** 导出 */\n    handleExport() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    /** 展开/收起 */\n    handleToggleExpand() {\n      this.isExpanded = !this.isExpanded\n      // 批量设置所有题目的展开状态\n      this.fixedQuestions.forEach(question => {\n        question.expanded = this.isExpanded\n      })\n      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)\n    },\n\n    /** 题目选择变化 */\n    handleQuestionSelectionChange(selection) {\n      this.manualSelect.selectedQuestions = selection\n      this.updateSelectedStats()\n    },\n\n    /** 更新选中题目统计 */\n    updateSelectedStats() {\n      const stats = {}\n      this.manualSelect.selectedQuestions.forEach(question => {\n        const type = question.questionType\n        stats[type] = (stats[type] || 0) + 1\n      })\n      this.manualSelect.selectedStats = stats\n    },\n\n    /** 确认手动选题 */\n    confirmManualSelect() {\n      if (this.manualSelect.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择题目')\n        return\n      }\n\n      // 将选中的题目添加到固定试卷中\n      this.manualSelect.selectedQuestions.forEach((question, index) => {\n        this.fixedQuestions.push({\n          id: Date.now() + index,\n          questionId: question.questionId,\n          content: question.questionContent,\n          type: question.questionType,\n          difficulty: question.difficulty,\n          options: question.options, // 保存选项信息\n          score: 1, // 默认1分\n          selected: false,\n          expanded: false // 默认收起状态\n        })\n      })\n\n      this.showManualSelectDialog = false\n      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)\n    },\n\n    /** 题库分页大小变化 */\n    handleBankSizeChange(val) {\n      this.manualSelect.bankPagination.pageSize = val\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题库当前页变化 */\n    handleBankCurrentChange(val) {\n      this.manualSelect.bankPagination.pageNum = val\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题目分页大小变化 */\n    handleQuestionSizeChange(val) {\n      this.manualSelect.questionPagination.pageSize = val\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 题目当前页变化 */\n    handleQuestionCurrentChange(val) {\n      this.manualSelect.questionPagination.pageNum = val\n      this.loadQuestions()\n    },\n\n    /** 获取难度文本 */\n    getDifficultyText(difficulty) {\n      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }\n      return difficultyMap[difficulty] || '未知'\n    },\n\n    /** 更新题目分数 */\n    updateQuestionScore(question) {\n      // 分数更新后可以触发总分重新计算\n      this.$forceUpdate()\n    },\n\n    /** 切换题目展开/收起状态 */\n    toggleQuestionExpand(question) {\n      question.expanded = !question.expanded\n    },\n\n    /** 删除单个题目 */\n    deleteQuestion(question, index) {\n      this.$confirm('确定删除这道题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions.splice(index, 1)\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 解析选项JSON字符串 */\n    parseOptions(optionsStr) {\n      try {\n        if (typeof optionsStr === 'string') {\n          return JSON.parse(optionsStr)\n        }\n        return optionsStr || []\n      } catch (error) {\n        console.error('解析选项失败:', error)\n        return []\n      }\n    },\n\n    /** 判断题目是否可选择 */\n    isQuestionSelectable(row) {\n      // 检查题目是否已经添加到试卷中\n      return !this.fixedQuestions.some(question => question.questionId === row.questionId)\n    },\n\n    /** 获取题目行的样式类名 */\n    getQuestionRowClassName({ row }) {\n      // 如果题目已被添加，添加禁用样式\n      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {\n        return 'disabled-question-row'\n      }\n      return ''\n    },\n    \n    /** 上传前验证 */\n    beforeUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0\n      const isLt2M = file.size / 1024 / 1024 < 2\n      \n      if (!isImage) {\n        this.$message.error('只能上传图片文件!')\n        return false\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!')\n        return false\n      }\n      return true\n    },\n    \n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    \n    /** 加载试卷数据 */\n    loadPaperData() {\n      if (this.paperId) {\n        // TODO: 调用API加载试卷数据\n        console.log('加载试卷数据:', this.paperId)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.exam-editor {\n  display: flex;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.exam-editor-left {\n  flex: 1;\n  padding: 20px;\n  padding-right: 420px; /* 为右侧固定面板留出空间 */\n  overflow-y: auto;\n  transition: padding-right 0.3s ease;\n  background-color: #FFF;\n}\n\n.exam-editor-left.collapsed {\n  padding-right: 70px; /* 右侧面板收起时的空间 */\n}\n\n.exam-editor-right {\n  background: #fff;\n  border-left: 1px solid #e4e7ed;\n  position: fixed;\n  right: 0;\n  top: 0;\n  height: 100vh;\n  transition: width 0.3s ease;\n  z-index: 100;\n}\n\n.collapse-button {\n  position: absolute;\n  left: -15px;\n  top: 20px;\n  width: 30px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e4e7ed;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  font-size: 16px;\n  color: #606266;\n}\n\n.collapse-button:hover {\n  background: #f5f7fa;\n  color: #409eff;\n}\n\n.editPaper_main_top {\n  background: #fff;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.editPaper_main_top .el-button-group {\n  margin-right: 15px;\n  display: inline-block;\n}\n\n.clear_both {\n  clear: both;\n}\n\n.subject_main-wrapper {\n  max-width: 100%;\n}\n\n/* 左侧卡片模块悬停效果 */\n.subject_main-wrapper .el-card {\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.subject_main-wrapper .el-card:hover {\n  border: 2px dashed #409eff;\n  background-color: #fafbff;\n}\n\n.subtitle-title {\n  position: relative;\n}\n\n.slgfont {\n  font-size: 24px;\n  color: #303133;\n}\n\n.paper-count {\n  margin-top: 10px;\n}\n\n.paper-count .el-tag {\n  margin-left: 8px;\n}\n\n\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.tac {\n  text-align: center;\n}\n\n.pd5 {\n  padding: 5px;\n}\n\n.editor-header {\n  padding: 20px;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.editor-header--big {\n  font-size: 20px;\n}\n\n.close-button {\n  font-size: 20px;\n  color: #909399;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  color: #f56c6c;\n  background-color: #fef0f0;\n}\n\n.main {\n  height: calc(100vh - 80px);\n  overflow-y: auto;\n  padding: 0;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.main-collapse {\n  border: none;\n}\n\n.main-collapse .el-collapse-item__header {\n  padding: 0 20px;\n  height: 60px;\n  line-height: 60px;\n  background: #f8f8f8;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* 使用深度选择器覆盖Element UI默认样式 */\n.main-collapse >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n.editor-collapse-item >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n/* Vue 3 深度选择器语法 */\n.main-collapse :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.editor-collapse-item :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.main-module {\n  padding: 20px;\n}\n\n.setting-block {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.setting-block:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n\n/* 二级设置块容器 */\n.block-expand {\n  margin-top: 10px;\n  padding-left: 20px;\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 二级设置块样式 */\n.sub-setting {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #e8e8e8;\n}\n\n.sub-setting:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n}\n\n.line {\n  display: flex;\n  align-items: flex-start;\n}\n\n.block-header {\n  justify-content: space-between;\n}\n\n.block-header b {\n  font-size: 14px;\n  color: #303133;\n  min-width: 93px;\n  line-height: 32px;\n}\n\n.input-area {\n  flex: 1;\n  margin-left: 20px;\n}\n\n.input--number {\n  width: 120px;\n}\n\n.dpib {\n  display: inline-block;\n}\n\n.avatar-uploader {\n  border: none !important;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 120px;\n  height: 80px;\n  box-sizing: border-box;\n}\n\n\n\n/* 使用伪元素创建统一的虚线边框 */\n.avatar-uploader::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.avatar-uploader:hover::before {\n  border-color: #409eff;\n}\n\n/* 确保Element UI组件没有边框 */\n.avatar-uploader .el-upload {\n  border: none !important;\n  width: 100%;\n  height: 100%;\n  background: transparent !important;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 120px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n}\n\n.avatar {\n  width: 120px;\n  height: 80px;\n  display: block;\n  object-fit: cover;\n}\n\n.image_area {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bold {\n  font-weight: bold;\n}\n\n.mr10 {\n  margin-right: 10px;\n}\n\n/* 折叠面板标题样式 */\n.collapse-title {\n  margin-left: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.collapse-title i {\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.el-popover__reference {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n}\n\n.el-popover__title {\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n/* 设置标题容器样式 */\n.setting-title {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n}\n\n.setting-title b {\n  font-size: 14px;\n  color: #303133;\n}\n\n/* 出题方式问号图标样式 */\n.paper-type-question {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n  position: relative;\n  z-index: 10;\n  font-size: 14px;\n  line-height: 1;\n}\n\n.paper-type-question:hover {\n  color: #409eff;\n}\n\n/* 出题方式提示框样式 */\n.paper-type-tooltip {\n  max-width: 320px !important;\n  z-index: 2000 !important;\n}\n\n/* 迟到限制提示框样式 */\n.late-limit-tooltip {\n  max-width: 280px !important;\n  z-index: 2000 !important;\n}\n\n/* 随机规则设置对话框样式 */\n.cascade-random-rules {\n  min-height: 400px;\n}\n\n/* 确保对话框层级正确 */\n::v-deep .el-dialog__wrapper {\n  z-index: 3000 !important;\n}\n\n::v-deep .el-dialog {\n  z-index: 3001 !important;\n  position: relative !important;\n}\n\n::v-deep .el-overlay {\n  z-index: 2999 !important;\n}\n\n.topbar {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.topbar .fl {\n  float: left;\n}\n\n.topbar .summary {\n  margin-left: 15px;\n  color: #666;\n  font-size: 14px;\n}\n\n.topbar .total_score {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.guide-steps-list {\n  margin: 30px 0;\n  padding: 0 20px;\n}\n\n/* 步骤组件样式优化 */\n::v-deep .guide-steps-list .el-step {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__head {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__main {\n  text-align: center;\n  margin-top: 10px;\n}\n\n::v-deep .guide-steps-list .el-step__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n::v-deep .guide-steps-list .el-step__description {\n  margin-top: 8px;\n  padding: 0 10px;\n}\n\n.step-content {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 5px;\n}\n\n.rules-content {\n  min-height: 200px;\n  margin: 20px 0;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 60px 0;\n  color: #999;\n  font-size: 14px;\n}\n\n.bottom-panel {\n  text-align: center;\n  padding: 20px 0;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n/* 强制覆盖Element UI折叠面板背景色 */\n.el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n  font-size: 16px !important;\n  font-weight: bold !important;\n}\n\n.el-collapse-item__header.is-active {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 使用属性选择器强制覆盖 */\n[class*=\"el-collapse-item__header\"] {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 深度选择器 */\n.exam-editor >>> .el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .exam-editor-left {\n    padding-right: 370px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 70px;\n  }\n}\n\n@media (max-width: 768px) {\n  .exam-editor {\n    flex-direction: column;\n  }\n\n  .exam-editor-left {\n    padding-right: 20px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 20px;\n  }\n\n  .exam-editor-right {\n    position: relative !important;\n    width: 100% !important;\n    height: 50vh;\n    border-left: none;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .collapse-button {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6yDA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;MACA;MACAM,mBAAA;MACAC,eAAA;MAEA;MACAC,cAAA;MAEA;MACAC,SAAA;QACAR,OAAA;QACAS,SAAA;QACAC,SAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,SAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QAAA;QACAC,MAAA;QAAA;QACAC,eAAA;QAAA;QACAC,eAAA;QAAA;QACAC,UAAA;MACA;MAEA;;MAEA;MACAC,cAAA;MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;;MAEA;MACAC,sBAAA;MACAC,eAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACAC,YAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;UACAC,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAC,cAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,qBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,kBAAA;UACAR,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAO,iBAAA;QAAA;QACAC,aAAA;MACA;MAEA;MACAC,qBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MAEA;MACAC,cAAA;MACA;MACAC,KAAA;MAEA;MACAC,iBAAA;MACAC,QAAA;QACAP,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACAK,gBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,wBAAA;MAAA;MACA3B,aAAA;MACA4B,mBAAA;IAAA,sBACA,sBACA,oBACA,gBACA,cACA,mBACA;MACA1B,OAAA;MACAC,QAAA;MACA0B,QAAA,EAAAC;IACA;EAEA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,YAAAjC,aAAA,CAAAkC,MAAA,WAAAC,IAAA;QAAA,OACAF,KAAA,CAAAV,QAAA,CAAAL,aAAA,CAAAkB,QAAA,CAAAD,IAAA,CAAAE,MAAA;MAAA,CACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAlC,KAAA;MACA,KAAAiB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA9C,QAAA,IAAA8C,IAAA,CAAA9C,QAAA,CAAA+C,MAAA;UACA;UACAD,IAAA,CAAA9C,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YACAtC,KAAA,IAAAsC,KAAA,CAAAC,aAAA;UACA;QACA;UACA;UACAvC,KAAA,IAAAoC,IAAA,CAAAG,aAAA;QACA;MACA;MACA,OAAAvC,KAAA;IACA;IAEA;IACAwC,cAAA,WAAAA,eAAA;MACA,IAAAxC,KAAA;MACA,KAAAiB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA9C,QAAA,IAAA8C,IAAA,CAAA9C,QAAA,CAAA+C,MAAA;UACA;UACAD,IAAA,CAAA9C,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YACAtC,KAAA,IAAAsC,KAAA,CAAA3E,UAAA;UACA;QACA;UACA;UACAqC,KAAA,IAAAoC,IAAA,CAAAzE,UAAA;QACA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACAyC,WAAA,WAAAA,YAAA;MACA,YAAAxB,KAAA,CAAAa,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,OAAA,OAAAC,GAAA;MACA,KAAA5B,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAzF,IAAA,UAAAyF,IAAA,CAAAtB,aAAA;UACAsB,IAAA,CAAAtB,aAAA,CAAAqB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAA;MACA,IAAAC,KAAA;MACA,KAAApE,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACA,IAAAxG,IAAA,GAAAwG,QAAA,CAAAxG,IAAA;QACAuG,KAAA,CAAAvG,IAAA,KAAAuG,KAAA,CAAAvG,IAAA;MACA;MACA,OAAAuG,KAAA;IACA;IAEA;IACAE,yBAAA,WAAAA,0BAAA;MACA,IAAApD,KAAA;MACA,KAAAU,gBAAA,CAAAyB,OAAA,WAAAC,IAAA;QACApC,KAAA,IAAAoC,IAAA,CAAAG,aAAA;MACA;MACA,OAAAvC,KAAA;IACA;IAEA;IACAqD,qBAAA,WAAAA,sBAAA;MACA,IAAArD,KAAA;MACA,KAAAU,gBAAA,CAAAyB,OAAA,WAAAC,IAAA;QACApC,KAAA,IAAAoC,IAAA,CAAAzE,UAAA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACAsD,sBAAA,WAAAA,uBAAA;MACA,YAAA5C,gBAAA,CAAAoB,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAa,sBAAA,WAAAA,uBAAA;MACA,IAAAX,OAAA,OAAAC,GAAA;MACA,KAAAnC,gBAAA,CAAAyB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAzF,IAAA,UAAAyF,IAAA,CAAAtB,aAAA;UACAsB,IAAA,CAAAtB,aAAA,CAAAqB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;EACA;EACAY,KAAA;IACA9G,OAAA,WAAAA,QAAA+G,GAAA;MACA,IAAAA,GAAA,SAAA3G,OAAA;QACA,KAAA4G,aAAA;MACA;IACA;IAEA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QAAA,WAAAC,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAAC,QAAA;UAAA,WAAAF,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBAAA,MACAP,MAAA,CAAA1C,QAAA,CAAAP,QAAA,UAAAgD,MAAA,IAAAA,MAAA,CAAAvB,MAAA;kBAAA8B,QAAA,CAAAC,CAAA;kBAAA;gBAAA;gBAAAD,QAAA,CAAAC,CAAA;gBAAA,OACAP,MAAA,CAAAQ,uBAAA;cAAA;gBAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;UAAA,GAAAL,OAAA;QAAA;MAEA;MACAM,IAAA;IACA;EACA;EACAC,OAAA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA;IACA;IAEA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAxH,mBAAA,SAAAA,mBAAA;MACA,KAAAC,eAAA,QAAAD,mBAAA;IACA;IAEA,WACAyH,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,QAAA,CAAAC,IAAA;IACA;IAEA,WACAE,aAAA,WAAAA,cAAA;MACA,KAAAH,QAAA,CAAAC,IAAA;IACA;IAEA,WACAG,oBAAA,WAAAA,qBAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;IACA;IAEA,YACAI,gBAAA,WAAAA,iBAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;IAIA,iBACAK,aAAA,WAAAA,cAAA;MACA,KAAAnE,cAAA;IACA;IAEA,qBACAoE,eAAA,WAAAA,gBAAA;MACA,KAAApE,cAAA;IACA;IAEA,aACAqE,kBAAA,WAAAA,mBAAA;MACA,KAAAjE,gBAAA;MACA,KAAAC,WAAA;;MAEA;MACA,SAAAJ,KAAA,CAAAoB,MAAA;QACA,KAAAlB,QAAA,CAAAP,QAAA,QAAAK,KAAA,IAAAtE,IAAA;MACA;QACA,KAAAwE,QAAA,CAAAP,QAAA;MACA;MAEA,KAAAM,iBAAA;MACA;MACA,KAAAoE,WAAA;QACAxF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjE,mBAAA;MACA;MACAkE,OAAA,CAAAC,GAAA,EACA,IAAAC,8BAAA,OAAAN,WAAA,GACA,IAAAO,sBAAA;QAAA9F,QAAA;MAAA,GACA,EAAA+F,IAAA,WAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAApJ,OAAA,EAAAkJ,KAAA;UAAAG,YAAA,GAAAF,KAAA;UAAAG,gBAAA,GAAAH,KAAA;QACA,IAAApG,aAAA,GAAAsG,YAAA,CAAAE,IAAA;QACAX,MAAA,CAAAzF,KAAA,GAAAkG,YAAA,CAAAlG,KAAA;QACAyF,MAAA,CAAAvG,eAAA,GAAAiH,gBAAA,CAAAC,IAAA,IAAAD,gBAAA,CAAAlJ,IAAA;;QAEA;QACA,IAAAoJ,kBAAA,GAAAzG,aAAA,CAAA0G,GAAA,WAAAvE,IAAA;UAAA,OACA,IAAAwE,+BAAA,EAAAxE,IAAA,CAAAE,MAAA,EAAA6D,IAAA,WAAA5C,KAAA;YACAnB,IAAA,CAAAyE,aAAA,GAAAtD,KAAA,CAAAjG,IAAA,GAAAiG,KAAA,CAAAjG,IAAA,CAAAwJ,UAAA,IAAAvD,KAAA,CAAAjG,IAAA,CAAA+C,KAAA;YACA,OAAA+B,IAAA;UACA,GAAA2E,KAAA;YACA3E,IAAA,CAAAyE,aAAA;YACA,OAAAzE,IAAA;UACA;QAAA,CACA;QAEA2D,OAAA,CAAAC,GAAA,CAAAU,kBAAA,EAAAP,IAAA,WAAAa,cAAA;UACAlB,MAAA,CAAA7F,aAAA,GAAA+G,cAAA;UACAlB,MAAA,CAAAjE,mBAAA;QACA;MACA,GAAAkF,KAAA;QACAjB,MAAA,CAAAjE,mBAAA;MACA;IACA;IAEA,WACAoF,eAAA,WAAAA,gBAAA;MACA,KAAA/B,QAAA,CAAAC,IAAA;MACA,KAAA9D,cAAA;IACA;IAEA,aACA6F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhD,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAA+C,SAAA;QAAA,WAAAhD,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAAA,MACA0C,MAAA,CAAA3F,QAAA,CAAAP,QAAA,UAAAkG,MAAA,CAAA3F,QAAA,CAAAL,aAAA,CAAAuB,MAAA;gBAAA2E,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAGAwC,MAAA,CAAA3F,QAAA,CAAAP,QAAA,UAAAkG,MAAA,CAAA3F,QAAA,CAAAJ,qBAAA,CAAAsB,MAAA;gBAAA2E,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAKAwC,MAAA,CAAA3F,QAAA,CAAAP,QAAA;gBAAAoG,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA,MACA0C,MAAA,CAAAvF,wBAAA;gBAAAyF,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAKA,IAAAwC,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAI,kBAAA;cACA,WAAAJ,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAK,kBAAA;cACA,WAAAL,MAAA,CAAA1F,gBAAA;gBACA;gBACA0F,MAAA,CAAAM,qBAAA;cACA;gBACA;gBACAN,MAAA,CAAAO,UAAA;cACA;cAEAP,MAAA,CAAAjC,QAAA,CAAAyC,OAAA,CAAAR,MAAA,CAAA1F,gBAAA;cACA0F,MAAA,CAAA5F,iBAAA;;cAEA;cACA4F,MAAA,CAAAS,aAAA;YAAA;cAAA,OAAAP,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,IAAA9E,IAAA,QAAAf,WAAA;MACAe,IAAA,CAAAzF,IAAA,QAAAwE,QAAA,CAAAP,QAAA;MACAwB,IAAA,CAAAvB,YAAA,QAAAM,QAAA,CAAAN,YAAA;MAEA,SAAAM,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAA0G,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;;QAEA;QACA,IAAApE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,YAAA;UACArF,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,YAAA;QACA;QACArF,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;MACA,gBAAAzG,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAyG,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;IACA;IAEA,YACA8F,UAAA,WAAAA,WAAA;MAAA,IAAAQ,MAAA;MACA;MACA,SAAAzG,gBAAA,sBAAAE,UAAA;QACA,KAAAwG,aAAA;QACA;MACA;;MAEA;MACA,SAAA3G,QAAA,CAAAP,QAAA,eAAAO,QAAA,CAAAN,YAAA,uBAAAe,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAAgG,KAAA;UACA,IAAA3F,IAAA;YACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YAAA;YACApL,IAAA,EAAAkL,MAAA,CAAA1G,QAAA,CAAAP,QAAA;YACAC,YAAA,EAAAgH,MAAA,CAAA1G,QAAA,CAAAN,YAAA;YACA0B,aAAA;YACAqF,gBAAA;YACAjK,UAAA;YACAmD,aAAA,GAAAiB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA+E,aAAA,EAAAzE,IAAA,CAAAyE;YACA;YACAiB,YAAA,EAAA1F,IAAA,CAAAyE,aAAA;UACA;UACAqB,MAAA,CAAAM,eAAA,CAAA/F,IAAA;UACAyF,MAAA,CAAA5G,KAAA,CAAAmH,IAAA,CAAAhG,IAAA;QACA;MACA;QACA;QACA,KAAA0F,aAAA;MACA;IACA;IAEA,mBACAX,kBAAA,WAAAA,mBAAA;MAAA,IAAAkB,MAAA;MACA;MACA,SAAAlH,QAAA,CAAAP,QAAA,eAAAO,QAAA,CAAAN,YAAA,uBAAAe,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAAgG,KAAA;UACA,IAAA3F,IAAA;YACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YACApL,IAAA,EAAA0L,MAAA,CAAAlH,QAAA,CAAAP,QAAA;YACAC,YAAA,EAAAwH,MAAA,CAAAlH,QAAA,CAAAN,YAAA;YACA0B,aAAA;YACAqF,gBAAA;YACAjK,UAAA;YACAmD,aAAA,GAAAiB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA+E,aAAA,EAAAzE,IAAA,CAAAyE;YACA;YACAiB,YAAA,EAAA1F,IAAA,CAAAyE,aAAA;UACA;UACA6B,MAAA,CAAAC,0BAAA,CAAAlG,IAAA;UACAiG,MAAA,CAAA3H,gBAAA,CAAA0H,IAAA,CAAAhG,IAAA;QACA;MACA;QACA;QACA,KAAAmG,wBAAA;MACA;IACA;IAEA,qBACAA,wBAAA,WAAAA,yBAAA;MACA,IAAAnG,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QACAvL,IAAA,OAAAwE,QAAA,CAAAP,QAAA;QACAC,YAAA,OAAAM,QAAA,CAAAN,YAAA;QACA0B,aAAA;QACAqF,gBAAA;QACAjK,UAAA;QACA8J,YAAA;MACA;MAEA,SAAAtG,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAA0G,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA,gBAAArF,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAyG,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;MAEA,KAAAb,gBAAA,CAAA0H,IAAA,CAAAhG,IAAA;MACA,KAAAkG,0BAAA,CAAAlG,IAAA;IACA;IAEA,oBACAgF,qBAAA,WAAAA,sBAAA;MACA,IAAAhF,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QACAvL,IAAA,OAAAwE,QAAA,CAAAP,QAAA;QACA8B,QAAA,OAAApB,UAAA,CAAA0G,EAAA;QACAzF,aAAA;QACAqF,gBAAA;QACAjK,UAAA;QACA8J,YAAA;MACA;MAEA,SAAAtG,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAyG,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA,gBAAAJ,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAA0G,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA;;MAEA;MACA,UAAAlF,UAAA,CAAAhC,QAAA;QACA,KAAAkJ,IAAA,MAAAlH,UAAA;MACA;;MAEA;MACA,KAAAA,UAAA,CAAAhC,QAAA,CAAA8I,IAAA,CAAAhG,IAAA;;MAEA;MACA,KAAA1B,gBAAA,CAAA0H,IAAA,CAAAhG,IAAA;MACA,KAAAkG,0BAAA,CAAAlG,IAAA;IACA;IAEA,aACA0F,aAAA,WAAAA,cAAA;MACA,IAAA1F,IAAA;QACA4F,EAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;QACAvL,IAAA,OAAAwE,QAAA,CAAAP,QAAA;QACAC,YAAA,OAAAM,QAAA,CAAAN,YAAA;QACA0B,aAAA;QAAA;QACAqF,gBAAA;QAAA;QACAjK,UAAA;QAAA;QACA8J,YAAA;MACA;;MAEA;MACA,SAAArG,gBAAA,sBAAAE,UAAA;QACAc,IAAA,CAAAM,QAAA,QAAApB,UAAA,CAAA0G,EAAA;;QAEA;QACA,UAAA1G,UAAA,CAAAhC,QAAA;UACA,KAAAkJ,IAAA,MAAAlH,UAAA;QACA;;QAEA;QACA,KAAAA,UAAA,CAAAhC,QAAA,CAAA8I,IAAA,CAAAhG,IAAA;MACA;MAEA,SAAAjB,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAA0G,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA+E,aAAA,EAAAzE,IAAA,CAAAyE;UACA;QAAA;QACApE,IAAA,CAAAqF,YAAA,GAAArF,IAAA,CAAAR,aAAA,CAAA8F,MAAA,WAAAC,GAAA,EAAA5F,IAAA;UAAA,OAAA4F,GAAA,IAAA5F,IAAA,CAAAyE,aAAA;QAAA;MACA,gBAAArF,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAyG,mBAAA,CAAA3K,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAqF,YAAA,QAAAlG,wBAAA;MACA;;MAEA;MACA,SAAAH,gBAAA;QACA,KAAAH,KAAA,CAAAmH,IAAA,CAAAhG,IAAA;MACA;;MAEA;MACA,KAAA+F,eAAA,CAAA/F,IAAA;IACA;IAEA,aACAmF,aAAA,WAAAA,cAAA;MACA,KAAApG,QAAA;QACAP,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACA,SAAAK,gBAAA,8BAAAA,gBAAA;QACA,KAAAA,gBAAA;QACA,KAAAC,WAAA;QACA,KAAAC,UAAA;MACA;MACA,KAAAC,wBAAA;IACA;IAEA,WACAkH,YAAA,WAAAA,aAAA;MACA,KAAAnD,WAAA,CAAAxF,OAAA;MACA,KAAAwF,WAAA,CAAA7D,QAAA,QAAA8D,aAAA,IAAA7D,SAAA;MACA,KAAA8D,iBAAA;IACA;IAEA,WACAkD,WAAA,WAAAA,YAAA;MACA,KAAAnD,aAAA;MACA,KAAAD,WAAA,CAAAxF,OAAA;MACA,KAAAwF,WAAA,CAAA7D,QAAA,GAAAC,SAAA;MACA,KAAA8D,iBAAA;IACA;IAEA,aACAmD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzH,QAAA,CAAAL,aAAA,GAAA8H,SAAA,CAAAtC,GAAA,WAAAuC,IAAA;QAAA,OAAAA,IAAA,CAAA5G,MAAA;MAAA;IACA;IAEA,eACA6G,kBAAA,WAAAA,mBAAA7G,MAAA;MAAA,IAAA8G,MAAA;MACA,IAAAhB,KAAA,QAAA5G,QAAA,CAAAL,aAAA,CAAAkI,OAAA,CAAA/G,MAAA;MACA,IAAA8F,KAAA;QACA,KAAA5G,QAAA,CAAAL,aAAA,CAAAmI,MAAA,CAAAlB,KAAA;MACA;;MAEA;MACA,KAAAmB,SAAA;QACA,IAAAC,KAAA,GAAAJ,MAAA,CAAAK,KAAA,CAAAC,iBAAA;QACA,IAAAF,KAAA;UACA;UACA,IAAAG,aAAA,GAAAP,MAAA,CAAAnJ,aAAA,CAAA2J,IAAA,WAAAxH,IAAA;YAAA,OAAAA,IAAA,CAAAE,MAAA,KAAAA,MAAA;UAAA;UACA,IAAAqH,aAAA;YACAH,KAAA,CAAAK,kBAAA,CAAAF,aAAA;UACA;QACA;MACA;IACA;IAEA,WACAG,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAApE,WAAA,CAAAxF,OAAA,GAAA4J,IAAA;MACA,KAAAlE,iBAAA;IACA;IAEA,mBACAmE,eAAA,WAAAA,gBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAC,gBAAA,MAAA5K,eAAA,EAAA0K,UAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAArN,IAAA;IACA;IAEA,4BACAsN,gBAAA,WAAAA,iBAAAC,UAAA,EAAA/B,EAAA;MACA;MACA,IAAAgC,cAAA,QAAAC,iBAAA,CAAAF,UAAA;;MAEA;MACA,IAAAF,QAAA,GAAAG,cAAA,CAAAT,IAAA,WAAAW,GAAA;QAAA,OAAAA,GAAA,CAAAlC,EAAA,KAAAA,EAAA;MAAA;MACA,OAAA6B,QAAA;IACA;IAEA,iBACAI,iBAAA,WAAAA,kBAAAF,UAAA;MACA,IAAAI,MAAA;MAEA,SAAAC,QAAAC,IAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA1N,OAAA,EACAwN,IAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAlG,CAAA,IAAAsG,IAAA;YAAA,IAAAR,GAAA,GAAAM,KAAA,CAAApL,KAAA;YACA+K,MAAA,CAAA/B,IAAA,CAAA8B,GAAA;YACA,IAAAA,GAAA,CAAA5K,QAAA,IAAA4K,GAAA,CAAA5K,QAAA,CAAA+C,MAAA;cACA+H,OAAA,CAAAF,GAAA,CAAA5K,QAAA;YACA;UACA;QAAA,SAAAqL,GAAA;UAAAL,SAAA,CAAAM,CAAA,CAAAD,GAAA;QAAA;UAAAL,SAAA,CAAAO,CAAA;QAAA;MACA;MAEAT,OAAA,CAAAL,UAAA;MACA,OAAAI,MAAA;IACA;IAEA,aACAhC,eAAA,WAAAA,gBAAA/F,IAAA;MACAA,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;IACA;IAEA,aACAkD,mBAAA,WAAAA,oBAAAC,aAAA;MACA,IAAAC,eAAA,OAAA9N,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QACA;QAAA;QAAA;MAAA,QACA,gCACA;;MAEA;MACA,IAAAkG,KAAA,CAAAkI,OAAA,CAAAF,aAAA;QACA,OAAAA,aAAA,CAAAzE,GAAA,WAAA4E,CAAA;UAAA,OAAAF,eAAA,CAAAE,CAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,OAAAH,eAAA,CAAAD,aAAA;IACA;IAIA,cACAK,iBAAA,WAAAA,kBAAAC,SAAA;MACA,IAAAA,SAAA,CAAA1O,IAAA;QACA;MACA,WAAA0O,SAAA,CAAA1O,IAAA;QACA;QACA,IAAA0O,SAAA,CAAAtK,qBAAA,IAAAsK,SAAA,CAAAtK,qBAAA,CAAAsB,MAAA;UACA,IAAA2I,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAA,eAAA,CAAAK,SAAA,CAAAtK,qBAAA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA,iCACAuK,gBAAA,WAAAA,iBAAAlJ,IAAA;MACA,IAAAA,IAAA,CAAAzF,IAAA;QACA;MACA,WAAAyF,IAAA,CAAAzF,IAAA;QACA;MACA;MACA;IACA;IAEA,+BACA4O,cAAA,WAAAA,eAAAnJ,IAAA;MACA,IAAAA,IAAA,CAAAzF,IAAA;QACA;QACA,IAAAyF,IAAA,CAAAR,aAAA,IAAAQ,IAAA,CAAAR,aAAA,CAAAS,MAAA;UACA,OAAAD,IAAA,CAAAR,aAAA,CAAA0E,GAAA,WAAAvE,IAAA;YAAA,OAAAA,IAAA,CAAAN,QAAA;UAAA,GAAA0J,IAAA;QACA;QACA;MACA,WAAA/I,IAAA,CAAAzF,IAAA;QACA;QACA,IAAAyF,IAAA,CAAArB,qBAAA,IAAAqB,IAAA,CAAArB,qBAAA,CAAAsB,MAAA;UACA,IAAA2I,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAA5I,IAAA,CAAArB,qBAAA,CAAAuF,GAAA,WAAA3J,IAAA;YAAA,OAAAqO,eAAA,CAAArO,IAAA;UAAA,GAAAwO,IAAA;QACA;QACA;MACA;MACA;IACA;IAEA,YACAK,UAAA,WAAAA,WAAApJ,IAAA;MACA,KAAAhB,gBAAA;MACA,KAAAE,UAAA,GAAAc,IAAA;;MAEA;MACA,IAAAqJ,eAAA;MAEA,IAAArJ,IAAA,CAAAzF,IAAA;QACA;QACA8O,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAtK,QAAA,CAAAP,QAAA,GAAA6K,eAAA;MACA,KAAAtK,QAAA,CAAAN,YAAA;MACA,KAAAM,QAAA,CAAAL,aAAA;MACA,KAAAK,QAAA,CAAAJ,qBAAA;MAEA,KAAAG,iBAAA;MACA,KAAAoE,WAAA;QACAxF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAkG,QAAA,WAAAA,SAAAtJ,IAAA;MAAA,IAAAuJ,MAAA;MACA,KAAAvK,gBAAA;MACA,KAAAC,WAAA,GAAAe,IAAA;MACA,KAAAjB,QAAA,CAAAP,QAAA,GAAAwB,IAAA,CAAAzF,IAAA;MACA,KAAAwE,QAAA,CAAAN,YAAA,GAAAuB,IAAA,CAAAvB,YAAA;MAEA,IAAAuB,IAAA,CAAAzF,IAAA;QACA;QACA,KAAAwE,QAAA,CAAAL,aAAA,OAAA0G,mBAAA,CAAA3K,OAAA,EAAAuF,IAAA,CAAAtB,aAAA;MACA,WAAAsB,IAAA,CAAAzF,IAAA;QACA;QACA,KAAAwE,QAAA,CAAAJ,qBAAA,OAAAyG,mBAAA,CAAA3K,OAAA,EAAAuF,IAAA,CAAArB,qBAAA;QACA;QACA,KAAAmI,SAAA;UACAyC,MAAA,CAAAtH,uBAAA;QACA;MACA;MAEA,KAAAnD,iBAAA;MACA,KAAAoE,WAAA;QACAxF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAoG,UAAA,WAAAA,WAAAxJ,IAAA;MAAA,IAAAyJ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArP,IAAA;MACA,GAAAmJ,IAAA;QACA,IAAAiC,KAAA,GAAA8D,MAAA,CAAA5K,KAAA,CAAAgL,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAA5F,IAAA,CAAA4F,EAAA;QAAA;QACA,IAAAD,KAAA;UACA8D,MAAA,CAAA5K,KAAA,CAAAgI,MAAA,CAAAlB,KAAA;UACA8D,MAAA,CAAAhH,QAAA,CAAAyC,OAAA;QACA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,YACAyF,eAAA,WAAAA,gBAAA7K,UAAA,EAAA+J,SAAA;MAAA,IAAAe,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArP,IAAA;MACA,GAAAmJ,IAAA;QACA,IAAAiC,KAAA,GAAAzG,UAAA,CAAAhC,QAAA,CAAA2M,SAAA,WAAA3J,KAAA;UAAA,OAAAA,KAAA,CAAA0F,EAAA,KAAAqD,SAAA,CAAArD,EAAA;QAAA;QACA,IAAAD,KAAA;UACAzG,UAAA,CAAAhC,QAAA,CAAA2J,MAAA,CAAAlB,KAAA;UACAqE,MAAA,CAAAvH,QAAA,CAAAyC,OAAA;QACA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,cACA2F,cAAA,WAAAA,eAAA;MACA,aAAAjL,gBAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA,iBACAkL,2BAAA,WAAAA,4BAAA;MACA;MACA,SAAAlL,gBAAA;QACA;MACA;MACA;MACA,SAAAA,gBAAA;QACA;MACA;MACA;MACA;IACA;IAEA,iBACAmL,kBAAA,WAAAA,mBAAA3L,QAAA;MACA;MACA,SAAAQ,gBAAA;QACA,YAAAC,WAAA,CAAA1E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA,sBAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAA3E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,SAAAU,UAAA,CAAA3E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAU,UAAA,CAAA3E,IAAA;MACA;;MAEA;MACA,SAAAyE,gBAAA;QACA;QACA,OAAAR,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA;QACA;QACA,OAAAR,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA,iCAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAA3E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAU,UAAA,CAAA3E,IAAA;MACA;;MAEA;MACA;IACA;IAEA,iBACA6P,sBAAA,WAAAA,uBAAAtM,YAAA;MACA;MACA,SAAAkB,gBAAA,sBAAAA,gBAAA;QACA;MACA;MAEA,UAAAE,UAAA;QACA;MACA;;MAEA;MACA,IAAAmL,qBAAA;MACA,SAAAnL,UAAA,CAAAhC,QAAA;QACA,KAAAgC,UAAA,CAAAhC,QAAA,CAAA6C,OAAA,WAAAG,KAAA;UACA,IAAAA,KAAA,CAAA3F,IAAA,UAAA2F,KAAA,CAAAvB,qBAAA;YACA0L,qBAAA,CAAArE,IAAA,CAAAsE,KAAA,CAAAD,qBAAA,MAAAjF,mBAAA,CAAA3K,OAAA,EAAAyF,KAAA,CAAAvB,qBAAA;UACA;QACA;MACA;;MAEA;MACA,QAAA0L,qBAAA,CAAAzK,QAAA,CAAA9B,YAAA;IACA;IAEA,iBACAyM,eAAA,WAAAA,gBAAAC,GAAA;MACA;MACA,SAAAxL,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA,sBAAAA,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA;QACA,aAAAmC,sBAAA,CAAAvB,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;MACA;;MAEA;MACA,aAAAU,WAAA,CAAAX,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;IACA;IAEA,iBACA4K,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;MACA;MACA,SAAAxL,gBAAA,oBAAAA,gBAAA,sBAAAuB,WAAA,CAAAX,QAAA,CAAA4K,GAAA,CAAA3K,MAAA;QACA;MACA;MACA;IACA;IAEA,eACAoC,uBAAA,WAAAA,wBAAA;MAAA,IAAA0I,MAAA;MAAA,WAAAjJ,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAAgJ,SAAA;QAAA,IAAApL,aAAA,EAAAqL,KAAA,EAAAC,EAAA;QAAA,WAAAnJ,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAAiJ,SAAA;UAAA,kBAAAA,SAAA,CAAA/I,CAAA;YAAA;cAAA,MACA2I,MAAA,CAAA5L,QAAA,CAAAP,QAAA,WAAAmM,MAAA,CAAA5L,QAAA,CAAAJ,qBAAA,CAAAsB,MAAA;gBAAA8K,SAAA,CAAA/I,CAAA;gBAAA;cAAA;cACA2I,MAAA,CAAAxL,wBAAA;cAAA,OAAA4L,SAAA,CAAA7I,CAAA;YAAA;cAIA;cACA1C,aAAA;cACA,KAAAmL,MAAA,CAAA3L,gBAAA,iBAAA2L,MAAA,CAAA3L,gBAAA,6BAAA2L,MAAA,CAAAzL,UAAA,IAAAyL,MAAA,CAAAzL,UAAA,CAAA3E,IAAA;gBACA;gBACAiF,aAAA,GAAAmL,MAAA,CAAAzL,UAAA,CAAAM,aAAA;cACA;gBACA;gBACAA,aAAA,GAAAmL,MAAA,CAAAnL,aAAA;cACA;cAAA,IAEAA,aAAA,CAAAS,MAAA;gBAAA8K,SAAA,CAAA/I,CAAA;gBAAA;cAAA;cACA2I,MAAA,CAAAxL,wBAAA;cACAwL,MAAA,CAAAlI,QAAA,CAAAoC,OAAA;cAAA,OAAAkG,SAAA,CAAA7I,CAAA;YAAA;cAAA6I,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAA/I,CAAA;cAAA,OAKA2I,MAAA,CAAAM,sBAAA,CAAAzL,aAAA,EAAAmL,MAAA,CAAA5L,QAAA,CAAAJ,qBAAA;YAAA;cAAAkM,KAAA,GAAAE,SAAA,CAAAG,CAAA;cACAP,MAAA,CAAAxL,wBAAA,GAAA0L,KAAA;cAEA,IAAAA,KAAA;gBACAF,MAAA,CAAAlI,QAAA,CAAAoC,OAAA;cACA;cAAAkG,SAAA,CAAA/I,CAAA;cAAA;YAAA;cAAA+I,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAG,CAAA;cAEAC,OAAA,CAAAC,KAAA,cAAAN,EAAA;cACAH,MAAA,CAAAxL,wBAAA;cACAwL,MAAA,CAAAlI,QAAA,CAAA2I,KAAA;YAAA;cAAA,OAAAL,SAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA0I,QAAA;MAAA;IAEA;IAEA,oBACAK,sBAAA,WAAAA,uBAAAI,KAAA,EAAA1C,aAAA;MAAA,WAAAjH,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAA0J,SAAA;QAAA,IAAAjH,UAAA,EAAAkH,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAA/J,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAA6J,SAAA;UAAA,kBAAAA,SAAA,CAAA3J,CAAA;YAAA;cAAA,MACA,CAAAqJ,KAAA,CAAApL,MAAA,KAAA0I,aAAA,CAAA1I,MAAA;gBAAA0L,SAAA,CAAA3J,CAAA;gBAAA;cAAA;cAAA,OAAA2J,SAAA,CAAAzJ,CAAA,IACA;YAAA;cAGAmC,UAAA,MAEA;cAAAkH,UAAA,OAAApD,2BAAA,CAAA1N,OAAA,EACA4Q,KAAA;cAAAM,SAAA,CAAAX,CAAA;cAAAS,KAAA,oBAAA9J,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAA6J,MAAA;gBAAA,IAAA9L,IAAA,EAAAiM,QAAA,EAAAC,UAAA,EAAAC,GAAA;gBAAA,WAAAnK,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAAiK,SAAA;kBAAA,kBAAAA,SAAA,CAAA/J,CAAA;oBAAA;sBAAArC,IAAA,GAAA6L,MAAA,CAAAxO,KAAA;sBAAA+O,SAAA,CAAAf,CAAA;sBAAAe,SAAA,CAAA/J,CAAA;sBAAA,OAGA,IAAAmC,+BAAA,EAAAxE,IAAA,CAAAE,MAAA;oBAAA;sBAAA+L,QAAA,GAAAG,SAAA,CAAAb,CAAA;sBACAC,OAAA,CAAAa,GAAA,gBAAAC,MAAA,CAAAtM,IAAA,CAAAE,MAAA,gCAAA+L,QAAA;sBAEA,IAAAA,QAAA,CAAAM,IAAA,YAAAN,QAAA,CAAA/Q,IAAA;wBACAgR,UAAA,GAAAD,QAAA,CAAA/Q,IAAA,EAEA;wBACA8N,aAAA,CAAA5I,OAAA,WAAAxF,IAAA;0BACA,QAAAA,IAAA;4BACA;8BAAA;8BACA8J,UAAA,IAAAwH,UAAA,CAAAM,YAAA;8BACA;4BACA;8BAAA;8BACA9H,UAAA,IAAAwH,UAAA,CAAAO,cAAA;8BACA;4BACA;8BAAA;8BACA/H,UAAA,IAAAwH,UAAA,CAAAQ,QAAA;8BACA;0BACA;wBACA;sBACA;sBAAAN,SAAA,CAAA/J,CAAA;sBAAA;oBAAA;sBAAA+J,SAAA,CAAAf,CAAA;sBAAAc,GAAA,GAAAC,SAAA,CAAAb,CAAA;sBAEAC,OAAA,CAAAC,KAAA,4BAAAa,MAAA,CAAAtM,IAAA,CAAAE,MAAA,4CAAAiM,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAA7J,CAAA;kBAAA;gBAAA,GAAAuJ,KAAA;cAAA;cAAAF,UAAA,CAAAlD,CAAA;YAAA;cAAA,KAAAmD,MAAA,GAAAD,UAAA,CAAAvJ,CAAA,IAAAsG,IAAA;gBAAAqD,SAAA,CAAA3J,CAAA;gBAAA;cAAA;cAAA,OAAA2J,SAAA,CAAAW,CAAA,KAAAC,mBAAA,CAAA9R,OAAA,EAAAgR,KAAA;YAAA;cAAAE,SAAA,CAAA3J,CAAA;cAAA;YAAA;cAAA2J,SAAA,CAAA3J,CAAA;cAAA;YAAA;cAAA2J,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAAAK,UAAA,CAAA/C,CAAA,CAAAkD,GAAA;YAAA;cAAAC,SAAA,CAAAX,CAAA;cAAAO,UAAA,CAAA9C,CAAA;cAAA,OAAAkD,SAAA,CAAAlD,CAAA;YAAA;cAIA0C,OAAA,CAAAa,GAAA,oCAAAC,MAAA,CAAA5H,UAAA;cAAA,OAAAsH,SAAA,CAAAzJ,CAAA,IACAmC,UAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IAEA,eACAkB,qBAAA,WAAAA,sBAAAhO,QAAA;MACA;MACA,SAAAQ,gBAAA;QACA,YAAAC,WAAA,CAAA1E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA;QACA,OAAAR,QAAA;MACA;MAEA;IACA;IAEA,cACAiO,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,KAAAhQ,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAA4L,QAAA,GAAAD,OAAA,CAAA/P,SAAA;MACA;IACA;IAEA,aACAiQ,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAA1O,iBAAA,QAAAzB,cAAA,CAAAgD,MAAA,WAAAoN,CAAA;QAAA,OAAAA,CAAA,CAAAH,QAAA;MAAA;MACA,IAAAxO,iBAAA,CAAA8B,MAAA;QACA,KAAAwC,QAAA,CAAAoC,OAAA;QACA;MACA;MAEA,KAAA6E,QAAA,+CAAAuC,MAAA,CAAA9N,iBAAA,CAAA8B,MAAA;QACA0J,iBAAA;QACAC,gBAAA;QACArP,IAAA;MACA,GAAAmJ,IAAA;QACAmJ,OAAA,CAAAnQ,cAAA,GAAAmQ,OAAA,CAAAnQ,cAAA,CAAAgD,MAAA,WAAAoN,CAAA;UAAA,QAAAA,CAAA,CAAAH,QAAA;QAAA;QACAE,OAAA,CAAAlQ,SAAA;QACAkQ,OAAA,CAAApK,QAAA,CAAAyC,OAAA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,WACAyI,kBAAA,WAAAA,mBAAA;MACA,KAAAlQ,sBAAA;MACA,KAAAmQ,oBAAA;IACA;IAEA,gBACAA,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA3P,YAAA,CAAAC,gBAAA;MACA,KAAAD,YAAA,CAAAE,iBAAA;MACA,KAAAF,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAAX,YAAA,CAAAc,iBAAA;MACA,KAAAd,YAAA,CAAAe,aAAA;;MAEA;MACA,KAAA6O,gBAAA;;MAEA;MACA,KAAAC,6BAAA;IACA;IAEA,cACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,OAAA;MACA,IAAA1J,sBAAA;QAAA9F,QAAA;MAAA,GAAA+F,IAAA,WAAAkI,QAAA;QACA,IAAAjE,UAAA,GAAAiE,QAAA,CAAA5H,IAAA;QACAmJ,OAAA,CAAArQ,eAAA,GAAAqQ,OAAA,CAAAC,iBAAA,CAAAzF,UAAA;MACA,GAAArD,KAAA,WAAA8G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA+B,OAAA,CAAArQ,eAAA;MACA;IACA;IAEA,YACAsQ,iBAAA,WAAAA,kBAAAzF,UAAA;MACA,IAAAzD,GAAA;;MAEA;MACAyD,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;QACAvD,GAAA,CAAAuD,QAAA,CAAA7B,EAAA,QAAAyH,cAAA,CAAA5S,OAAA,MAAA4S,cAAA,CAAA5S,OAAA,MAAAgN,QAAA;UAAAvK,QAAA;QAAA;MACA;;MAEA;MACA,IAAA6K,MAAA;MACAJ,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;QACA,IAAAA,QAAA,CAAAnH,QAAA;UACA;UACAyH,MAAA,CAAA/B,IAAA,CAAA9B,GAAA,CAAAuD,QAAA,CAAA7B,EAAA;QACA;UACA;UACA,IAAA1B,GAAA,CAAAuD,QAAA,CAAAnH,QAAA;YACA4D,GAAA,CAAAuD,QAAA,CAAAnH,QAAA,EAAApD,QAAA,CAAA8I,IAAA,CAAA9B,GAAA,CAAAuD,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;;MAEA;MACA,IAAA0H,mBAAA,YAAAA,mBAAAC,IAAA;QACA,IAAAA,IAAA,CAAArQ,QAAA,IAAAqQ,IAAA,CAAArQ,QAAA,CAAA+C,MAAA;UACA,OAAAsN,IAAA,CAAArQ,QAAA;QACA,WAAAqQ,IAAA,CAAArQ,QAAA,IAAAqQ,IAAA,CAAArQ,QAAA,CAAA+C,MAAA;UACAsN,IAAA,CAAArQ,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YAAA,OAAAoN,mBAAA,CAAApN,KAAA;UAAA;QACA;MACA;;MAEA;MACA6H,MAAA,CAAAhI,OAAA,WAAAwN,IAAA;QAAA,OAAAD,mBAAA,CAAAC,IAAA;MAAA;MAEA,OAAAxF,MAAA;IACA;IAEA,gBACAyF,sBAAA,WAAAA,uBAAAhG,UAAA,EAAAG,UAAA;MACA,IAAAI,MAAA,IAAAP,UAAA;MAEA,IAAAiG,aAAA,YAAAA,aAAAnN,QAAA;QACAqH,UAAA,CAAA5H,OAAA,WAAA0H,QAAA;UACA,IAAAA,QAAA,CAAAnH,QAAA,KAAAA,QAAA;YACAyH,MAAA,CAAA/B,IAAA,CAAAyB,QAAA,CAAA7B,EAAA;YACA6H,aAAA,CAAAhG,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;MAEA6H,aAAA,CAAAjG,UAAA;MACA,OAAAO,MAAA;IACA;IAEA,kBACAmF,6BAAA,WAAAA,8BAAA;MAAA,IAAAQ,OAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAAtQ,YAAA,CAAAC,gBAAA;QACA;QACA,IAAAmG,sBAAA;UAAA9F,QAAA;QAAA,GAAA+F,IAAA,WAAAkI,QAAA;UACA,IAAAgC,aAAA,GAAAhC,QAAA,CAAA5H,IAAA;UACA2J,WAAA,GAAAD,OAAA,CAAAF,sBAAA,CAAAE,OAAA,CAAArQ,YAAA,CAAAC,gBAAA,EAAAsQ,aAAA;;UAEA;UACAF,OAAA,CAAAG,6BAAA,CAAAF,WAAA;QACA,GAAArJ,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACAsC,OAAA,CAAAG,6BAAA;QACA;MACA;QACA;QACA,KAAAA,6BAAA;MACA;IACA;IAEA,mBACAA,6BAAA,WAAAA,8BAAAF,WAAA;MAAA,IAAAG,OAAA;MACA,IAAA5K,WAAA;QACAxF,OAAA,OAAAL,YAAA,CAAAI,cAAA,CAAAC,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAI,cAAA,CAAAE,QAAA;QACA0B,QAAA,OAAAhC,YAAA,CAAAE,iBAAA,IAAA+B;MACA;MAEA,IAAAqO,WAAA,CAAA1N,MAAA;QACA;QACA,IAAA8N,QAAA,GAAAJ,WAAA,CAAAzJ,GAAA,WAAAsD,UAAA;UACA,WAAAhE,8BAAA,MAAA6J,cAAA,CAAA5S,OAAA,MAAA4S,cAAA,CAAA5S,OAAA,MAAAyI,WAAA;YAAAsE,UAAA,EAAAA;UAAA;QACA;QAEAlE,OAAA,CAAAC,GAAA,CAAAwK,QAAA,EAAArK,IAAA,WAAAsK,SAAA;UACA,IAAAC,QAAA;UACA,IAAA5J,UAAA;UAEA2J,SAAA,CAAAjO,OAAA,WAAA6L,QAAA;YACA,IAAAA,QAAA,CAAA5H,IAAA;cACAiK,QAAA,CAAAjI,IAAA,CAAAsE,KAAA,CAAA2D,QAAA,MAAA7I,mBAAA,CAAA3K,OAAA,EAAAmR,QAAA,CAAA5H,IAAA;cACAK,UAAA,IAAAuH,QAAA,CAAAhO,KAAA;YACA;UACA;;UAEA;UACA,IAAAsQ,WAAA,GAAAD,QAAA,CAAAvO,MAAA,WAAAC,IAAA,EAAAgG,KAAA,EAAAwI,IAAA;YAAA,OACAxI,KAAA,KAAAwI,IAAA,CAAAtE,SAAA,WAAAuE,CAAA;cAAA,OAAAA,CAAA,CAAAvO,MAAA,KAAAF,IAAA,CAAAE,MAAA;YAAA;UAAA,CACA;UAEAiO,OAAA,CAAAzQ,YAAA,CAAAG,aAAA,GAAA0Q,WAAA;UACAJ,OAAA,CAAAzQ,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAAsQ,WAAA,CAAAjO,MAAA;QACA,GAAAqE,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAArL,QAAA,CAAA2I,KAAA;UACA0C,OAAA,CAAAzQ,YAAA,CAAAG,aAAA;UACAsQ,OAAA,CAAAzQ,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;QACA;QACA,IAAA4F,8BAAA,EAAAN,WAAA,EAAAQ,IAAA,WAAAkI,QAAA;UACAkC,OAAA,CAAAzQ,YAAA,CAAAG,aAAA,GAAAoO,QAAA,CAAA5H,IAAA;UACA8J,OAAA,CAAAzQ,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAAgO,QAAA,CAAAhO,KAAA;QACA,GAAA0G,KAAA,WAAA8G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAArL,QAAA,CAAA2I,KAAA;UACA0C,OAAA,CAAAzQ,YAAA,CAAAG,aAAA;UACAsQ,OAAA,CAAAzQ,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEA,WACAyQ,mBAAA,WAAAA,oBAAA;MACA,KAAAhR,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAAwP,6BAAA;IACA;IAEA,WACAoB,kBAAA,WAAAA,mBAAA9D,GAAA;MACA,KAAAnN,YAAA,CAAAQ,cAAA,GAAA2M,GAAA,CAAA3K,MAAA;MACA,KAAA0O,aAAA;IACA;IAEA,aACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAAnR,YAAA,CAAAQ,cAAA;QACA,KAAAR,YAAA,CAAAY,SAAA;QACA,KAAAZ,YAAA,CAAAa,kBAAA,CAAAN,KAAA;QACA;MACA;MAEA,IAAAsF,WAAA;QACAxF,OAAA,OAAAL,YAAA,CAAAa,kBAAA,CAAAR,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAa,kBAAA,CAAAP,QAAA;QACAkC,MAAA,OAAAxC,YAAA,CAAAQ,cAAA;QACAC,YAAA,OAAAT,YAAA,CAAAS,YAAA,IAAAwB,SAAA;QACAvB,UAAA,OAAAV,YAAA,CAAAU,UAAA,IAAAuB,SAAA;QACAmP,eAAA,OAAApR,YAAA,CAAAW,qBAAA,IAAAsB;MACA;MAEA,IAAAoP,sBAAA,EAAAxL,WAAA,EAAAQ,IAAA,WAAAkI,QAAA;QACA4C,OAAA,CAAAnR,YAAA,CAAAY,SAAA,GAAA2N,QAAA,CAAA5H,IAAA;QACAwK,OAAA,CAAAnR,YAAA,CAAAa,kBAAA,CAAAN,KAAA,GAAAgO,QAAA,CAAAhO,KAAA;MACA,GAAA0G,KAAA,WAAA8G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAoD,OAAA,CAAA/L,QAAA,CAAA2I,KAAA;QACAoD,OAAA,CAAAnR,YAAA,CAAAY,SAAA;QACAuQ,OAAA,CAAAnR,YAAA,CAAAa,kBAAA,CAAAN,KAAA;MACA;IACA;IAEA,WACA+Q,eAAA,WAAAA,gBAAA;MACA,KAAAtR,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAA6Q,aAAA;IACA;IAEA,aACAK,mBAAA,WAAAA,oBAAA;MACA,KAAAvR,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAA2Q,eAAA;IACA;IAEA,WACAE,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAxQ,qBAAA;MACA,KAAAyQ,oBAAA;MACA,KAAA1L,iBAAA;IACA;IAEA,mBACA0L,oBAAA,WAAAA,qBAAA;MACA,KAAAxQ,gBAAA;MACA,KAAAC,eAAA;QACAC,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;IACA;IAEA,mBACAoQ,wBAAA,WAAAA,yBAAA;MACA,KAAA/P,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAF,QAAA,CAAAP,QAAA;MACA,KAAAM,iBAAA;MACA;MACA,KAAAoE,WAAA;QACAxF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,oBACA4L,2BAAA,WAAAA,4BAAA9P,UAAA;MACA,KAAAF,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,UAAA,GAAAA,UAAA;;MAEA;MACA,IAAAmK,eAAA;MACA,IAAAnK,UAAA,CAAA3E,IAAA;QACA;QACA8O,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAtK,QAAA,CAAAP,QAAA,GAAA6K,eAAA;MACA,KAAAtK,QAAA,CAAAN,YAAA;MACA,KAAAM,QAAA,CAAAL,aAAA;MACA,KAAAK,QAAA,CAAAJ,qBAAA;MAEA,KAAAG,iBAAA;MACA,KAAAoE,WAAA;QACAxF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA6D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,qBACA8C,0BAAA,WAAAA,2BAAAlG,IAAA;MACAA,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAwF,gBAAA;IACA;IAEA,mBACAyJ,qBAAA,WAAAA,sBAAAjP,IAAA;MAAA,IAAAkP,OAAA;MACA;MACA,IAAAlP,IAAA,CAAA9C,QAAA,IAAA8C,IAAA,CAAA9C,QAAA,CAAA+C,MAAA;QACA;QACAD,IAAA,CAAA9C,QAAA,CAAA6C,OAAA,WAAAkJ,SAAA;UACA,IAAAkG,UAAA,GAAAD,OAAA,CAAA5Q,gBAAA,CAAAuL,SAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;UAAA;UACA,IAAAuJ,UAAA;YACAD,OAAA,CAAA5Q,gBAAA,CAAAuI,MAAA,CAAAsI,UAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAxJ,KAAA,QAAArH,gBAAA,CAAAuL,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAA5F,IAAA,CAAA4F,EAAA;MAAA;MACA,IAAAD,KAAA;QACA,KAAArH,gBAAA,CAAAuI,MAAA,CAAAlB,KAAA;MACA;IACA;IAEA,oBACAyJ,0BAAA,WAAAA,2BAAAlQ,UAAA,EAAA+J,SAAA;MACA;MACA,IAAA/J,UAAA,CAAAhC,QAAA;QACA,IAAAiS,UAAA,GAAAjQ,UAAA,CAAAhC,QAAA,CAAA2M,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;QAAA;QACA,IAAAuJ,UAAA;UACAjQ,UAAA,CAAAhC,QAAA,CAAA2J,MAAA,CAAAsI,UAAA;QACA;MACA;;MAEA;MACA,IAAAxJ,KAAA,QAAArH,gBAAA,CAAAuL,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAqD,SAAA,CAAArD,EAAA;MAAA;MACA,IAAAD,KAAA;QACA,KAAArH,gBAAA,CAAAuI,MAAA,CAAAlB,KAAA;MACA;IACA;IAEA,iBACA0J,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5N,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAA2N,SAAA;QAAA,IAAApR,iBAAA,EAAAqR,GAAA;QAAA,WAAA7N,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAA2N,SAAA;UAAA,kBAAAA,SAAA,CAAAzN,CAAA;YAAA;cAAA,MACAsN,OAAA,CAAAhR,gBAAA,CAAA2B,MAAA;gBAAAwP,SAAA,CAAAzN,CAAA;gBAAA;cAAA;cACAsN,OAAA,CAAA7M,QAAA,CAAAoC,OAAA;cAAA,OAAA4K,SAAA,CAAAvN,CAAA;YAAA;cAAAuN,SAAA,CAAAzE,CAAA;cAAAyE,SAAA,CAAAzN,CAAA;cAAA,OAMAsN,OAAA,CAAAI,yBAAA,CAAAJ,OAAA,CAAAhR,gBAAA;YAAA;cAAAH,iBAAA,GAAAsR,SAAA,CAAAvE,CAAA;cAAA,MAEA/M,iBAAA,CAAA8B,MAAA;gBAAAwP,SAAA,CAAAzN,CAAA;gBAAA;cAAA;cACAsN,OAAA,CAAA7M,QAAA,CAAAoC,OAAA;cAAA,OAAA4K,SAAA,CAAAvN,CAAA;YAAA;cAIA;cACA/D,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA,EAAA4E,KAAA;gBACA2J,OAAA,CAAA5S,cAAA,CAAAsJ,IAAA;kBACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;kBACAgK,UAAA,EAAA5O,QAAA,CAAA4O,UAAA;kBACAC,OAAA,EAAA7O,QAAA,CAAA0N,eAAA;kBACAlU,IAAA,EAAAwG,QAAA,CAAAjD,YAAA;kBACAC,UAAA,EAAAgD,QAAA,CAAAhD,UAAA;kBACA8R,OAAA,EAAA9O,QAAA,CAAA8O,OAAA;kBACAC,KAAA,EAAA/O,QAAA,CAAA+O,KAAA;kBAAA;kBACAnD,QAAA;kBACAoD,QAAA;gBACA;cACA;cAEAT,OAAA,CAAAjR,qBAAA;cACAiR,OAAA,CAAA7M,QAAA,CAAAyC,OAAA,2DAAA+G,MAAA,CAAA9N,iBAAA,CAAA8B,MAAA;cAAAwP,SAAA,CAAAzN,CAAA;cAAA;YAAA;cAAAyN,SAAA,CAAAzE,CAAA;cAAAwE,GAAA,GAAAC,SAAA,CAAAvE,CAAA;cAEAC,OAAA,CAAAC,KAAA,YAAAoE,GAAA;cACAF,OAAA,CAAA7M,QAAA,CAAA2I,KAAA;YAAA;cAAA,OAAAqE,SAAA,CAAAvN,CAAA;UAAA;QAAA,GAAAqN,QAAA;MAAA;IAEA;IAEA,eACAG,yBAAA,WAAAA,0BAAA7Q,KAAA;MAAA,IAAAmR,OAAA;MAAA,WAAAtO,kBAAA,CAAAjH,OAAA,mBAAAkH,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAAqO,SAAA;QAAA,IAAAC,YAAA,EAAA7P,WAAA,EAAA8P,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,GAAA;QAAA,WAAA3O,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAAyO,SAAA;UAAA,kBAAAA,SAAA,CAAAvO,CAAA;YAAA;cACAkO,YAAA,OAEA;cACA7P,WAAA,GAAAxB,KAAA,CAAAa,MAAA,WAAAM,IAAA;gBAAA,QAAAA,IAAA,CAAAM,QAAA;cAAA;cAAA6P,UAAA,OAAAhI,2BAAA,CAAA1N,OAAA,EAEA4F,WAAA;cAAAkQ,SAAA,CAAAvF,CAAA;cAAAqF,MAAA,oBAAA1O,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAAyO,OAAA;gBAAA,IAAAnR,UAAA,EAAAsR,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAA1S,SAAA,EAAA2S,UAAA,EAAAC,MAAA,EAAAlR,IAAA,EAAAuD,WAAA,EAAA0I,QAAA,EAAAkF,QAAA,EAAA3Q,aAAA,EAAAhC,iBAAA,EAAA4S,GAAA,EAAAC,GAAA,EAAAC,GAAA;gBAAA,WAAAtP,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAAoP,SAAA;kBAAA,kBAAAA,SAAA,CAAAlP,CAAA;oBAAA;sBAAA9C,UAAA,GAAAkR,MAAA,CAAApT,KAAA;sBAAAkU,SAAA,CAAAlG,CAAA;sBAEA;sBACAwF,UAAA,GAAA3R,KAAA,CAAAa,MAAA,WAAAM,IAAA;wBAAA,OAAAA,IAAA,CAAAM,QAAA,KAAApB,UAAA,CAAA0G,EAAA;sBAAA;sBAAA,MAEA4K,UAAA,CAAAvQ,MAAA;wBAAAiR,SAAA,CAAAlP,CAAA;wBAAA;sBAAA;sBACA;sBAAAyO,UAAA,OAAAtI,2BAAA,CAAA1N,OAAA,EACA+V,UAAA;sBAAAU,SAAA,CAAAlG,CAAA;sBAAA2F,MAAA,oBAAAhP,aAAA,CAAAlH,OAAA,IAAAmH,CAAA,UAAA+O,OAAA;wBAAA,IAAA1H,SAAA,EAAAhL,SAAA,EAAAkT,UAAA,EAAAC,MAAA,EAAAzR,IAAA,EAAA0R,UAAA,EAAAC,MAAA,EAAAxT,YAAA,EAAAoF,WAAA,EAAA0I,QAAA,EAAAkF,QAAA,EAAA3Q,aAAA,EAAAhC,iBAAA,EAAAoT,GAAA,EAAAC,GAAA;wBAAA,WAAA7P,aAAA,CAAAlH,OAAA,IAAAqH,CAAA,WAAA2P,SAAA;0BAAA,kBAAAA,SAAA,CAAAzP,CAAA;4BAAA;8BAAAiH,SAAA,GAAAyH,MAAA,CAAA1T,KAAA;8BACAiB,SAAA,OAEA;8BAAAkT,UAAA,OAAAhJ,2BAAA,CAAA1N,OAAA,EACAyE,UAAA,CAAAM,aAAA;8BAAAiS,SAAA,CAAAzG,CAAA;8BAAAmG,UAAA,CAAA9I,CAAA;4BAAA;8BAAA,KAAA+I,MAAA,GAAAD,UAAA,CAAAnP,CAAA,IAAAsG,IAAA;gCAAAmJ,SAAA,CAAAzP,CAAA;gCAAA;8BAAA;8BAAArC,IAAA,GAAAyR,MAAA,CAAApU,KAAA;8BAAAqU,UAAA,OAAAlJ,2BAAA,CAAA1N,OAAA,EACAwO,SAAA,CAAAtK,qBAAA;8BAAA8S,SAAA,CAAAzG,CAAA;8BAAAqG,UAAA,CAAAhJ,CAAA;4BAAA;8BAAA,KAAAiJ,MAAA,GAAAD,UAAA,CAAArP,CAAA,IAAAsG,IAAA;gCAAAmJ,SAAA,CAAAzP,CAAA;gCAAA;8BAAA;8BAAAlE,YAAA,GAAAwT,MAAA,CAAAtU,KAAA;8BACAkG,WAAA;gCACArD,MAAA,EAAAF,IAAA,CAAAE,MAAA;gCACA/B,YAAA,EAAAA,YAAA;gCACAJ,OAAA;gCACAC,QAAA;8BACA;8BAAA8T,SAAA,CAAAzP,CAAA;8BAAA,OAEA,IAAA0M,sBAAA,EAAAxL,WAAA;4BAAA;8BAAA0I,QAAA,GAAA6F,SAAA,CAAAvG,CAAA;8BACA,IAAAU,QAAA,CAAA5H,IAAA,IAAA4H,QAAA,CAAA5H,IAAA,CAAA/D,MAAA;gCACAhC,SAAA,GAAAA,SAAA,CAAAgO,MAAA,CAAAL,QAAA,CAAA5H,IAAA;8BACA;4BAAA;8BAAAyN,SAAA,CAAAzP,CAAA;8BAAA;4BAAA;8BAAAyP,SAAA,CAAAzP,CAAA;8BAAA;4BAAA;8BAAAyP,SAAA,CAAAzG,CAAA;8BAAAuG,GAAA,GAAAE,SAAA,CAAAvG,CAAA;8BAAAmG,UAAA,CAAA7I,CAAA,CAAA+I,GAAA;4BAAA;8BAAAE,SAAA,CAAAzG,CAAA;8BAAAqG,UAAA,CAAA5I,CAAA;8BAAA,OAAAgJ,SAAA,CAAAhJ,CAAA;4BAAA;8BAAAgJ,SAAA,CAAAzP,CAAA;8BAAA;4BAAA;8BAAAyP,SAAA,CAAAzP,CAAA;8BAAA;4BAAA;8BAAAyP,SAAA,CAAAzG,CAAA;8BAAAwG,GAAA,GAAAC,SAAA,CAAAvG,CAAA;8BAAAiG,UAAA,CAAA3I,CAAA,CAAAgJ,GAAA;4BAAA;8BAAAC,SAAA,CAAAzG,CAAA;8BAAAmG,UAAA,CAAA1I,CAAA;8BAAA,OAAAgJ,SAAA,CAAAhJ,CAAA;4BAAA;8BAIA;8BACA,IAAAxK,SAAA,CAAAgC,MAAA;gCACA6Q,QAAA,GAAAd,OAAA,CAAA0B,YAAA,KAAAtM,mBAAA,CAAA3K,OAAA,EAAAwD,SAAA;gCACAkC,aAAA,GAAAwR,IAAA,CAAAC,GAAA,CAAA3I,SAAA,CAAA9I,aAAA,EAAA2Q,QAAA,CAAA7Q,MAAA;gCACA9B,iBAAA,GAAA2S,QAAA,CAAAe,KAAA,IAAA1R,aAAA,GAEA;gCACAhC,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA;kCACAA,QAAA,CAAA+O,KAAA,GAAA7G,SAAA,CAAAzD,gBAAA;gCACA;gCAEA0K,YAAA,CAAAlK,IAAA,CAAAsE,KAAA,CAAA4F,YAAA,MAAA9K,mBAAA,CAAA3K,OAAA,EAAA0D,iBAAA;8BACA;4BAAA;8BAAA,OAAAsT,SAAA,CAAAvP,CAAA;0BAAA;wBAAA,GAAAyO,MAAA;sBAAA;sBAAAF,UAAA,CAAApI,CAAA;oBAAA;sBAAA,KAAAqI,MAAA,GAAAD,UAAA,CAAAzO,CAAA,IAAAsG,IAAA;wBAAA4I,SAAA,CAAAlP,CAAA;wBAAA;sBAAA;sBAAA,OAAAkP,SAAA,CAAA5E,CAAA,KAAAC,mBAAA,CAAA9R,OAAA,EAAAkW,MAAA;oBAAA;sBAAAO,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAAAkP,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAAAkP,SAAA,CAAAlG,CAAA;sBAAA+F,GAAA,GAAAG,SAAA,CAAAhG,CAAA;sBAAAuF,UAAA,CAAAjI,CAAA,CAAAuI,GAAA;oBAAA;sBAAAG,SAAA,CAAAlG,CAAA;sBAAAyF,UAAA,CAAAhI,CAAA;sBAAA,OAAAyI,SAAA,CAAAzI,CAAA;oBAAA;sBAAAyI,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAGA;sBACA/D,SAAA;sBAAA2S,UAAA,OAAAzI,2BAAA,CAAA1N,OAAA,EAEAyE,UAAA,CAAAM,aAAA;sBAAA0R,SAAA,CAAAlG,CAAA;sBAAA4F,UAAA,CAAAvI,CAAA;oBAAA;sBAAA,KAAAwI,MAAA,GAAAD,UAAA,CAAA5O,CAAA,IAAAsG,IAAA;wBAAA4I,SAAA,CAAAlP,CAAA;wBAAA;sBAAA;sBAAArC,IAAA,GAAAkR,MAAA,CAAA7T,KAAA;sBACAkG,WAAA;wBACArD,MAAA,EAAAF,IAAA,CAAAE,MAAA;wBACAnC,OAAA;wBACAC,QAAA;sBACA;sBAAAuT,SAAA,CAAAlP,CAAA;sBAAA,OAEA,IAAA0M,sBAAA,EAAAxL,WAAA;oBAAA;sBAAA0I,QAAA,GAAAsF,SAAA,CAAAhG,CAAA;sBACA,IAAAU,QAAA,CAAA5H,IAAA,IAAA4H,QAAA,CAAA5H,IAAA,CAAA/D,MAAA;wBACAhC,SAAA,GAAAA,SAAA,CAAAgO,MAAA,CAAAL,QAAA,CAAA5H,IAAA;sBACA;oBAAA;sBAAAkN,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAAAkP,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAAAkP,SAAA,CAAAlG,CAAA;sBAAAgG,GAAA,GAAAE,SAAA,CAAAhG,CAAA;sBAAA0F,UAAA,CAAApI,CAAA,CAAAwI,GAAA;oBAAA;sBAAAE,SAAA,CAAAlG,CAAA;sBAAA4F,UAAA,CAAAnI,CAAA;sBAAA,OAAAyI,SAAA,CAAAzI,CAAA;oBAAA;sBAGA;sBACA,IAAAxK,SAAA,CAAAgC,MAAA;wBACA6Q,QAAA,GAAAd,OAAA,CAAA0B,YAAA,KAAAtM,mBAAA,CAAA3K,OAAA,EAAAwD,SAAA;wBACAkC,aAAA,GAAAwR,IAAA,CAAAC,GAAA,CAAA1S,UAAA,CAAAiB,aAAA,EAAA2Q,QAAA,CAAA7Q,MAAA;wBACA9B,iBAAA,GAAA2S,QAAA,CAAAe,KAAA,IAAA1R,aAAA,GAEA;wBACAhC,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA;0BACAA,QAAA,CAAA+O,KAAA,GAAA5Q,UAAA,CAAAsG,gBAAA;wBACA;wBAEA0K,YAAA,CAAAlK,IAAA,CAAAsE,KAAA,CAAA4F,YAAA,MAAA9K,mBAAA,CAAA3K,OAAA,EAAA0D,iBAAA;sBACA;oBAAA;sBAAA+S,SAAA,CAAAlP,CAAA;sBAAA;oBAAA;sBAAAkP,SAAA,CAAAlG,CAAA;sBAAAiG,GAAA,GAAAC,SAAA,CAAAhG,CAAA;sBAGAC,OAAA,CAAAC,KAAA,YAAA6F,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAAhP,CAAA;kBAAA;gBAAA,GAAAmO,MAAA;cAAA;cAAAF,UAAA,CAAA9H,CAAA;YAAA;cAAA,KAAA+H,MAAA,GAAAD,UAAA,CAAAnO,CAAA,IAAAsG,IAAA;gBAAAiI,SAAA,CAAAvO,CAAA;gBAAA;cAAA;cAAA,OAAAuO,SAAA,CAAAjE,CAAA,KAAAC,mBAAA,CAAA9R,OAAA,EAAA4V,MAAA;YAAA;cAAAE,SAAA,CAAAvO,CAAA;cAAA;YAAA;cAAAuO,SAAA,CAAAvO,CAAA;cAAA;YAAA;cAAAuO,SAAA,CAAAvF,CAAA;cAAAsF,GAAA,GAAAC,SAAA,CAAArF,CAAA;cAAAiF,UAAA,CAAA3H,CAAA,CAAA8H,GAAA;YAAA;cAAAC,SAAA,CAAAvF,CAAA;cAAAmF,UAAA,CAAA1H,CAAA;cAAA,OAAA8H,SAAA,CAAA9H,CAAA;YAAA;cAAA,OAAA8H,SAAA,CAAArO,CAAA,IAIAgO,YAAA;UAAA;QAAA,GAAAD,QAAA;MAAA;IACA;IAEA,aACAyB,YAAA,WAAAA,aAAAI,KAAA;MACA,SAAAC,CAAA,GAAAD,KAAA,CAAA7R,MAAA,MAAA8R,CAAA,MAAAA,CAAA;QACA,IAAAC,CAAA,GAAAL,IAAA,CAAAM,KAAA,CAAAN,IAAA,CAAAO,MAAA,MAAAH,CAAA;QAAA,IAAAI,KAAA,GACA,CAAAL,KAAA,CAAAE,CAAA,GAAAF,KAAA,CAAAC,CAAA;QAAAD,KAAA,CAAAC,CAAA,IAAAI,KAAA;QAAAL,KAAA,CAAAE,CAAA,IAAAG,KAAA;MACA;MACA,OAAAL,KAAA;IACA;IAEA,SACAM,UAAA,WAAAA,WAAA;MACA,KAAA3P,QAAA,CAAAC,IAAA;IACA;IAEA,aACA2P,mBAAA,WAAAA,oBAAA;MACA,KAAA5P,QAAA,CAAAC,IAAA;IACA;IAEA,SACA4P,YAAA,WAAAA,aAAA;MACA,KAAA7P,QAAA,CAAAC,IAAA;IACA;IAEA,YACA6P,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAA5V,UAAA,SAAAA,UAAA;MACA;MACA,KAAAF,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAAgP,QAAA,GAAAyC,OAAA,CAAA5V,UAAA;MACA;MACA,KAAA6F,QAAA,CAAAC,IAAA,UAAAuJ,MAAA,MAAArP,UAAA;IACA;IAEA,aACA6V,6BAAA,WAAAA,8BAAAjM,SAAA;MACA,KAAAnJ,YAAA,CAAAc,iBAAA,GAAAqI,SAAA;MACA,KAAAkM,mBAAA;IACA;IAEA,eACAA,mBAAA,WAAAA,oBAAA;MACA,IAAA5R,KAAA;MACA,KAAAzD,YAAA,CAAAc,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA;QACA,IAAAxG,IAAA,GAAAwG,QAAA,CAAAjD,YAAA;QACAgD,KAAA,CAAAvG,IAAA,KAAAuG,KAAA,CAAAvG,IAAA;MACA;MACA,KAAA8C,YAAA,CAAAe,aAAA,GAAA0C,KAAA;IACA;IAEA,aACA6R,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,SAAAvV,YAAA,CAAAc,iBAAA,CAAA8B,MAAA;QACA,KAAAwC,QAAA,CAAAoC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxH,YAAA,CAAAc,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA,EAAA4E,KAAA;QACAiN,OAAA,CAAAlW,cAAA,CAAAsJ,IAAA;UACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;UACAgK,UAAA,EAAA5O,QAAA,CAAA4O,UAAA;UACAC,OAAA,EAAA7O,QAAA,CAAA0N,eAAA;UACAlU,IAAA,EAAAwG,QAAA,CAAAjD,YAAA;UACAC,UAAA,EAAAgD,QAAA,CAAAhD,UAAA;UACA8R,OAAA,EAAA9O,QAAA,CAAA8O,OAAA;UAAA;UACAC,KAAA;UAAA;UACAnD,QAAA;UACAoD,QAAA;QACA;MACA;MAEA,KAAAlT,sBAAA;MACA,KAAA4F,QAAA,CAAAyC,OAAA,6BAAA+G,MAAA,MAAA5O,YAAA,CAAAc,iBAAA,CAAA8B,MAAA;IACA;IAEA,eACA4S,oBAAA,WAAAA,qBAAAxR,GAAA;MACA,KAAAhE,YAAA,CAAAI,cAAA,CAAAE,QAAA,GAAA0D,GAAA;MACA,KAAAhE,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAAwP,6BAAA;IACA;IAEA,cACA4F,uBAAA,WAAAA,wBAAAzR,GAAA;MACA,KAAAhE,YAAA,CAAAI,cAAA,CAAAC,OAAA,GAAA2D,GAAA;MACA,KAAA6L,6BAAA;IACA;IAEA,eACA6F,wBAAA,WAAAA,yBAAA1R,GAAA;MACA,KAAAhE,YAAA,CAAAa,kBAAA,CAAAP,QAAA,GAAA0D,GAAA;MACA,KAAAhE,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAA6Q,aAAA;IACA;IAEA,cACAyE,2BAAA,WAAAA,4BAAA3R,GAAA;MACA,KAAAhE,YAAA,CAAAa,kBAAA,CAAAR,OAAA,GAAA2D,GAAA;MACA,KAAAkN,aAAA;IACA;IAEA,aACA0E,iBAAA,WAAAA,kBAAAlV,UAAA;MACA,IAAAmV,aAAA,OAAApY,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAAyY,aAAA,CAAAnV,UAAA;IACA;IAEA,aACAoV,mBAAA,WAAAA,oBAAApS,QAAA;MACA;MACA,KAAAqS,YAAA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAAtS,QAAA;MACAA,QAAA,CAAAgP,QAAA,IAAAhP,QAAA,CAAAgP,QAAA;IACA;IAEA,aACAuD,cAAA,WAAAA,eAAAvS,QAAA,EAAA4E,KAAA;MAAA,IAAA4N,OAAA;MACA,KAAA7J,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArP,IAAA;MACA,GAAAmJ,IAAA;QACA6P,OAAA,CAAA7W,cAAA,CAAAmK,MAAA,CAAAlB,KAAA;QACA4N,OAAA,CAAA9Q,QAAA,CAAAyC,OAAA;MACA,GAAAZ,KAAA;QACA;MAAA,CACA;IACA;IAEA,kBACAkP,YAAA,WAAAA,aAAAC,UAAA;MACA;QACA,WAAAA,UAAA;UACA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,UAAA;QACA;QACA,OAAAA,UAAA;MACA,SAAArI,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACA;MACA;IACA;IAEA,gBACAwI,oBAAA,WAAAA,qBAAApJ,GAAA;MACA;MACA,aAAA9N,cAAA,CAAAmX,IAAA,WAAA9S,QAAA;QAAA,OAAAA,QAAA,CAAA4O,UAAA,KAAAnF,GAAA,CAAAmF,UAAA;MAAA;IACA;IAEA,iBACAmE,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAAvJ,GAAA,GAAAuJ,KAAA,CAAAvJ,GAAA;MACA;MACA,SAAA9N,cAAA,CAAAmX,IAAA,WAAA9S,QAAA;QAAA,OAAAA,QAAA,CAAA4O,UAAA,KAAAnF,GAAA,CAAAmF,UAAA;MAAA;QACA;MACA;MACA;IACA;IAEA,YACAqE,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAA1Z,IAAA,CAAAqM,OAAA;MACA,IAAAuN,MAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,OAAA;QACA,KAAAzR,QAAA,CAAA2I,KAAA;QACA;MACA;MACA,KAAA+I,MAAA;QACA,KAAA1R,QAAA,CAAA2I,KAAA;QACA;MACA;MACA;IACA;IAEA,YACAiJ,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAA9Z,MAAA,CAAA2Z,IAAA,CAAAI,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAja,MAAA,CAAA2Z,IAAA,CAAAO,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAna,MAAA,CAAA2Z,IAAA,CAAAS,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAra,MAAA,CAAA2Z,IAAA,CAAAW,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAva,MAAA,CAAA2Z,IAAA,CAAAa,UAAA,IAAAR,QAAA;MACA,UAAA1I,MAAA,CAAAsI,IAAA,OAAAtI,MAAA,CAAAwI,KAAA,OAAAxI,MAAA,CAAA2I,GAAA,OAAA3I,MAAA,CAAA6I,KAAA,OAAA7I,MAAA,CAAA+I,OAAA,OAAA/I,MAAA,CAAAiJ,OAAA;IACA;IAEA,aACA5T,aAAA,WAAAA,cAAA;MACA,SAAA5G,OAAA;QACA;QACAyQ,OAAA,CAAAa,GAAA,iBAAAtR,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}