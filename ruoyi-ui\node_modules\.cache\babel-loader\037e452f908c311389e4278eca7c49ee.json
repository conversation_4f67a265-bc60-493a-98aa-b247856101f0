{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754445546495}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_questionBank", "require", "_category", "_question", "name", "props", "visible", "type", "Boolean", "default", "paperId", "String", "Number", "data", "_defineProperty2", "rightPanelCollapsed", "rightPanel<PERSON><PERSON>th", "activeCollapse", "paperForm", "paperName", "paperDesc", "paperType", "coverImg", "totalScore", "passScore", "startTime", "endTime", "duration", "lateLimit", "allowEarlySubmit", "earlySubmitTime", "showScore", "showType", "requireAllAnswered", "showResult", "showCorrect", "showAnswer", "showAnalysis", "status", "enableTimeLimit", "durationSeconds", "createTime", "fixedQuestions", "selectAll", "isExpanded", "showManualSelectDialog", "categoryOptions", "cascaderProps", "value", "label", "children", "checkStrictly", "emitPath", "manualSelect", "selectedCate<PERSON><PERSON>", "bankSearchKeyword", "questionBanks", "bankPagination", "pageNum", "pageSize", "total", "selectedBankId", "questionType", "difficulty", "questionSearchKeyword", "questions", "questionPagination", "selectedQuestions", "selectedStats", "showFixedRandomDialog", "fixedRandomRules", "fixedRandomForm", "ruleType", "generateType", "selectedItems", "selectedQuestionTypes", "showRuleDialog", "rules", "showAddRuleDialog", "ruleForm", "currentOperation", "editingRule", "parentRule", "currentQuestionTypeCount", "questionBankLoading", "bankName", "undefined", "computed", "selectedBanks", "_this", "filter", "bank", "includes", "bankId", "totalQuestions", "for<PERSON>ach", "rule", "length", "child", "selectedCount", "totalRuleScore", "parentRules", "parentId", "usedBankIds", "usedIds", "Set", "add", "Array", "from", "fixedQuestionStats", "stats", "question", "fixedRandomTotalQuestions", "fixedRandomTotalScore", "watch", "val", "loadPaperData", "handler", "newVal", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "updateQuestionTypeCount", "a", "deep", "methods", "handleBack", "$emit", "toggleRightPanel", "handleExamEntry", "$message", "info", "handlePageSetting", "handlePublish", "handleInviteStudents", "handleInviteList", "handleAddRule", "handleEditRules", "handleAddFirstRule", "queryParams", "searchKeyword", "loadQuestionBanks", "_this3", "Promise", "all", "listQuestionBank", "listCategory", "then", "_ref2", "_ref3", "_slicedToArray2", "bankResponse", "categoryResponse", "rows", "statisticsPromises", "map", "getQuestionStatistics", "questionCount", "totalCount", "catch", "banksWithStats", "handleSaveRules", "handleSaveRule", "_this4", "_callee2", "_context2", "warning", "updateExistingRule", "addFixedRandomRule", "addNewRule", "success", "resetRuleForm", "_toConsumableArray2", "maxQuestions", "reduce", "sum", "scorePerQuestion", "_this5", "addSingleRule", "index", "id", "Date", "now", "updateRuleScore", "push", "_this6", "updateFixedRandomRuleScore", "addSingleFixedRandomRule", "$set", "handleSearch", "handleReset", "handleSelectionChange", "selection", "item", "removeSelectedBank", "_this7", "indexOf", "splice", "$nextTick", "table", "$refs", "questionBankTable", "rowToDeselect", "find", "toggleRowSelection", "handleCurrentChange", "page", "getCategoryName", "categoryId", "category", "findCategoryById", "categories", "flatCategories", "flattenCategories", "cat", "result", "flatten", "cats", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "err", "e", "f", "getQuestionTypeText", "questionTypes", "questionTypeMap", "isArray", "t", "join", "getChildRule<PERSON>abel", "childRule", "getRuleTypeLabel", "get<PERSON>ule<PERSON><PERSON>nt", "addSubRule", "defaultRuleType", "editRule", "_this8", "deleteRule", "_this9", "$confirm", "confirmButtonText", "cancelButtonText", "findIndex", "r", "deleteChildRule", "_this0", "getDialogTitle", "shouldShowRuleTypeSelection", "shouldShowRuleType", "shouldShowQuestionType", "existingQuestionTypes", "apply", "isRowSelectable", "row", "getRowClassName", "_ref4", "_this1", "_callee3", "count", "_t", "_context3", "p", "getQuestionCountByType", "v", "console", "error", "banks", "_callee4", "_iterator2", "_step2", "_loop", "_t3", "_context5", "response", "statistics", "_t2", "_context4", "log", "concat", "code", "singleChoice", "multipleChoice", "judgment", "d", "_regeneratorValues2", "shouldDisableRuleType", "handleSelectAll", "_this10", "selected", "handleDeleteSelected", "_this11", "q", "handleManualSelect", "initManualSelectData", "loadCategoryTree", "loadManualSelectQuestionBanks", "_this12", "buildCategoryTree", "_objectSpread2", "cleanEmptyChildren", "node", "getAllChildCategoryIds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this13", "categoryIds", "allCategories", "loadQuestionBanksByCategories", "_this14", "promises", "responses", "allBanks", "uniqueBanks", "self", "b", "searchQuestionBanks", "selectQuestionBank", "loadQuestions", "_this15", "questionContent", "listQuestion", "searchQuestions", "resetQuestionSearch", "handleRandomSelect", "resetFixedRandomForm", "handleAddFixedRandomRule", "deleteFixedRandomRule", "handleConfirmFixedRandomSelect", "_this16", "_callee5", "_t4", "_context6", "extractQuestionsFromRules", "questionId", "content", "options", "score", "expanded", "_this17", "_callee6", "allQuestions", "_iterator3", "_step3", "_loop2", "_t8", "_context8", "_iterator4", "_step4", "_iterator5", "_step5", "_queryParams", "_response", "shuffled", "_t5", "_t6", "_t7", "_context7", "shuffle<PERSON><PERSON><PERSON>", "Math", "min", "slice", "array", "i", "j", "floor", "random", "_ref5", "handleSort", "handleBatchSetScore", "handleExport", "handleToggleExpand", "_this18", "handleQuestionSelectionChange", "updateSelectedStats", "confirmManualSelect", "_this19", "handleBankSizeChange", "handleBankCurrentChange", "handleQuestionSizeChange", "handleQuestionCurrentChange", "getDifficultyText", "difficultyMap", "updateQuestionScore", "$forceUpdate", "toggleQuestionExpand", "deleteQuestion", "_this20", "parseOptions", "optionsStr", "JSON", "parse", "isQuestionSelectable", "some", "getQuestionRowClassName", "_ref6", "beforeUpload", "file", "isImage", "isLt2M", "size", "formatDate", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["src/views/biz/paper/create.vue"], "sourcesContent": ["<template>\n  <div class=\"exam-editor\">\n    <!-- 左侧主要内容区域 -->\n    <div class=\"exam-editor-left\" :class=\"{ collapsed: rightPanelCollapsed }\">\n      <!-- 顶部操作栏 -->\n      <div class=\"editPaper_main_top\">\n        <div class=\"el-button-group\">\n          <el-button type=\"primary\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i>\n            <span>返回试卷</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button @click=\"handleExamEntry\">\n            <i class=\"el-icon-share\"></i>\n            <span>考试入口</span>\n          </el-button>\n          <el-button @click=\"handlePageSetting\">\n            <i class=\"el-icon-picture-outline\"></i>\n            <span>设置考试页面</span>\n          </el-button>\n          <el-button type=\"warning\" @click=\"handlePublish\">\n            <i class=\"el-icon-upload\"></i>\n            <span>发布</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button type=\"success\" @click=\"handleInviteStudents\">\n            邀请考生\n          </el-button>\n          <el-button type=\"warning\" @click=\"handleInviteList\">\n            已邀请列表\n          </el-button>\n        </div>\n        \n        <div class=\"clear_both\"></div>\n      </div>\n\n      <!-- 试卷信息卡片 -->\n      <div class=\"subject_main-wrapper\">\n        <div class=\"subject_main\">\n          <el-card class=\"is-hover-shadow\" style=\"width: 100%;\">\n            <div class=\"subtitle-title\" style=\"padding: 10px;\">\n              <div>\n                <div style=\"float: left;\">\n                  <strong class=\"slgfont\">{{ paperForm.paperName || '新建试卷' }}</strong>\n                </div>\n                <div style=\"float: right; color: #ec661a; width: 130px; text-align: right;\">\n                  <span style=\"font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;\">{{ totalRuleScore }}</span>分\n                </div>\n                <div style=\"clear: both;\"></div>\n                <div class=\"paper-count\" style=\"font-size: 13px; color: #aaa;\">\n                  <span v-if=\"paperForm.createTime\">创建时间：{{ paperForm.createTime }}</span>\n                  <el-tag type=\"warning\" size=\"medium\" effect=\"light\">共 {{ totalQuestions }} 题</el-tag>\n                  <el-tag type=\"success\" size=\"medium\" effect=\"light\">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>\n                  <el-tag size=\"medium\" effect=\"light\">自动出分</el-tag>\n                </div>\n                <div class=\"rich-text\" style=\"display: none;\"></div>\n              </div>\n              <div style=\"clear: both;\"></div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 题目设置区域 -->\n        <div>\n          <div class=\"mb10 mt10\">\n            <div class=\"el-button-group\"></div>\n          </div>\n          \n          <div v-if=\"paperForm.paperType === 1\">\n            <!-- 随机试卷 -->\n            <div class=\"random-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 有规则时显示规则内容 -->\n                <div v-if=\"rules.length > 0\" style=\"padding: 10px;\">\n                  <div class=\"tac pd5\">\n                    <div>\n                      <el-button type=\"primary\" @click=\"handleEditRules\">\n                        <i class=\"el-icon-edit\"></i>\n                        <span>编辑规则</span>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 规则显示区域 -->\n                  <div v-for=\"rule in parentRules\" :key=\"rule.id\" class=\"rule-display-container\">\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule-display\">\n                      <span class=\"rule-label\">{{ getRuleTypeLabel(rule) }}</span>\n                      <span class=\"rule-content\">{{ getRuleContent(rule) }}</span>\n                    </div>\n\n                    <!-- 子规则显示 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules-display\">\n                      <div v-for=\"childRule in rule.children\" :key=\"childRule.id\" class=\"child-rule-display\">\n                        <div class=\"child-rule-left\">\n                          <span class=\"rule-label\">{{ getRuleTypeLabel(childRule) }}</span>\n                          <span class=\"rule-content\">{{ getRuleContent(childRule) }}</span>\n                        </div>\n                        <div class=\"child-rule-right\">\n                          <span class=\"rule-stats\">\n                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，\n                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，\n                            总分 <strong>{{ childRule.totalScore }}</strong> 分\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 没有规则时显示添加按钮 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 10px;\">\n                  <div>\n                    <div class=\"mb10\">点击添加规则设置本试卷的抽题规则</div>\n                    <el-button type=\"primary\" @click=\"handleAddRule\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>添加规则</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n          \n          <div v-else>\n            <!-- 固定试卷 -->\n            <div class=\"fixed-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 操作栏 -->\n                <div class=\"mb10 mt10\">\n                  <div class=\"el-button-group\">\n                    <label class=\"el-checkbox fl el-checkbox--medium is-bordered\" style=\"padding: 7.5px 20px;\">\n                      <span class=\"el-checkbox__input\">\n                        <span class=\"el-checkbox__inner\"></span>\n                        <input type=\"checkbox\" aria-hidden=\"false\" class=\"el-checkbox__original\" value=\"\" v-model=\"selectAll\" @change=\"handleSelectAll\">\n                      </span>\n                      <span class=\"el-checkbox__label\">全选</span>\n                    </label>\n                    <el-tooltip content=\"删除选中题目\" placement=\"top\">\n                      <el-button type=\"danger\" size=\"medium\" @click=\"handleDeleteSelected\">\n                        <i class=\"el-icon-delete\"></i>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n\n                  <div class=\"el-button-group\">\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>手动选题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleRandomSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>随机抽题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleSort\">\n                      <i class=\"el-icon-sort\"></i>\n                      <span>排序</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleBatchSetScore\">\n                      <i class=\"icon_size iconfont icon-batch-add\"></i>\n                      <span>批量设置分数</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleExport\">\n                      <i class=\"icon_size iconfont icon-daochu\"></i>\n                      <span>导出</span>\n                    </el-button>\n                  </div>\n\n                  <el-button type=\"default\" size=\"medium\" style=\"vertical-align: middle;\" @click=\"handleToggleExpand\">\n                    <i class=\"icon_size iconfont icon-zhankai\"></i>\n                    <span>{{ isExpanded ? '收起' : '展开' }}</span>\n                  </el-button>\n                </div>\n\n                <!-- 题目列表区域 -->\n                <div v-if=\"fixedQuestions.length > 0\" class=\"question-list\">\n                  <!-- 这里将显示已添加的题目列表 -->\n                  <div class=\"question-item\" v-for=\"(question, index) in fixedQuestions\" :key=\"question.id\">\n                    <!-- 题目头部 -->\n                    <div class=\"question-header\">\n                      <div class=\"question-header-left\">\n                        <el-checkbox v-model=\"question.selected\"></el-checkbox>\n                        <span class=\"question-number\">{{ index + 1 }}.</span>\n                        <span class=\"question-content\">{{ question.content }}</span>\n                      </div>\n                      <div class=\"question-header-right\">\n                        <!-- 分数设置 -->\n                        <el-input-number\n                          v-model=\"question.score\"\n                          :min=\"0.5\"\n                          :step=\"0.5\"\n                          size=\"mini\"\n                          style=\"width: 100px;\"\n                          @change=\"updateQuestionScore(question)\"\n                        ></el-input-number>\n                        <span style=\"margin-left: 5px; margin-right: 10px;\">分</span>\n\n                        <!-- 展开/收起按钮 -->\n                        <el-tooltip :content=\"question.expanded ? '收起' : '展开'\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"toggleQuestionExpand(question)\"\n                            style=\"margin-right: 5px;\"\n                          >\n                            <i :class=\"question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                          </el-button>\n                        </el-tooltip>\n\n                        <!-- 删除按钮 -->\n                        <el-tooltip content=\"删除\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"deleteQuestion(question, index)\"\n                            style=\"color: #f56c6c;\"\n                          >\n                            <i class=\"el-icon-delete\"></i>\n                          </el-button>\n                        </el-tooltip>\n                      </div>\n                    </div>\n\n                    <!-- 题目详情（展开时显示） -->\n                    <div v-if=\"question.expanded\" class=\"question-details\">\n                      <div class=\"question-info\">\n                        <span class=\"info-item\">题型：{{ getQuestionTypeText(question.type) }}</span>\n                        <span class=\"info-item\">难度：{{ getDifficultyText(question.difficulty) }}</span>\n                      </div>\n\n                      <!-- 选项显示 -->\n                      <div v-if=\"question.options\" class=\"question-options\">\n                        <div class=\"options-title\">选项：</div>\n                        <div\n                          v-for=\"(option, optIndex) in parseOptions(question.options)\"\n                          :key=\"optIndex\"\n                          class=\"option-item\"\n                          :class=\"{ 'correct-option': option.isCorrect }\"\n                        >\n                          <span class=\"option-key\">{{ option.key }}.</span>\n                          <span class=\"option-content\">{{ option.content }}</span>\n                          <span v-if=\"option.isCorrect\" class=\"correct-mark\">✓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 40px 10px;\">\n                  <div>\n                    <div class=\"mb10\">暂无题目，点击上方按钮添加题目到试卷中</div>\n                    <el-button type=\"primary\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>开始添加题目</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧设置面板 -->\n    <div class=\"exam-editor-right\" :style=\"{ width: rightPanelWidth + 'px' }\">\n      <i \n        :class=\"rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'\" \n        class=\"collapse-button\" \n        title=\"收起/展开\"\n        @click=\"toggleRightPanel\"\n      ></i>\n      \n      <div v-show=\"!rightPanelCollapsed\">\n        <div class=\"editor-header editor-header--big\">\n          <span>考试设置</span>\n          <i class=\"el-icon-close close-button\" @click=\"handleBack\" title=\"关闭\"></i>\n        </div>\n        \n        <div class=\"main\">\n          <el-form class=\"h100p\">\n            <el-collapse v-model=\"activeCollapse\" class=\"main-collapse h100p\" accordion>\n              <!-- 基础设置 -->\n              <el-collapse-item title=\"基础设置\" name=\"basic\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-setting\" style=\"color: #67c23a;\"></i>基础设置\n                  </div>\n                </template>\n\n                <div class=\"main-module\">\n                  <!-- 封面图 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>封面图</b>\n                      <div class=\"input-area\">\n                        <div style=\"margin-top: 20px;\">\n                          <span class=\"dpib\" style=\"width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;\">\n                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2\n                          </span>\n                          <div class=\"g-component-cover-uploader dpib\">\n                            <div class=\"avatar-uploader\" style=\"width: 120px; height: 80px;\">\n                              <el-upload\n                                class=\"avatar-uploader\"\n                                action=\"#\"\n                                :show-file-list=\"false\"\n                                :before-upload=\"beforeUpload\"\n                                accept=\".png, .jpeg, .jpg\"\n                              >\n                                <div class=\"image_area\">\n                                  <img v-if=\"paperForm.coverImg\" :src=\"paperForm.coverImg\" class=\"avatar\">\n                                  <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                                </div>\n                              </el-upload>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷名称 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷名称</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperName\"\n                          placeholder=\"请输入试卷名称\"\n                          maxlength=\"100\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷描述 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷描述</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperDesc\"\n                          type=\"textarea\"\n                          :rows=\"3\"\n                          placeholder=\"请输入试卷描述\"\n                          maxlength=\"500\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 出题方式 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>出题方式</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"paper-type-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">出题方式</div>\n                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>\n                            <br>\n                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-radio-group v-model=\"paperForm.paperType\" size=\"mini\">\n                          <el-radio-button :label=\"1\">随机试卷</el-radio-button>\n                          <el-radio-button :label=\"0\">固定试卷</el-radio-button>\n                        </el-radio-group>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n                  <!-- 时长 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>时长(按卷限时)</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">时长</div>\n                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>\n                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.duration\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                        <el-input-number v-model=\"paperForm.durationSeconds\" :min=\"0\" :max=\"59\" size=\"mini\" style=\"width: 85px;\"></el-input-number> 秒\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 及格分 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>及格分</b>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.passScore\" :min=\"0\" :max=\"paperForm.totalScore || 100\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>考试时间</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">考试时间</div>\n                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.enableTimeLimit\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间子项容器 -->\n                  <div class=\"block-expand\" v-if=\"paperForm.enableTimeLimit\">\n                    <!-- 考试开始时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试开始时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.startTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择开始时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 考试结束时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试结束时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.endTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择结束时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n\n                  </div>\n\n                  <!-- 提前交卷 -->\n                  <div class=\"setting-block\" v-if=\"paperForm.enableTimeLimit\">\n                    <div class=\"line block-header\">\n                      <b>提前交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch\n                          v-model=\"paperForm.allowEarlySubmit\"\n                          :active-value=\"1\"\n                          :inactive-value=\"0\"\n                        ></el-switch>\n                        <span v-if=\"paperForm.allowEarlySubmit\" style=\"margin-left: 10px;\">\n                          <el-input-number\n                            v-model=\"paperForm.earlySubmitTime\"\n                            :min=\"1\"\n                            :max=\"60\"\n                            size=\"mini\"\n                            style=\"width: 100px;\"\n                          ></el-input-number> 分钟\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示分值 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示分值</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showScore\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示题型 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示题型</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showType\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 迟到限制 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>迟到限制</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"late-limit-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">迟到限制</div>\n                            <div>设置迟到多少分钟后，禁止再进入考试。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.lateLimit\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分钟\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试中设置 -->\n              <el-collapse-item title=\"考试中\" name=\"during\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-edit-outline\" style=\"color: #409eff;\"></i>考试中\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n\n\n                  <!-- 全部答完才能交卷 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>全部答完才能交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.requireAllAnswered\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试后设置 -->\n              <el-collapse-item title=\"考试后\" name=\"after\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-finished\" style=\"color: #e6a23c;\"></i>考试后\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n                  <!-- 显示成绩 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示成绩</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showResult\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示对错 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示对错</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showCorrect\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示答案 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示答案</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnswer\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示解析 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示解析</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnalysis\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </el-form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 随机规则设置对话框 -->\n    <el-dialog\n      title=\"随机规则设置\"\n      :visible.sync=\"showRuleDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"新版规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFirstRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加一级规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ totalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ totalRuleScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"确定一级规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      按题库、按题型、按难度、按知识点抽题四选一\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"添加子规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      在每个上级规则基础上继续添加子规则\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"保存抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      各级规则设置完成后即可保存规则开始抽题\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"rules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in parentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"addSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}\n                          </div>\n                          <div class=\"rule-content\" v-if=\"childRule.type === 3\">\n                            <span>\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"rules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加一级规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showRuleDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleSaveRules\">保存规则</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 手动选题对话框 -->\n    <el-dialog\n      title=\"选择题目\"\n      :visible.sync=\"showManualSelectDialog\"\n      width=\"1200px\"\n      :close-on-click-modal=\"false\"\n      class=\"checked-question\"\n      top=\"15vh\"\n      :append-to-body=\"true\"\n      :z-index=\"3200\"\n    >\n      <div style=\"display: flex; height: 550px;\">\n        <!-- 左侧：题库列表 -->\n        <div style=\"width: 300px; padding-right: 10px;\">\n          <!-- 搜索区域 -->\n          <div style=\"padding: 5px; height: 42px; display: flex; gap: 10px;\">\n            <el-cascader\n              v-model=\"manualSelect.selectedCategory\"\n              :options=\"categoryOptions\"\n              :props=\"cascaderProps\"\n              placeholder=\"选择题库分类\"\n              size=\"small\"\n              style=\"width: 135px;\"\n              clearable\n              @change=\"searchQuestionBanks\"\n            ></el-cascader>\n\n            <el-input\n              v-model=\"manualSelect.bankSearchKeyword\"\n              placeholder=\"题库名称\"\n              size=\"small\"\n              style=\"width: 150px;\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchQuestionBanks\"></el-button>\n            </el-input>\n          </div>\n\n          <!-- 题库表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questionBanks\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @row-click=\"selectQuestionBank\"\n              highlight-current-row\n            >\n              <el-table-column type=\"index\" width=\"38\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"left\" header-align=\"center\"></el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding-top: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleBankSizeChange\"\n              @current-change=\"handleBankCurrentChange\"\n              :current-page=\"manualSelect.bankPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              :page-size=\"manualSelect.bankPagination.pageSize\"\n              :total=\"manualSelect.bankPagination.total\"\n              layout=\"prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 中间：题目列表 -->\n        <div style=\"width: 700px; padding: 0px 5px;\">\n          <!-- 筛选区域 -->\n          <div style=\"padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;\">\n            <el-select\n              v-model=\"manualSelect.questionType\"\n              placeholder=\"题型\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部题型\" value=\"\"></el-option>\n              <el-option label=\"单选题\" value=\"1\"></el-option>\n              <el-option label=\"多选题\" value=\"2\"></el-option>\n              <el-option label=\"判断题\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-select\n              v-model=\"manualSelect.difficulty\"\n              placeholder=\"难度\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部难度\" value=\"\"></el-option>\n              <el-option label=\"低\" value=\"1\"></el-option>\n              <el-option label=\"中\" value=\"2\"></el-option>\n              <el-option label=\"高\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-input\n              v-model=\"manualSelect.questionSearchKeyword\"\n              placeholder=\"搜索题目\"\n              size=\"small\"\n              style=\"width: 250px;\"\n            >\n              <template slot=\"append\">\n                <el-button @click=\"searchQuestions\" style=\"border-left: 1px solid #dcdfe6;\">搜索</el-button>\n                <el-button @click=\"resetQuestionSearch\" style=\"border-left: 1px solid #dcdfe6;\">重置</el-button>\n              </template>\n            </el-input>\n          </div>\n\n          <!-- 题目表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questions\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @selection-change=\"handleQuestionSelectionChange\"\n              :row-class-name=\"getQuestionRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"40\" :selectable=\"isQuestionSelectable\"></el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题干\" min-width=\"400\" header-align=\"center\" class-name=\"question-content-column\">\n                <template slot-scope=\"scope\">\n                  <div class=\"question-content-text\">\n                    {{ scope.row.questionContent }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionType\" label=\"题目类型\" width=\"78\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getQuestionTypeText(scope.row.questionType) }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"50\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getDifficultyText(scope.row.difficulty) }}\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleQuestionSizeChange\"\n              @current-change=\"handleQuestionCurrentChange\"\n              :current-page=\"manualSelect.questionPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 40, 50, 100]\"\n              :page-size=\"manualSelect.questionPagination.pageSize\"\n              :total=\"manualSelect.questionPagination.total\"\n              layout=\"sizes, prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 右侧：统计信息 -->\n        <div style=\"width: 150px; padding: 0px 5px;\">\n          <div style=\"padding: 7px 0px; height: 42px; font-size: 16px;\">试卷题型统计</div>\n          <div style=\"border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;\">\n            <!-- 显示已添加到试卷的题目统计 -->\n            <div v-for=\"(count, type) in fixedQuestionStats\" :key=\"type\" style=\"margin-bottom: 10px;\">\n              <div style=\"font-size: 14px; color: #606266;\">\n                {{ getQuestionTypeText(type) }}：{{ count }} 题\n              </div>\n            </div>\n\n            <!-- 显示当前选择的题目统计 -->\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;\">\n              <div style=\"font-size: 13px; color: #909399; margin-bottom: 8px;\">本次选择：</div>\n              <div v-for=\"(count, type) in manualSelect.selectedStats\" :key=\"'selected-' + type\" style=\"margin-bottom: 8px;\">\n                <div style=\"font-size: 13px; color: #409eff;\">\n                  {{ getQuestionTypeText(type) }}：{{ count }} 题\n                </div>\n              </div>\n            </div>\n          </div>\n          <div style=\"width: 140px; text-align: right; padding-right: 5px;\">\n            <div style=\"font-size: 14px; font-weight: bold;\">总题数：{{ fixedQuestions.length }} 题</div>\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"font-size: 12px; color: #409eff;\">\n              +{{ manualSelect.selectedQuestions.length }} 题\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showManualSelectDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"confirmManualSelect\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加规则对话框 -->\n    <el-dialog\n      :title=\"getDialogTitle()\"\n      :visible.sync=\"showAddRuleDialog\"\n      width=\"900px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3100\"\n    >\n      <div style=\"margin-bottom: -30px;\">\n        <el-form :model=\"ruleForm\" label-width=\"140px\">\n          <!-- 选择规则类型 -->\n          <el-form-item v-if=\"shouldShowRuleTypeSelection()\" label=\"选择规则类型\">\n            <el-radio-group v-model=\"ruleForm.ruleType\">\n              <el-radio\n                v-if=\"shouldShowRuleType(3)\"\n                :label=\"3\"\n                :disabled=\"shouldDisableRuleType(3)\"\n              >题库</el-radio>\n              <el-radio\n                v-if=\"shouldShowRuleType(1)\"\n                :label=\"1\"\n                :disabled=\"shouldDisableRuleType(1)\"\n              >题型</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 规则生成方式 -->\n          <el-form-item v-if=\"currentOperation !== 'addSub'\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                规则生成方式\n              </div>\n            </template>\n            <el-radio-group v-model=\"ruleForm.generateType\">\n              <el-radio label=\"divided\">分开设置</el-radio>\n              <el-radio label=\"one\">整体设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 已选择的题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"点击选项可取消选择。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                已选择的题库\n              </div>\n            </template>\n            <span v-if=\"ruleForm.selectedItems.length === 0\" class=\"ml20\">\n              暂无选择，请至少选择一项。\n            </span>\n            <div v-else>\n              <el-tag\n                v-for=\"item in selectedBanks\"\n                :key=\"item.bankId\"\n                closable\n                @close=\"removeSelectedBank(item.bankId)\"\n                type=\"info\"\n                size=\"medium\"\n                class=\"selected-bank-tag\"\n              >\n                {{ item.bankName }}\n              </el-tag>\n            </div>\n          </el-form-item>\n\n          <!-- 选择题型 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 1\" label=\"选择题型\">\n            <el-checkbox-group v-model=\"ruleForm.selectedQuestionTypes\">\n              <el-checkbox v-if=\"shouldShowQuestionType('1')\" label=\"1\">单选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('2')\" label=\"2\">多选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('3')\" label=\"3\">判断题</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n\n\n\n          <!-- 选择题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\" label=\"选择题库\">\n            <!-- 搜索区域 -->\n            <div class=\"clearfix\" style=\"margin-bottom: 6px;\">\n              <div style=\"float: right; display: flex; align-items: center;\">\n                <el-input\n                  v-model=\"searchKeyword\"\n                  placeholder=\"题库名称\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 8px;\"\n                  @keyup.enter.native=\"handleSearch\"\n                  clearable\n                >\n                  <el-button slot=\"append\" @click=\"handleSearch\">搜索</el-button>\n                </el-input>\n                <el-button size=\"small\" @click=\"handleReset\">重置</el-button>\n              </div>\n            </div>\n\n            <!-- 题库表格 -->\n            <el-table\n              ref=\"questionBankTable\"\n              :data=\"questionBanks\"\n              border\n              size=\"small\"\n              max-height=\"300\"\n              v-loading=\"questionBankLoading\"\n              @selection-change=\"handleSelectionChange\"\n              :row-class-name=\"getRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"40\" label=\"#\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" :selectable=\"isRowSelectable\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"questionCount\" label=\"题目数量\" width=\"80\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.questionCount || 0 }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分类\" width=\"150\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getCategoryName(scope.row.categoryId) }}\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <!-- 分页 -->\n            <div style=\"text-align: right; margin-top: 10px;\">\n              <el-pagination\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"queryParams.pageNum\"\n                :page-size=\"queryParams.pageSize\"\n                layout=\"total, prev, pager, next\"\n                :total=\"total\"\n                small\n                background\n              >\n              </el-pagination>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showAddRuleDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSaveRule\">保 存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 固定试卷随机抽题对话框 -->\n    <el-dialog\n      title=\"随机抽题规则设置\"\n      :visible.sync=\"showFixedRandomDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"随机抽题规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFixedRandomRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加抽题规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ fixedRandomTotalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ fixedRandomTotalScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"设置抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      选择题库和题型，设置抽题数量和分数\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"确认抽题\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      根据规则随机抽取题目并添加到试卷\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"fixedRandomRules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in fixedRandomRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <div class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮 -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"fixedRandomRules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加抽题规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showFixedRandomDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleConfirmFixedRandomSelect\">确认抽题</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n/* 表格垂直对齐优化 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {\n  vertical-align: middle;\n}\n\n/* 多选框垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n/* 序号列垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n/* 规则节点样式 */\n.rule-node-cascade {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.rule-node-cascade:hover {\n  border: 1px dashed #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n/* 左侧题库信息 */\n.rule-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.rule-type-label {\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n  min-width: 50px;\n}\n\n.rule-content {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 右侧操作区域 */\n.rule-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rule-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.control-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  white-space: nowrap;\n}\n\n.control-divider {\n  color: #999;\n  margin: 0 4px;\n}\n\n.total-score {\n  font-weight: 600;\n  color: #333;\n}\n\n.input-number-mini {\n  width: 110px !important;\n}\n\n.rule-actions {\n  display: flex;\n  gap: 2px;\n}\n\n.action-btn {\n  color: #409eff !important;\n  padding: 4px 6px !important;\n  margin-left: 0 !important;\n}\n\n.action-btn:hover {\n  background-color: #ecf5ff !important;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n/* 子规则样式 */\n.children-rules {\n  margin-top: 12px;\n  margin-left: 20px;\n  border-left: 2px dashed #e8e8e8;\n  padding-left: 20px;\n}\n\n.children_component {\n  margin-bottom: 8px !important;\n  border: 1px dashed #d9d9d9 !important;\n  background-color: #f9f9f9 !important;\n}\n\n.children_component:hover {\n  border: 1px dashed #409eff !important;\n  background-color: #ecf5ff !important;\n}\n\n.parent-rule {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 禁用的表格行样式 */\n::v-deep .disabled-row {\n  background-color: #f5f5f5 !important;\n  color: #c0c4cc !important;\n}\n\n::v-deep .disabled-row:hover {\n  background-color: #f5f5f5 !important;\n}\n\n::v-deep .disabled-row td {\n  color: #c0c4cc !important;\n}\n\n/* random-paper 规则显示样式 */\n.rule-display-container {\n  border: 1px dashed #d0d0d0;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #fafbfc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 题库容器悬停效果 */\n.rule-display-container:hover {\n  border-color: #409eff;\n  background-color: #f8fbff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.rule-display-container:hover .parent-rule-display {\n  background-color: #e3f2fd;\n  border-left-color: #1976d2;\n}\n\n.rule-display-container:hover .parent-rule-display .rule-label {\n  color: #1976d2;\n}\n\n.parent-rule-display {\n  font-size: 14px;\n  color: #303133;\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n  border-left: 4px solid #409eff;\n}\n\n.children-rules-display {\n  margin-left: 20px;\n}\n\n.child-rule-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px;\n  margin-bottom: 8px;\n  background-color: #ffffff;\n  border: 1px dashed #d0d0d0;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.child-rule-display:last-child {\n  margin-bottom: 0;\n}\n\n/* 子规则悬停效果 */\n.child-rule-display:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.child-rule-display:hover .rule-label {\n  color: #1976d2;\n}\n\n.child-rule-display:hover .rule-stats {\n  background-color: #e3f2fd;\n  border-color: #90caf9;\n}\n\n.child-rule-left {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.child-rule-right {\n  flex-shrink: 0;\n  margin-left: 20px;\n}\n\n.rule-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.rule-content {\n  color: #303133;\n}\n\n.rule-stats {\n  font-size: 13px;\n  color: #606266;\n  background-color: #f0f9ff;\n  padding: 4px 8px;\n  border-radius: 5px;\n  border: 1px solid #b3d8ff;\n  white-space: nowrap;\n}\n\n.rule-stats strong {\n  color: #409eff;\n  font-weight: 600;\n}\n\n/* 固定试卷样式 */\n.question-list {\n  margin-top: 20px;\n}\n\n.question-item {\n  margin-bottom: 12px;\n  background-color: #fafafa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.question-item:hover {\n  background-color: #f0f9ff;\n  border-color: #409eff;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 15px;\n}\n\n.question-header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.question-header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.question-number {\n  font-weight: bold;\n  color: #409eff;\n  margin-left: 10px;\n  margin-right: 10px;\n  min-width: 30px;\n}\n\n.question-content {\n  flex: 1;\n  color: #303133;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-right: 15px;\n}\n\n.question-details {\n  border-top: 1px solid #e4e7ed;\n  padding: 15px;\n  background-color: #ffffff;\n}\n\n.question-info {\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: inline-block;\n  margin-right: 20px;\n  font-size: 13px;\n  color: #606266;\n}\n\n.question-options {\n  margin-top: 10px;\n}\n\n.options-title {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.option-item.correct-option {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.option-key {\n  font-weight: bold;\n  margin-right: 8px;\n  min-width: 20px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n.el-button-group {\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.icon_size {\n  font-size: 14px;\n}\n\n/* 禁用题目行样式 */\n.disabled-question-row {\n  background-color: #f5f7fa !important;\n  color: #c0c4cc !important;\n}\n\n.disabled-question-row:hover {\n  background-color: #f5f7fa !important;\n}\n\n.disabled-question-row td {\n  color: #c0c4cc !important;\n}\n\n/* 题干内容左对齐样式 */\n.question-content-text {\n  max-height: 60px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: left !important;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n/* 强制题干列内容左对齐 */\n.el-table .question-content-column {\n  text-align: left !important;\n}\n\n.el-table .question-content-column .cell {\n  text-align: left !important;\n  padding-left: 10px !important;\n  padding-right: 10px !important;\n}\n\n/* 已选择题库标签样式 */\n.selected-bank-tag {\n  margin-right: 8px !important;\n  margin-bottom: 4px !important;\n  font-size: 13px !important;\n}\n\n/* 修复标签关闭按钮样式 */\n::v-deep .selected-bank-tag .el-tag__close {\n  color: #909399 !important;\n  font-size: 12px !important;\n  margin-left: 6px !important;\n  cursor: pointer !important;\n  border-radius: 50% !important;\n  width: 16px !important;\n  height: 16px !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  line-height: 1 !important;\n  vertical-align: middle !important;\n}\n\n::v-deep .selected-bank-tag .el-tag__close:hover {\n  background-color: #909399 !important;\n  color: #fff !important;\n}\n</style>\n\n<script>\nimport { listQuestionBank } from \"@/api/biz/questionBank\"\nimport { listCategory } from \"@/api/biz/category\"\nimport { getQuestionStatistics, listQuestion } from \"@/api/biz/question\"\n\nexport default {\n  name: \"PaperCreate\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    paperId: {\n      type: [String, Number],\n      default: null\n    }\n  },\n  data() {\n    return {\n      // 右侧面板状态\n      rightPanelCollapsed: false,\n      rightPanelWidth: 410,\n      \n      // 折叠面板激活项（accordion模式下为字符串）\n      activeCollapse: 'basic',\n      \n      // 试卷表单数据\n      paperForm: {\n        paperId: null,\n        paperName: '',\n        paperDesc: '',\n        paperType: 1, // 0: 固定试卷, 1: 随机试卷\n        coverImg: '',\n        totalScore: 100,\n        passScore: 60,\n        startTime: null,\n        endTime: null,\n        duration: 90, // 考试时长，分钟\n        lateLimit: 0, // 迟到限制，分钟\n        allowEarlySubmit: 0, // 是否允许提前交卷\n        earlySubmitTime: 40, // 提前交卷时间，分钟\n        showScore: 0, // 是否显示分值\n        showType: 0, // 是否显示题型\n        requireAllAnswered: 0, // 是否要求全部答完才能交卷\n        showResult: 0, // 是否显示成绩\n        showCorrect: 0, // 是否显示对错\n        showAnswer: 0, // 是否显示答案\n        showAnalysis: 0, // 是否显示解析\n        status: 0, // 状态：0未发布 1已发布\n        enableTimeLimit: 0, // 是否启用考试时间限制\n        durationSeconds: 0, // 时长秒数\n        createTime: null\n      },\n      \n      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）\n\n      // 固定试卷相关数据\n      fixedQuestions: [], // 固定试卷的题目列表\n      selectAll: false, // 全选状态\n      isExpanded: false, // 展开状态\n\n      // 手动选题对话框\n      showManualSelectDialog: false,\n      categoryOptions: [], // 分类选项数据\n      cascaderProps: {\n        value: 'id',\n        label: 'name',\n        children: 'children',\n        checkStrictly: false,\n        emitPath: false\n      },\n      manualSelect: {\n        selectedCategory: '', // 选择的题库目录\n        bankSearchKeyword: '', // 题库搜索关键词\n        questionBanks: [], // 题库列表\n        bankPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedBankId: null, // 选择的题库ID\n        questionType: '', // 题型筛选\n        difficulty: '', // 难度筛选\n        questionSearchKeyword: '', // 题目搜索关键词\n        questions: [], // 题目列表\n        questionPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedQuestions: [], // 选中的题目\n        selectedStats: {} // 选中题目的统计信息\n      },\n\n      // 固定试卷随机抽题对话框\n      showFixedRandomDialog: false,\n\n      // 固定试卷随机抽题相关数据\n      fixedRandomRules: [], // 临时规则列表，用于固定试卷随机抽题\n      fixedRandomForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n\n      // 随机规则对话框\n      showRuleDialog: false,\n      // 规则列表\n      rules: [],\n\n      // 添加规则对话框\n      showAddRuleDialog: false,\n      ruleForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n      // 当前操作状态\n      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则\n      editingRule: null, // 正在编辑的规则\n      parentRule: null, // 父规则（添加子规则时使用）\n      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量\n      questionBanks: [],\n      questionBankLoading: false,\n      categoryOptions: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n    }\n  },\n  computed: {\n    // 已选择的题库\n    selectedBanks() {\n      return this.questionBanks.filter(bank =>\n        this.ruleForm.selectedItems.includes(bank.bankId)\n      )\n    },\n\n    // 计算总题目数量\n    totalQuestions() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的题目数\n          rule.children.forEach(child => {\n            total += child.selectedCount || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的题目数\n          total += rule.selectedCount || 0\n        }\n      })\n      return total\n    },\n\n    // 计算总分数\n    totalRuleScore() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的总分\n          rule.children.forEach(child => {\n            total += child.totalScore || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的总分\n          total += rule.totalScore || 0\n        }\n      })\n      return total\n    },\n\n    // 获取父规则列表（只包含没有parentId的规则）\n    parentRules() {\n      return this.rules.filter(rule => !rule.parentId)\n    },\n\n    // 获取已使用的题库ID列表\n    usedBankIds() {\n      const usedIds = new Set()\n      this.rules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    },\n\n    // 计算已添加到试卷的题目统计\n    fixedQuestionStats() {\n      const stats = {}\n      this.fixedQuestions.forEach(question => {\n        const type = question.type\n        stats[type] = (stats[type] || 0) + 1\n      })\n      return stats\n    },\n\n    // 固定试卷随机抽题总题数\n    fixedRandomTotalQuestions() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.selectedCount || 0\n      })\n      return total\n    },\n\n    // 固定试卷随机抽题总分数\n    fixedRandomTotalScore() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.totalScore || 0\n      })\n      return total\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val && this.paperId) {\n        this.loadPaperData()\n      }\n    },\n\n    // 监听题型选择变化\n    'ruleForm.selectedQuestionTypes': {\n      async handler(newVal) {\n        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {\n          await this.updateQuestionTypeCount()\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 返回试卷列表 */\n    handleBack() {\n      this.$emit('close')\n    },\n    \n    /** 切换右侧面板 */\n    toggleRightPanel() {\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\n      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400\n    },\n    \n    /** 考试入口 */\n    handleExamEntry() {\n      this.$message.info('考试入口功能开发中...')\n    },\n    \n    /** 设置考试页面 */\n    handlePageSetting() {\n      this.$message.info('设置考试页面功能开发中...')\n    },\n    \n    /** 发布试卷 */\n    handlePublish() {\n      this.$message.info('发布试卷功能开发中...')\n    },\n    \n    /** 邀请考生 */\n    handleInviteStudents() {\n      this.$message.info('邀请考生功能开发中...')\n    },\n    \n    /** 已邀请列表 */\n    handleInviteList() {\n      this.$message.info('已邀请列表功能开发中...')\n    },\n    \n\n    \n    /** 添加规则（随机试卷） */\n    handleAddRule() {\n      this.showRuleDialog = true\n    },\n\n    /** 编辑规则（打开规则编辑界面） */\n    handleEditRules() {\n      this.showRuleDialog = true\n    },\n\n    /** 添加一级规则 */\n    handleAddFirstRule() {\n      this.currentOperation = 'add'\n      this.editingRule = null\n\n      // 如果已有规则，限制只能选择相同类型\n      if (this.rules.length > 0) {\n        this.ruleForm.ruleType = this.rules[0].type\n      } else {\n        this.ruleForm.ruleType = 3 // 默认题库\n      }\n\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 加载题库列表 */\n    loadQuestionBanks() {\n      this.questionBankLoading = true\n      // 同时加载题库和分类数据\n      Promise.all([\n        listQuestionBank(this.queryParams),\n        listCategory({ pageSize: 1000 })\n      ]).then(([bankResponse, categoryResponse]) => {\n        const questionBanks = bankResponse.rows || []\n        this.total = bankResponse.total || 0\n        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []\n\n        // 为每个题库获取题目统计\n        const statisticsPromises = questionBanks.map(bank =>\n          getQuestionStatistics(bank.bankId).then(stats => {\n            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0\n            return bank\n          }).catch(() => {\n            bank.questionCount = 0\n            return bank\n          })\n        )\n\n        Promise.all(statisticsPromises).then(banksWithStats => {\n          this.questionBanks = banksWithStats\n          this.questionBankLoading = false\n        })\n      }).catch(() => {\n        this.questionBankLoading = false\n      })\n    },\n\n    /** 保存规则 */\n    handleSaveRules() {\n      this.$message.info('保存规则功能开发中...')\n      this.showRuleDialog = false\n    },\n\n    /** 保存单个规则 */\n    async handleSaveRule() {\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {\n        this.$message.warning('请至少选择一个题库')\n        return\n      }\n      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {\n        this.$message.warning('请至少选择一个题型')\n        return\n      }\n\n      // 题型规则验证：检查是否有可用题目\n      if (this.ruleForm.ruleType === 1) {\n        if (this.currentQuestionTypeCount === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')\n          return\n        }\n      }\n\n      if (this.currentOperation === 'edit') {\n        // 编辑现有规则\n        this.updateExistingRule()\n      } else if (this.currentOperation === 'addFixedRandom') {\n        // 添加固定试卷随机抽题规则\n        this.addFixedRandomRule()\n      } else {\n        // 添加新规则（包括添加子规则）\n        this.addNewRule()\n      }\n\n      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')\n      this.showAddRuleDialog = false\n\n      // 重置表单\n      this.resetRuleForm()\n    },\n\n    /** 更新现有规则 */\n    updateExistingRule() {\n      const rule = this.editingRule\n      rule.type = this.ruleForm.ruleType\n      rule.generateType = this.ruleForm.generateType\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n\n        // 重新计算总分，确保选取数量不超过最大题目数\n        if (rule.selectedCount > rule.maxQuestions) {\n          rule.selectedCount = rule.maxQuestions\n        }\n        rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n    },\n\n    /** 添加新规则 */\n    addNewRule() {\n      // 如果是添加子规则，按原逻辑处理\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        this.addSingleRule()\n        return\n      }\n\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index, // 确保每个规则有唯一ID\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 0.5,\n            totalScore: 0.5,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateRuleScore(rule)\n          this.rules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleRule()\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    addFixedRandomRule() {\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index,\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 1,\n            totalScore: 1,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateFixedRandomRuleScore(rule)\n          this.fixedRandomRules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleFixedRandomRule()\n      }\n    },\n\n    /** 添加单个固定试卷随机抽题规则 */\n    addSingleFixedRandomRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      }\n\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加单个规则 */\n    addSingleRule() {\n      const rule = {\n        id: Date.now(), // 临时ID\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1, // 默认选取1题\n        scorePerQuestion: 0.5, // 默认每题0.5分\n        totalScore: 0.5, // 默认总分0.5分\n        maxQuestions: 0 // 最大题目数\n      }\n\n      // 如果是添加子规则\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        rule.parentId = this.parentRule.id\n\n        // 确保父规则有children数组\n        if (!this.parentRule.children) {\n          this.$set(this.parentRule, 'children', [])\n        }\n\n        // 添加到父规则的children中\n        this.parentRule.children.push(rule)\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n\n      // 只有父规则才添加到主规则列表\n      if (this.currentOperation !== 'addSub') {\n        this.rules.push(rule)\n      }\n\n      // 更新规则分数\n      this.updateRuleScore(rule)\n    },\n\n    /** 重置规则表单 */\n    resetRuleForm() {\n      this.ruleForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n      this.currentOperation = 'add'\n      this.editingRule = null\n      this.parentRule = null\n      this.currentQuestionTypeCount = 0\n    },\n\n    /** 搜索题库 */\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = this.searchKeyword || undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 重置搜索 */\n    handleReset() {\n      this.searchKeyword = ''\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 表格选择变化 */\n    handleSelectionChange(selection) {\n      this.ruleForm.selectedItems = selection.map(item => item.bankId)\n    },\n\n    /** 移除已选择的题库 */\n    removeSelectedBank(bankId) {\n      const index = this.ruleForm.selectedItems.indexOf(bankId)\n      if (index > -1) {\n        this.ruleForm.selectedItems.splice(index, 1)\n      }\n\n      // 同步取消表格中的勾选状态\n      this.$nextTick(() => {\n        const table = this.$refs.questionBankTable\n        if (table) {\n          // 找到对应的行数据\n          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)\n          if (rowToDeselect) {\n            table.toggleRowSelection(rowToDeselect, false)\n          }\n        }\n      })\n    },\n\n    /** 分页变化 */\n    handleCurrentChange(page) {\n      this.queryParams.pageNum = page\n      this.loadQuestionBanks()\n    },\n\n    /** 根据分类ID获取分类名称 */\n    getCategoryName(categoryId) {\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\n      return category ? category.name : '未分类'\n    },\n\n    /** 在分类数据中查找分类（支持扁平和树形结构） */\n    findCategoryById(categories, id) {\n      // 创建扁平化的分类列表\n      const flatCategories = this.flattenCategories(categories)\n\n      // 在扁平化列表中查找\n      const category = flatCategories.find(cat => cat.id === id)\n      return category || null\n    },\n\n    /** 将树形分类数据扁平化 */\n    flattenCategories(categories) {\n      let result = []\n\n      function flatten(cats) {\n        for (const cat of cats) {\n          result.push(cat)\n          if (cat.children && cat.children.length > 0) {\n            flatten(cat.children)\n          }\n        }\n      }\n\n      flatten(categories)\n      return result\n    },\n\n    /** 更新规则分数 */\n    updateRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 获取题型文本 */\n    getQuestionTypeText(questionTypes) {\n      const questionTypeMap = {\n        '1': '单选题', '2': '多选题', '3': '判断题',\n        1: '单选题', 2: '多选题', 3: '判断题'\n      }\n\n      // 如果是数组，处理多个题型\n      if (Array.isArray(questionTypes)) {\n        return questionTypes.map(t => questionTypeMap[t]).join('、')\n      }\n\n      // 如果是单个值，直接返回对应文本\n      return questionTypeMap[questionTypes] || '未知'\n    },\n\n\n\n    /** 获取子规则标签 */\n    getChildRuleLabel(childRule) {\n      if (childRule.type === 3) {\n        return '题库：'\n      } else if (childRule.type === 1) {\n        // 题型子规则只显示具体的题型名称，不显示\"题型：\"前缀\n        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return questionTypeMap[childRule.selectedQuestionTypes[0]]\n        } else {\n          return '题型'\n        }\n      }\n      return '规则：'\n    },\n\n    /** 获取规则类型标签（用于random-paper显示） */\n    getRuleTypeLabel(rule) {\n      if (rule.type === 3) {\n        return '题库：'\n      } else if (rule.type === 1) {\n        return '题型：'\n      }\n      return '规则：'\n    },\n\n    /** 获取规则内容（用于random-paper显示） */\n    getRuleContent(rule) {\n      if (rule.type === 3) {\n        // 题库规则显示题库名称\n        if (rule.selectedBanks && rule.selectedBanks.length > 0) {\n          return rule.selectedBanks.map(bank => bank.bankName).join('、')\n        }\n        return '未选择题库'\n      } else if (rule.type === 1) {\n        // 题型规则显示题型名称\n        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')\n        }\n        return '未选择题型'\n      }\n      return '未配置'\n    },\n\n    /** 添加子规则 */\n    addSubRule(rule) {\n      this.currentOperation = 'addSub'\n      this.parentRule = rule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n\n      if (rule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 编辑规则 */\n    editRule(rule) {\n      this.currentOperation = 'edit'\n      this.editingRule = rule\n      this.ruleForm.ruleType = rule.type\n      this.ruleForm.generateType = rule.generateType\n\n      if (rule.type === 3) {\n        // 题库规则\n        this.ruleForm.selectedItems = [...rule.selectedItems]\n      } else if (rule.type === 1) {\n        // 题型规则\n        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]\n        // 编辑题型规则时，更新题目数量\n        this.$nextTick(() => {\n          this.updateQuestionTypeCount()\n        })\n      }\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 删除规则 */\n    deleteRule(rule) {\n      this.$confirm('确定要删除这条规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = this.rules.findIndex(r => r.id === rule.id)\n        if (index > -1) {\n          this.rules.splice(index, 1)\n          this.$message.success('规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 删除子规则 */\n    deleteChildRule(parentRule, childRule) {\n      this.$confirm('确定要删除这条子规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = parentRule.children.findIndex(child => child.id === childRule.id)\n        if (index > -1) {\n          parentRule.children.splice(index, 1)\n          this.$message.success('子规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 获取对话框标题 */\n    getDialogTitle() {\n      switch (this.currentOperation) {\n        case 'add':\n          return '添加规则'\n        case 'edit':\n          return '编辑规则'\n        case 'addSub':\n          return '添加子规则'\n        default:\n          return '添加规则'\n      }\n    },\n\n    /** 是否显示规则类型选择 */\n    shouldShowRuleTypeSelection() {\n      // 所有操作都显示规则类型选择\n      return true\n    },\n\n    /** 是否显示规则类型选项 */\n    shouldShowRuleType(ruleType) {\n      // 编辑时只显示当前规则的类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type === ruleType\n      }\n\n      // 添加子规则时的逻辑\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        // 题型规则的子规则只能是题库规则\n        if (this.parentRule.type === 1) {\n          return ruleType === 3\n        }\n        // 题库规则的子规则只能是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 添加一级规则时的逻辑\n      if (this.currentOperation === 'add') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 其他情况显示所有类型\n      return true\n    },\n\n    /** 是否显示特定题型选项 */\n    shouldShowQuestionType(questionType) {\n      // 如果不是添加子规则，显示所有题型\n      if (this.currentOperation !== 'addSub' || !this.parentRule) {\n        return true\n      }\n\n      // 获取父规则下已有的题型子规则中选择的题型\n      const existingQuestionTypes = []\n      if (this.parentRule.children) {\n        this.parentRule.children.forEach(child => {\n          if (child.type === 1 && child.selectedQuestionTypes) {\n            existingQuestionTypes.push(...child.selectedQuestionTypes)\n          }\n        })\n      }\n\n      // 如果该题型已经被选择过，则隐藏\n      return !existingQuestionTypes.includes(questionType)\n    },\n\n    /** 判断表格行是否可选择 */\n    isRowSelectable(row) {\n      // 如果是编辑操作，允许选择\n      if (this.currentOperation === 'edit') {\n        return true\n      }\n\n      // 如果是添加子规则，允许选择（子规则不涉及题库选择）\n      if (this.currentOperation === 'addSub') {\n        return true\n      }\n\n      // 如果是添加一级规则，检查题库是否已被使用\n      return !this.usedBankIds.includes(row.bankId)\n    },\n\n    /** 获取表格行的样式类名 */\n    getRowClassName({ row }) {\n      // 如果题库已被使用且不是编辑操作，添加禁用样式\n      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {\n        return 'disabled-row'\n      }\n      return ''\n    },\n\n    /** 更新题型题目数量 */\n    async updateQuestionTypeCount() {\n      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {\n        this.currentQuestionTypeCount = 0\n        return\n      }\n\n      // 获取题库信息\n      let selectedBanks = []\n      if (this.currentOperation === 'addSub' && this.parentRule && this.parentRule.type === 3) {\n        // 添加子规则时，使用父规则的题库\n        selectedBanks = this.parentRule.selectedBanks || []\n      } else {\n        // 其他情况使用当前选择的题库\n        selectedBanks = this.selectedBanks\n      }\n\n      if (!selectedBanks.length) {\n        this.currentQuestionTypeCount = 0\n        this.$message.warning('请先选择题库')\n        return\n      }\n\n      try {\n        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)\n        this.currentQuestionTypeCount = count\n\n        if (count === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目')\n        }\n      } catch (error) {\n        console.error('查询题目数量失败:', error)\n        this.currentQuestionTypeCount = 0\n        this.$message.error('查询题目数量失败')\n      }\n    },\n\n    /** 根据题库和题型查询题目数量 */\n    async getQuestionCountByType(banks, questionTypes) {\n      if (!banks.length || !questionTypes.length) {\n        return 0\n      }\n\n      let totalCount = 0\n\n      // 遍历每个题库，查询题目统计\n      for (const bank of banks) {\n        try {\n          // 使用已导入的API方法\n          const response = await getQuestionStatistics(bank.bankId)\n          console.log(`题库${bank.bankId}统计数据:`, response)\n\n          if (response.code === 200 && response.data) {\n            const statistics = response.data\n\n            // 根据选择的题型累加数量\n            questionTypes.forEach(type => {\n              switch (type) {\n                case '1': // 单选题\n                  totalCount += statistics.singleChoice || 0\n                  break\n                case '2': // 多选题\n                  totalCount += statistics.multipleChoice || 0\n                  break\n                case '3': // 判断题\n                  totalCount += statistics.judgment || 0\n                  break\n              }\n            })\n          }\n        } catch (error) {\n          console.error(`查询题库${bank.bankId}统计信息失败:`, error)\n        }\n      }\n\n      console.log(`总题目数量: ${totalCount}`)\n      return totalCount\n    },\n\n    /** 是否禁用规则类型 */\n    shouldDisableRuleType(ruleType) {\n      // 编辑时不能更改类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type !== ruleType\n      }\n\n      // 添加一级规则时，只能选择题库，禁用其他类型\n      if (this.currentOperation === 'add') {\n        return ruleType !== 3\n      }\n\n      return false\n    },\n    \n    /** 全选/取消全选 */\n    handleSelectAll() {\n      this.fixedQuestions.forEach(question => {\n        question.selected = this.selectAll\n      })\n    },\n\n    /** 删除选中题目 */\n    handleDeleteSelected() {\n      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)\n      if (selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)\n        this.selectAll = false\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 手动选题 */\n    handleManualSelect() {\n      this.showManualSelectDialog = true\n      this.initManualSelectData()\n    },\n\n    /** 初始化手动选题数据 */\n    initManualSelectData() {\n      // 重置数据\n      this.manualSelect.selectedCategory = ''\n      this.manualSelect.bankSearchKeyword = ''\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.manualSelect.selectedQuestions = []\n      this.manualSelect.selectedStats = {}\n\n      // 加载分类数据\n      this.loadCategoryTree()\n\n      // 加载题库列表\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 加载分类树数据 */\n    loadCategoryTree() {\n      listCategory({ pageSize: 1000 }).then(response => {\n        const categories = response.rows || []\n        this.categoryOptions = this.buildCategoryTree(categories)\n      }).catch(error => {\n        console.error('加载分类数据失败:', error)\n        this.categoryOptions = []\n      })\n    },\n\n    /** 构建分类树 */\n    buildCategoryTree(categories) {\n      const map = {}\n\n      // 先将所有分类放入map中\n      categories.forEach(category => {\n        map[category.id] = { ...category, children: [] }\n      })\n\n      // 构建完整的树形结构\n      const result = []\n      categories.forEach(category => {\n        if (category.parentId === 0) {\n          // 顶级分类\n          result.push(map[category.id])\n        } else {\n          // 子分类\n          if (map[category.parentId]) {\n            map[category.parentId].children.push(map[category.id])\n          }\n        }\n      })\n\n      // 清理空的children数组\n      const cleanEmptyChildren = (node) => {\n        if (node.children && node.children.length === 0) {\n          delete node.children\n        } else if (node.children && node.children.length > 0) {\n          node.children.forEach(child => cleanEmptyChildren(child))\n        }\n      }\n\n      // 清理所有节点的空children\n      result.forEach(node => cleanEmptyChildren(node))\n\n      return result\n    },\n\n    /** 获取所有子分类ID */\n    getAllChildCategoryIds(categoryId, categories) {\n      const result = [categoryId]\n\n      const findChildren = (parentId) => {\n        categories.forEach(category => {\n          if (category.parentId === parentId) {\n            result.push(category.id)\n            findChildren(category.id)\n          }\n        })\n      }\n\n      findChildren(categoryId)\n      return result\n    },\n\n    /** 加载手动选题的题库列表 */\n    loadManualSelectQuestionBanks() {\n      let categoryIds = []\n\n      // 如果选择了分类，获取该分类及其所有子分类的ID\n      if (this.manualSelect.selectedCategory) {\n        // 获取原始分类数据（包含所有层级）\n        listCategory({ pageSize: 1000 }).then(response => {\n          const allCategories = response.rows || []\n          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)\n\n          // 执行多次查询，因为后端不支持IN查询\n          this.loadQuestionBanksByCategories(categoryIds)\n        }).catch(error => {\n          console.error('加载分类数据失败:', error)\n          this.loadQuestionBanksByCategories([])\n        })\n      } else {\n        // 没有选择分类，加载所有题库\n        this.loadQuestionBanksByCategories([])\n      }\n    },\n\n    /** 根据分类ID列表加载题库 */\n    loadQuestionBanksByCategories(categoryIds) {\n      const queryParams = {\n        pageNum: this.manualSelect.bankPagination.pageNum,\n        pageSize: this.manualSelect.bankPagination.pageSize,\n        bankName: this.manualSelect.bankSearchKeyword || undefined\n      }\n\n      if (categoryIds.length > 0) {\n        // 如果有分类筛选，需要合并多个分类的结果\n        const promises = categoryIds.map(categoryId => {\n          return listQuestionBank({ ...queryParams, categoryId })\n        })\n\n        Promise.all(promises).then(responses => {\n          const allBanks = []\n          let totalCount = 0\n\n          responses.forEach(response => {\n            if (response.rows) {\n              allBanks.push(...response.rows)\n              totalCount += response.total || 0\n            }\n          })\n\n          // 去重（根据bankId）\n          const uniqueBanks = allBanks.filter((bank, index, self) =>\n            index === self.findIndex(b => b.bankId === bank.bankId)\n          )\n\n          this.manualSelect.questionBanks = uniqueBanks\n          this.manualSelect.bankPagination.total = uniqueBanks.length\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      } else {\n        // 没有分类筛选，直接查询\n        listQuestionBank(queryParams).then(response => {\n          this.manualSelect.questionBanks = response.rows || []\n          this.manualSelect.bankPagination.total = response.total || 0\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      }\n    },\n\n    /** 搜索题库 */\n    searchQuestionBanks() {\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 选择题库 */\n    selectQuestionBank(row) {\n      this.manualSelect.selectedBankId = row.bankId\n      this.loadQuestions()\n    },\n\n    /** 加载题目列表 */\n    loadQuestions() {\n      if (!this.manualSelect.selectedBankId) {\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n        return\n      }\n\n      const queryParams = {\n        pageNum: this.manualSelect.questionPagination.pageNum,\n        pageSize: this.manualSelect.questionPagination.pageSize,\n        bankId: this.manualSelect.selectedBankId,\n        questionType: this.manualSelect.questionType || undefined,\n        difficulty: this.manualSelect.difficulty || undefined,\n        questionContent: this.manualSelect.questionSearchKeyword || undefined\n      }\n\n      listQuestion(queryParams).then(response => {\n        this.manualSelect.questions = response.rows || []\n        this.manualSelect.questionPagination.total = response.total || 0\n      }).catch(error => {\n        console.error('加载题目列表失败:', error)\n        this.$message.error('加载题目列表失败')\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n      })\n    },\n\n    /** 搜索题目 */\n    searchQuestions() {\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 重置题目搜索 */\n    resetQuestionSearch() {\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.searchQuestions()\n    },\n\n    /** 随机抽题 */\n    handleRandomSelect() {\n      // 为固定试卷打开随机规则设置对话框\n      this.showFixedRandomDialog = true\n      this.resetFixedRandomForm()\n      this.loadQuestionBanks()\n    },\n\n    /** 重置固定试卷随机抽题表单 */\n    resetFixedRandomForm() {\n      this.fixedRandomRules = []\n      this.fixedRandomForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    handleAddFixedRandomRule() {\n      this.currentOperation = 'addFixedRandom'\n      this.editingRule = null\n      this.ruleForm.ruleType = 3 // 默认题库\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 更新固定试卷随机抽题规则分数 */\n    updateFixedRandomRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 删除固定试卷随机抽题规则 */\n    deleteFixedRandomRule(rule) {\n      const index = this.fixedRandomRules.findIndex(r => r.id === rule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 确认固定试卷随机抽题 */\n    async handleConfirmFixedRandomSelect() {\n      if (this.fixedRandomRules.length === 0) {\n        this.$message.warning('请先添加抽题规则')\n        return\n      }\n\n      try {\n        // 根据规则随机抽取题目\n        const selectedQuestions = await this.extractQuestionsFromRules(this.fixedRandomRules)\n\n        if (selectedQuestions.length === 0) {\n          this.$message.warning('根据当前规则未能抽取到题目')\n          return\n        }\n\n        // 将抽取的题目添加到固定试卷中\n        selectedQuestions.forEach((question, index) => {\n          this.fixedQuestions.push({\n            id: Date.now() + index,\n            questionId: question.questionId,\n            content: question.questionContent,\n            type: question.questionType,\n            difficulty: question.difficulty,\n            options: question.options,\n            score: question.score || 1, // 使用规则中设置的分数\n            selected: false,\n            expanded: false\n          })\n        })\n\n        this.showFixedRandomDialog = false\n        this.$message.success(`成功随机抽取并添加 ${selectedQuestions.length} 道题目`)\n      } catch (error) {\n        console.error('随机抽题失败:', error)\n        this.$message.error('随机抽题失败，请重试')\n      }\n    },\n\n    /** 根据规则抽取题目 */\n    async extractQuestionsFromRules(rules) {\n      const allQuestions = []\n\n      for (const rule of rules) {\n        try {\n          let questions = []\n\n          if (rule.type === 3) {\n            // 题库规则：从选中的题库中抽取题目\n            for (const bank of rule.selectedBanks) {\n              const queryParams = {\n                bankId: bank.bankId,\n                pageNum: 1,\n                pageSize: 1000 // 获取足够多的题目用于随机选择\n              }\n\n              const response = await listQuestion(queryParams)\n              if (response.rows && response.rows.length > 0) {\n                questions = questions.concat(response.rows)\n              }\n            }\n          } else if (rule.type === 1) {\n            // 题型规则：从所有题库中按题型抽取题目\n            for (const questionType of rule.selectedQuestionTypes) {\n              const queryParams = {\n                questionType: questionType,\n                pageNum: 1,\n                pageSize: 1000\n              }\n\n              const response = await listQuestion(queryParams)\n              if (response.rows && response.rows.length > 0) {\n                questions = questions.concat(response.rows)\n              }\n            }\n          }\n\n          // 随机选择指定数量的题目\n          if (questions.length > 0) {\n            const shuffled = this.shuffleArray([...questions])\n            const selectedCount = Math.min(rule.selectedCount, shuffled.length)\n            const selectedQuestions = shuffled.slice(0, selectedCount)\n\n            // 为每个题目设置分数\n            selectedQuestions.forEach(question => {\n              question.score = rule.scorePerQuestion\n            })\n\n            allQuestions.push(...selectedQuestions)\n          }\n        } catch (error) {\n          console.error('抽取题目失败:', error)\n        }\n      }\n\n      return allQuestions\n    },\n\n    /** 数组随机排序 */\n    shuffleArray(array) {\n      for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]]\n      }\n      return array\n    },\n\n    /** 排序 */\n    handleSort() {\n      this.$message.info('排序功能开发中...')\n    },\n\n    /** 批量设置分数 */\n    handleBatchSetScore() {\n      this.$message.info('批量设置分数功能开发中...')\n    },\n\n    /** 导出 */\n    handleExport() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    /** 展开/收起 */\n    handleToggleExpand() {\n      this.isExpanded = !this.isExpanded\n      // 批量设置所有题目的展开状态\n      this.fixedQuestions.forEach(question => {\n        question.expanded = this.isExpanded\n      })\n      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)\n    },\n\n    /** 题目选择变化 */\n    handleQuestionSelectionChange(selection) {\n      this.manualSelect.selectedQuestions = selection\n      this.updateSelectedStats()\n    },\n\n    /** 更新选中题目统计 */\n    updateSelectedStats() {\n      const stats = {}\n      this.manualSelect.selectedQuestions.forEach(question => {\n        const type = question.questionType\n        stats[type] = (stats[type] || 0) + 1\n      })\n      this.manualSelect.selectedStats = stats\n    },\n\n    /** 确认手动选题 */\n    confirmManualSelect() {\n      if (this.manualSelect.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择题目')\n        return\n      }\n\n      // 将选中的题目添加到固定试卷中\n      this.manualSelect.selectedQuestions.forEach((question, index) => {\n        this.fixedQuestions.push({\n          id: Date.now() + index,\n          questionId: question.questionId,\n          content: question.questionContent,\n          type: question.questionType,\n          difficulty: question.difficulty,\n          options: question.options, // 保存选项信息\n          score: 1, // 默认1分\n          selected: false,\n          expanded: false // 默认收起状态\n        })\n      })\n\n      this.showManualSelectDialog = false\n      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)\n    },\n\n    /** 题库分页大小变化 */\n    handleBankSizeChange(val) {\n      this.manualSelect.bankPagination.pageSize = val\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题库当前页变化 */\n    handleBankCurrentChange(val) {\n      this.manualSelect.bankPagination.pageNum = val\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题目分页大小变化 */\n    handleQuestionSizeChange(val) {\n      this.manualSelect.questionPagination.pageSize = val\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 题目当前页变化 */\n    handleQuestionCurrentChange(val) {\n      this.manualSelect.questionPagination.pageNum = val\n      this.loadQuestions()\n    },\n\n    /** 获取难度文本 */\n    getDifficultyText(difficulty) {\n      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }\n      return difficultyMap[difficulty] || '未知'\n    },\n\n    /** 更新题目分数 */\n    updateQuestionScore(question) {\n      // 分数更新后可以触发总分重新计算\n      this.$forceUpdate()\n    },\n\n    /** 切换题目展开/收起状态 */\n    toggleQuestionExpand(question) {\n      question.expanded = !question.expanded\n    },\n\n    /** 删除单个题目 */\n    deleteQuestion(question, index) {\n      this.$confirm('确定删除这道题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions.splice(index, 1)\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 解析选项JSON字符串 */\n    parseOptions(optionsStr) {\n      try {\n        if (typeof optionsStr === 'string') {\n          return JSON.parse(optionsStr)\n        }\n        return optionsStr || []\n      } catch (error) {\n        console.error('解析选项失败:', error)\n        return []\n      }\n    },\n\n    /** 判断题目是否可选择 */\n    isQuestionSelectable(row) {\n      // 检查题目是否已经添加到试卷中\n      return !this.fixedQuestions.some(question => question.questionId === row.questionId)\n    },\n\n    /** 获取题目行的样式类名 */\n    getQuestionRowClassName({ row }) {\n      // 如果题目已被添加，添加禁用样式\n      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {\n        return 'disabled-question-row'\n      }\n      return ''\n    },\n    \n    /** 上传前验证 */\n    beforeUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0\n      const isLt2M = file.size / 1024 / 1024 < 2\n      \n      if (!isImage) {\n        this.$message.error('只能上传图片文件!')\n        return false\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!')\n        return false\n      }\n      return true\n    },\n    \n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    \n    /** 加载试卷数据 */\n    loadPaperData() {\n      if (this.paperId) {\n        // TODO: 调用API加载试卷数据\n        console.log('加载试卷数据:', this.paperId)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.exam-editor {\n  display: flex;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.exam-editor-left {\n  flex: 1;\n  padding: 20px;\n  padding-right: 420px; /* 为右侧固定面板留出空间 */\n  overflow-y: auto;\n  transition: padding-right 0.3s ease;\n  background-color: #FFF;\n}\n\n.exam-editor-left.collapsed {\n  padding-right: 70px; /* 右侧面板收起时的空间 */\n}\n\n.exam-editor-right {\n  background: #fff;\n  border-left: 1px solid #e4e7ed;\n  position: fixed;\n  right: 0;\n  top: 0;\n  height: 100vh;\n  transition: width 0.3s ease;\n  z-index: 100;\n}\n\n.collapse-button {\n  position: absolute;\n  left: -15px;\n  top: 20px;\n  width: 30px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e4e7ed;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  font-size: 16px;\n  color: #606266;\n}\n\n.collapse-button:hover {\n  background: #f5f7fa;\n  color: #409eff;\n}\n\n.editPaper_main_top {\n  background: #fff;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.editPaper_main_top .el-button-group {\n  margin-right: 15px;\n  display: inline-block;\n}\n\n.clear_both {\n  clear: both;\n}\n\n.subject_main-wrapper {\n  max-width: 100%;\n}\n\n/* 左侧卡片模块悬停效果 */\n.subject_main-wrapper .el-card {\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.subject_main-wrapper .el-card:hover {\n  border: 2px dashed #409eff;\n  background-color: #fafbff;\n}\n\n.subtitle-title {\n  position: relative;\n}\n\n.slgfont {\n  font-size: 24px;\n  color: #303133;\n}\n\n.paper-count {\n  margin-top: 10px;\n}\n\n.paper-count .el-tag {\n  margin-left: 8px;\n}\n\n\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.tac {\n  text-align: center;\n}\n\n.pd5 {\n  padding: 5px;\n}\n\n.editor-header {\n  padding: 20px;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.editor-header--big {\n  font-size: 20px;\n}\n\n.close-button {\n  font-size: 20px;\n  color: #909399;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  color: #f56c6c;\n  background-color: #fef0f0;\n}\n\n.main {\n  height: calc(100vh - 80px);\n  overflow-y: auto;\n  padding: 0;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.main-collapse {\n  border: none;\n}\n\n.main-collapse .el-collapse-item__header {\n  padding: 0 20px;\n  height: 60px;\n  line-height: 60px;\n  background: #f8f8f8;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* 使用深度选择器覆盖Element UI默认样式 */\n.main-collapse >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n.editor-collapse-item >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n/* Vue 3 深度选择器语法 */\n.main-collapse :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.editor-collapse-item :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.main-module {\n  padding: 20px;\n}\n\n.setting-block {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.setting-block:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n\n/* 二级设置块容器 */\n.block-expand {\n  margin-top: 10px;\n  padding-left: 20px;\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 二级设置块样式 */\n.sub-setting {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #e8e8e8;\n}\n\n.sub-setting:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n}\n\n.line {\n  display: flex;\n  align-items: flex-start;\n}\n\n.block-header {\n  justify-content: space-between;\n}\n\n.block-header b {\n  font-size: 14px;\n  color: #303133;\n  min-width: 93px;\n  line-height: 32px;\n}\n\n.input-area {\n  flex: 1;\n  margin-left: 20px;\n}\n\n.input--number {\n  width: 120px;\n}\n\n.dpib {\n  display: inline-block;\n}\n\n.avatar-uploader {\n  border: none !important;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 120px;\n  height: 80px;\n  box-sizing: border-box;\n}\n\n\n\n/* 使用伪元素创建统一的虚线边框 */\n.avatar-uploader::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.avatar-uploader:hover::before {\n  border-color: #409eff;\n}\n\n/* 确保Element UI组件没有边框 */\n.avatar-uploader .el-upload {\n  border: none !important;\n  width: 100%;\n  height: 100%;\n  background: transparent !important;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 120px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n}\n\n.avatar {\n  width: 120px;\n  height: 80px;\n  display: block;\n  object-fit: cover;\n}\n\n.image_area {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bold {\n  font-weight: bold;\n}\n\n.mr10 {\n  margin-right: 10px;\n}\n\n/* 折叠面板标题样式 */\n.collapse-title {\n  margin-left: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.collapse-title i {\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.el-popover__reference {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n}\n\n.el-popover__title {\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n/* 设置标题容器样式 */\n.setting-title {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n}\n\n.setting-title b {\n  font-size: 14px;\n  color: #303133;\n}\n\n/* 出题方式问号图标样式 */\n.paper-type-question {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n  position: relative;\n  z-index: 10;\n  font-size: 14px;\n  line-height: 1;\n}\n\n.paper-type-question:hover {\n  color: #409eff;\n}\n\n/* 出题方式提示框样式 */\n.paper-type-tooltip {\n  max-width: 320px !important;\n  z-index: 2000 !important;\n}\n\n/* 迟到限制提示框样式 */\n.late-limit-tooltip {\n  max-width: 280px !important;\n  z-index: 2000 !important;\n}\n\n/* 随机规则设置对话框样式 */\n.cascade-random-rules {\n  min-height: 400px;\n}\n\n/* 确保对话框层级正确 */\n::v-deep .el-dialog__wrapper {\n  z-index: 3000 !important;\n}\n\n::v-deep .el-dialog {\n  z-index: 3001 !important;\n  position: relative !important;\n}\n\n::v-deep .el-overlay {\n  z-index: 2999 !important;\n}\n\n.topbar {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.topbar .fl {\n  float: left;\n}\n\n.topbar .summary {\n  margin-left: 15px;\n  color: #666;\n  font-size: 14px;\n}\n\n.topbar .total_score {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.guide-steps-list {\n  margin: 30px 0;\n  padding: 0 20px;\n}\n\n/* 步骤组件样式优化 */\n::v-deep .guide-steps-list .el-step {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__head {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__main {\n  text-align: center;\n  margin-top: 10px;\n}\n\n::v-deep .guide-steps-list .el-step__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n::v-deep .guide-steps-list .el-step__description {\n  margin-top: 8px;\n  padding: 0 10px;\n}\n\n.step-content {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 5px;\n}\n\n.rules-content {\n  min-height: 200px;\n  margin: 20px 0;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 60px 0;\n  color: #999;\n  font-size: 14px;\n}\n\n.bottom-panel {\n  text-align: center;\n  padding: 20px 0;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n/* 强制覆盖Element UI折叠面板背景色 */\n.el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n  font-size: 16px !important;\n  font-weight: bold !important;\n}\n\n.el-collapse-item__header.is-active {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 使用属性选择器强制覆盖 */\n[class*=\"el-collapse-item__header\"] {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 深度选择器 */\n.exam-editor >>> .el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .exam-editor-left {\n    padding-right: 370px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 70px;\n  }\n}\n\n@media (max-width: 768px) {\n  .exam-editor {\n    flex-direction: column;\n  }\n\n  .exam-editor-left {\n    padding-right: 20px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 20px;\n  }\n\n  .exam-editor-right {\n    position: relative !important;\n    width: 100% !important;\n    height: 50vh;\n    border-left: none;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .collapse-button {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0uDA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAH,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;MACA;MACAM,mBAAA;MACAC,eAAA;MAEA;MACAC,cAAA;MAEA;MACAC,SAAA;QACAR,OAAA;QACAS,SAAA;QACAC,SAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QAAA;QACAC,SAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,eAAA;QAAA;QACAC,SAAA;QAAA;QACAC,QAAA;QAAA;QACAC,kBAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;QAAA;QACAC,YAAA;QAAA;QACAC,MAAA;QAAA;QACAC,eAAA;QAAA;QACAC,eAAA;QAAA;QACAC,UAAA;MACA;MAEA;;MAEA;MACAC,cAAA;MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;;MAEA;MACAC,sBAAA;MACAC,eAAA;MAAA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA;MACA;MACAC,YAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,cAAA;UACAC,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAC,cAAA;QAAA;QACAC,YAAA;QAAA;QACAC,UAAA;QAAA;QACAC,qBAAA;QAAA;QACAC,SAAA;QAAA;QACAC,kBAAA;UACAR,OAAA;UACAC,QAAA;UACAC,KAAA;QACA;QACAO,iBAAA;QAAA;QACAC,aAAA;MACA;MAEA;MACAC,qBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MAEA;MACAC,cAAA;MACA;MACAC,KAAA;MAEA;MACAC,iBAAA;MACAC,QAAA;QACAP,QAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA;MACAK,gBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,wBAAA;MAAA;MACA3B,aAAA;MACA4B,mBAAA;IAAA,sBACA,sBACA,oBACA,gBACA,cACA,mBACA;MACA1B,OAAA;MACAC,QAAA;MACA0B,QAAA,EAAAC;IACA;EAEA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,YAAAjC,aAAA,CAAAkC,MAAA,WAAAC,IAAA;QAAA,OACAF,KAAA,CAAAV,QAAA,CAAAL,aAAA,CAAAkB,QAAA,CAAAD,IAAA,CAAAE,MAAA;MAAA,CACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAlC,KAAA;MACA,KAAAiB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA9C,QAAA,IAAA8C,IAAA,CAAA9C,QAAA,CAAA+C,MAAA;UACA;UACAD,IAAA,CAAA9C,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YACAtC,KAAA,IAAAsC,KAAA,CAAAC,aAAA;UACA;QACA;UACA;UACAvC,KAAA,IAAAoC,IAAA,CAAAG,aAAA;QACA;MACA;MACA,OAAAvC,KAAA;IACA;IAEA;IACAwC,cAAA,WAAAA,eAAA;MACA,IAAAxC,KAAA;MACA,KAAAiB,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA9C,QAAA,IAAA8C,IAAA,CAAA9C,QAAA,CAAA+C,MAAA;UACA;UACAD,IAAA,CAAA9C,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YACAtC,KAAA,IAAAsC,KAAA,CAAA3E,UAAA;UACA;QACA;UACA;UACAqC,KAAA,IAAAoC,IAAA,CAAAzE,UAAA;QACA;MACA;MACA,OAAAqC,KAAA;IACA;IAEA;IACAyC,WAAA,WAAAA,YAAA;MACA,YAAAxB,KAAA,CAAAa,MAAA,WAAAM,IAAA;QAAA,QAAAA,IAAA,CAAAM,QAAA;MAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,OAAA,OAAAC,GAAA;MACA,KAAA5B,KAAA,CAAAkB,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAzF,IAAA,UAAAyF,IAAA,CAAAtB,aAAA;UACAsB,IAAA,CAAAtB,aAAA,CAAAqB,OAAA,WAAAF,MAAA;YACAW,OAAA,CAAAE,GAAA,CAAAb,MAAA;UACA;QACA;MACA;MACA,OAAAc,KAAA,CAAAC,IAAA,CAAAJ,OAAA;IACA;IAEA;IACAK,kBAAA,WAAAA,mBAAA;MACA,IAAAC,KAAA;MACA,KAAApE,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACA,IAAAxG,IAAA,GAAAwG,QAAA,CAAAxG,IAAA;QACAuG,KAAA,CAAAvG,IAAA,KAAAuG,KAAA,CAAAvG,IAAA;MACA;MACA,OAAAuG,KAAA;IACA;IAEA;IACAE,yBAAA,WAAAA,0BAAA;MACA,IAAApD,KAAA;MACA,KAAAU,gBAAA,CAAAyB,OAAA,WAAAC,IAAA;QACApC,KAAA,IAAAoC,IAAA,CAAAG,aAAA;MACA;MACA,OAAAvC,KAAA;IACA;IAEA;IACAqD,qBAAA,WAAAA,sBAAA;MACA,IAAArD,KAAA;MACA,KAAAU,gBAAA,CAAAyB,OAAA,WAAAC,IAAA;QACApC,KAAA,IAAAoC,IAAA,CAAAzE,UAAA;MACA;MACA,OAAAqC,KAAA;IACA;EACA;EACAsD,KAAA;IACA5G,OAAA,WAAAA,QAAA6G,GAAA;MACA,IAAAA,GAAA,SAAAzG,OAAA;QACA,KAAA0G,aAAA;MACA;IACA;IAEA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QAAA,WAAAC,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAAC,QAAA;UAAA,WAAAF,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBAAA,MACAP,MAAA,CAAAxC,QAAA,CAAAP,QAAA,UAAA8C,MAAA,IAAAA,MAAA,CAAArB,MAAA;kBAAA4B,QAAA,CAAAC,CAAA;kBAAA;gBAAA;gBAAAD,QAAA,CAAAC,CAAA;gBAAA,OACAP,MAAA,CAAAQ,uBAAA;cAAA;gBAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;UAAA,GAAAL,OAAA;QAAA;MAEA;MACAM,IAAA;IACA;EACA;EACAC,OAAA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA;IACA;IAEA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAtH,mBAAA,SAAAA,mBAAA;MACA,KAAAC,eAAA,QAAAD,mBAAA;IACA;IAEA,WACAuH,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA,aACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAF,QAAA,CAAAC,IAAA;IACA;IAEA,WACAE,aAAA,WAAAA,cAAA;MACA,KAAAH,QAAA,CAAAC,IAAA;IACA;IAEA,WACAG,oBAAA,WAAAA,qBAAA;MACA,KAAAJ,QAAA,CAAAC,IAAA;IACA;IAEA,YACAI,gBAAA,WAAAA,iBAAA;MACA,KAAAL,QAAA,CAAAC,IAAA;IACA;IAIA,iBACAK,aAAA,WAAAA,cAAA;MACA,KAAAjE,cAAA;IACA;IAEA,qBACAkE,eAAA,WAAAA,gBAAA;MACA,KAAAlE,cAAA;IACA;IAEA,aACAmE,kBAAA,WAAAA,mBAAA;MACA,KAAA/D,gBAAA;MACA,KAAAC,WAAA;;MAEA;MACA,SAAAJ,KAAA,CAAAoB,MAAA;QACA,KAAAlB,QAAA,CAAAP,QAAA,QAAAK,KAAA,IAAAtE,IAAA;MACA;QACA,KAAAwE,QAAA,CAAAP,QAAA;MACA;MAEA,KAAAM,iBAAA;MACA;MACA,KAAAkE,WAAA;QACAtF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA2D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/D,mBAAA;MACA;MACAgE,OAAA,CAAAC,GAAA,EACA,IAAAC,8BAAA,OAAAN,WAAA,GACA,IAAAO,sBAAA;QAAA5F,QAAA;MAAA,GACA,EAAA6F,IAAA,WAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAlJ,OAAA,EAAAgJ,KAAA;UAAAG,YAAA,GAAAF,KAAA;UAAAG,gBAAA,GAAAH,KAAA;QACA,IAAAlG,aAAA,GAAAoG,YAAA,CAAAE,IAAA;QACAX,MAAA,CAAAvF,KAAA,GAAAgG,YAAA,CAAAhG,KAAA;QACAuF,MAAA,CAAArG,eAAA,GAAA+G,gBAAA,CAAAC,IAAA,IAAAD,gBAAA,CAAAhJ,IAAA;;QAEA;QACA,IAAAkJ,kBAAA,GAAAvG,aAAA,CAAAwG,GAAA,WAAArE,IAAA;UAAA,OACA,IAAAsE,+BAAA,EAAAtE,IAAA,CAAAE,MAAA,EAAA2D,IAAA,WAAA1C,KAAA;YACAnB,IAAA,CAAAuE,aAAA,GAAApD,KAAA,CAAAjG,IAAA,GAAAiG,KAAA,CAAAjG,IAAA,CAAAsJ,UAAA,IAAArD,KAAA,CAAAjG,IAAA,CAAA+C,KAAA;YACA,OAAA+B,IAAA;UACA,GAAAyE,KAAA;YACAzE,IAAA,CAAAuE,aAAA;YACA,OAAAvE,IAAA;UACA;QAAA,CACA;QAEAyD,OAAA,CAAAC,GAAA,CAAAU,kBAAA,EAAAP,IAAA,WAAAa,cAAA;UACAlB,MAAA,CAAA3F,aAAA,GAAA6G,cAAA;UACAlB,MAAA,CAAA/D,mBAAA;QACA;MACA,GAAAgF,KAAA;QACAjB,MAAA,CAAA/D,mBAAA;MACA;IACA;IAEA,WACAkF,eAAA,WAAAA,gBAAA;MACA,KAAA/B,QAAA,CAAAC,IAAA;MACA,KAAA5D,cAAA;IACA;IAEA,aACA2F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhD,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAA+C,SAAA;QAAA,WAAAhD,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAAA,MACA0C,MAAA,CAAAzF,QAAA,CAAAP,QAAA,UAAAgG,MAAA,CAAAzF,QAAA,CAAAL,aAAA,CAAAuB,MAAA;gBAAAyE,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAGAwC,MAAA,CAAAzF,QAAA,CAAAP,QAAA,UAAAgG,MAAA,CAAAzF,QAAA,CAAAJ,qBAAA,CAAAsB,MAAA;gBAAAyE,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAAA,MAKAwC,MAAA,CAAAzF,QAAA,CAAAP,QAAA;gBAAAkG,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA,MACA0C,MAAA,CAAArF,wBAAA;gBAAAuF,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cACA0C,MAAA,CAAAjC,QAAA,CAAAoC,OAAA;cAAA,OAAAD,SAAA,CAAA1C,CAAA;YAAA;cAKA,IAAAwC,MAAA,CAAAxF,gBAAA;gBACA;gBACAwF,MAAA,CAAAI,kBAAA;cACA,WAAAJ,MAAA,CAAAxF,gBAAA;gBACA;gBACAwF,MAAA,CAAAK,kBAAA;cACA;gBACA;gBACAL,MAAA,CAAAM,UAAA;cACA;cAEAN,MAAA,CAAAjC,QAAA,CAAAwC,OAAA,CAAAP,MAAA,CAAAxF,gBAAA;cACAwF,MAAA,CAAA1F,iBAAA;;cAEA;cACA0F,MAAA,CAAAQ,aAAA;YAAA;cAAA,OAAAN,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,IAAA5E,IAAA,QAAAf,WAAA;MACAe,IAAA,CAAAzF,IAAA,QAAAwE,QAAA,CAAAP,QAAA;MACAwB,IAAA,CAAAvB,YAAA,QAAAM,QAAA,CAAAN,YAAA;MAEA,SAAAM,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAAuG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAAwE,GAAA,WAAArE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA6E,aAAA,EAAAvE,IAAA,CAAAuE;UACA;QAAA;QACAlE,IAAA,CAAAkF,YAAA,GAAAlF,IAAA,CAAAR,aAAA,CAAA2F,MAAA,WAAAC,GAAA,EAAAzF,IAAA;UAAA,OAAAyF,GAAA,IAAAzF,IAAA,CAAAuE,aAAA;QAAA;;QAEA;QACA,IAAAlE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAkF,YAAA;UACAlF,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAkF,YAAA;QACA;QACAlF,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,gBAAA;MACA,gBAAAtG,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAsG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAkF,YAAA,QAAA/F,wBAAA;MACA;IACA;IAEA,YACA2F,UAAA,WAAAA,WAAA;MAAA,IAAAQ,MAAA;MACA;MACA,SAAAtG,gBAAA,sBAAAE,UAAA;QACA,KAAAqG,aAAA;QACA;MACA;;MAEA;MACA,SAAAxG,QAAA,CAAAP,QAAA,eAAAO,QAAA,CAAAN,YAAA,uBAAAe,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAA6F,KAAA;UACA,IAAAxF,IAAA;YACAyF,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YAAA;YACAjL,IAAA,EAAA+K,MAAA,CAAAvG,QAAA,CAAAP,QAAA;YACAC,YAAA,EAAA6G,MAAA,CAAAvG,QAAA,CAAAN,YAAA;YACA0B,aAAA;YACAkF,gBAAA;YACA9J,UAAA;YACAmD,aAAA,GAAAiB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA6E,aAAA,EAAAvE,IAAA,CAAAuE;YACA;YACAgB,YAAA,EAAAvF,IAAA,CAAAuE,aAAA;UACA;UACAoB,MAAA,CAAAM,eAAA,CAAA5F,IAAA;UACAsF,MAAA,CAAAzG,KAAA,CAAAgH,IAAA,CAAA7F,IAAA;QACA;MACA;QACA;QACA,KAAAuF,aAAA;MACA;IACA;IAEA,mBACAV,kBAAA,WAAAA,mBAAA;MAAA,IAAAiB,MAAA;MACA;MACA,SAAA/G,QAAA,CAAAP,QAAA,eAAAO,QAAA,CAAAN,YAAA,uBAAAe,aAAA,CAAAS,MAAA;QACA,KAAAT,aAAA,CAAAO,OAAA,WAAAJ,IAAA,EAAA6F,KAAA;UACA,IAAAxF,IAAA;YACAyF,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;YACAjL,IAAA,EAAAuL,MAAA,CAAA/G,QAAA,CAAAP,QAAA;YACAC,YAAA,EAAAqH,MAAA,CAAA/G,QAAA,CAAAN,YAAA;YACA0B,aAAA;YACAkF,gBAAA;YACA9J,UAAA;YACAmD,aAAA,GAAAiB,IAAA,CAAAE,MAAA;YACAL,aAAA;cACAK,MAAA,EAAAF,IAAA,CAAAE,MAAA;cACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;cACA6E,aAAA,EAAAvE,IAAA,CAAAuE;YACA;YACAgB,YAAA,EAAAvF,IAAA,CAAAuE,aAAA;UACA;UACA4B,MAAA,CAAAC,0BAAA,CAAA/F,IAAA;UACA8F,MAAA,CAAAxH,gBAAA,CAAAuH,IAAA,CAAA7F,IAAA;QACA;MACA;QACA;QACA,KAAAgG,wBAAA;MACA;IACA;IAEA,qBACAA,wBAAA,WAAAA,yBAAA;MACA,IAAAhG,IAAA;QACAyF,EAAA,EAAAC,IAAA,CAAAC,GAAA;QACApL,IAAA,OAAAwE,QAAA,CAAAP,QAAA;QACAC,YAAA,OAAAM,QAAA,CAAAN,YAAA;QACA0B,aAAA;QACAkF,gBAAA;QACA9J,UAAA;QACA2J,YAAA;MACA;MAEA,SAAAnG,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAAuG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAAwE,GAAA,WAAArE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA6E,aAAA,EAAAvE,IAAA,CAAAuE;UACA;QAAA;QACAlE,IAAA,CAAAkF,YAAA,GAAAlF,IAAA,CAAAR,aAAA,CAAA2F,MAAA,WAAAC,GAAA,EAAAzF,IAAA;UAAA,OAAAyF,GAAA,IAAAzF,IAAA,CAAAuE,aAAA;QAAA;MACA,gBAAAnF,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAsG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAkF,YAAA,QAAA/F,wBAAA;MACA;MAEA,KAAAb,gBAAA,CAAAuH,IAAA,CAAA7F,IAAA;MACA,KAAA+F,0BAAA,CAAA/F,IAAA;IACA;IAEA,aACAuF,aAAA,WAAAA,cAAA;MACA,IAAAvF,IAAA;QACAyF,EAAA,EAAAC,IAAA,CAAAC,GAAA;QAAA;QACApL,IAAA,OAAAwE,QAAA,CAAAP,QAAA;QACAC,YAAA,OAAAM,QAAA,CAAAN,YAAA;QACA0B,aAAA;QAAA;QACAkF,gBAAA;QAAA;QACA9J,UAAA;QAAA;QACA2J,YAAA;MACA;;MAEA;MACA,SAAAlG,gBAAA,sBAAAE,UAAA;QACAc,IAAA,CAAAM,QAAA,QAAApB,UAAA,CAAAuG,EAAA;;QAEA;QACA,UAAAvG,UAAA,CAAAhC,QAAA;UACA,KAAA+I,IAAA,MAAA/G,UAAA;QACA;;QAEA;QACA,KAAAA,UAAA,CAAAhC,QAAA,CAAA2I,IAAA,CAAA7F,IAAA;MACA;MAEA,SAAAjB,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAAtB,aAAA,OAAAuG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAL,aAAA;QACAsB,IAAA,CAAAR,aAAA,QAAAA,aAAA,CAAAwE,GAAA,WAAArE,IAAA;UAAA;YACAE,MAAA,EAAAF,IAAA,CAAAE,MAAA;YACAR,QAAA,EAAAM,IAAA,CAAAN,QAAA;YACA6E,aAAA,EAAAvE,IAAA,CAAAuE;UACA;QAAA;QACAlE,IAAA,CAAAkF,YAAA,GAAAlF,IAAA,CAAAR,aAAA,CAAA2F,MAAA,WAAAC,GAAA,EAAAzF,IAAA;UAAA,OAAAyF,GAAA,IAAAzF,IAAA,CAAAuE,aAAA;QAAA;MACA,gBAAAnF,QAAA,CAAAP,QAAA;QACA;QACAwB,IAAA,CAAArB,qBAAA,OAAAsG,mBAAA,CAAAxK,OAAA,OAAAsE,QAAA,CAAAJ,qBAAA;QACAqB,IAAA,CAAAkF,YAAA,QAAA/F,wBAAA;MACA;;MAEA;MACA,SAAAH,gBAAA;QACA,KAAAH,KAAA,CAAAgH,IAAA,CAAA7F,IAAA;MACA;;MAEA;MACA,KAAA4F,eAAA,CAAA5F,IAAA;IACA;IAEA,aACAgF,aAAA,WAAAA,cAAA;MACA,KAAAjG,QAAA;QACAP,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;MACA,KAAAK,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,UAAA;MACA,KAAAC,wBAAA;IACA;IAEA,WACA+G,YAAA,WAAAA,aAAA;MACA,KAAAlD,WAAA,CAAAtF,OAAA;MACA,KAAAsF,WAAA,CAAA3D,QAAA,QAAA4D,aAAA,IAAA3D,SAAA;MACA,KAAA4D,iBAAA;IACA;IAEA,WACAiD,WAAA,WAAAA,YAAA;MACA,KAAAlD,aAAA;MACA,KAAAD,WAAA,CAAAtF,OAAA;MACA,KAAAsF,WAAA,CAAA3D,QAAA,GAAAC,SAAA;MACA,KAAA4D,iBAAA;IACA;IAEA,aACAkD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtH,QAAA,CAAAL,aAAA,GAAA2H,SAAA,CAAArC,GAAA,WAAAsC,IAAA;QAAA,OAAAA,IAAA,CAAAzG,MAAA;MAAA;IACA;IAEA,eACA0G,kBAAA,WAAAA,mBAAA1G,MAAA;MAAA,IAAA2G,MAAA;MACA,IAAAhB,KAAA,QAAAzG,QAAA,CAAAL,aAAA,CAAA+H,OAAA,CAAA5G,MAAA;MACA,IAAA2F,KAAA;QACA,KAAAzG,QAAA,CAAAL,aAAA,CAAAgI,MAAA,CAAAlB,KAAA;MACA;;MAEA;MACA,KAAAmB,SAAA;QACA,IAAAC,KAAA,GAAAJ,MAAA,CAAAK,KAAA,CAAAC,iBAAA;QACA,IAAAF,KAAA;UACA;UACA,IAAAG,aAAA,GAAAP,MAAA,CAAAhJ,aAAA,CAAAwJ,IAAA,WAAArH,IAAA;YAAA,OAAAA,IAAA,CAAAE,MAAA,KAAAA,MAAA;UAAA;UACA,IAAAkH,aAAA;YACAH,KAAA,CAAAK,kBAAA,CAAAF,aAAA;UACA;QACA;MACA;IACA;IAEA,WACAG,mBAAA,WAAAA,oBAAAC,IAAA;MACA,KAAAnE,WAAA,CAAAtF,OAAA,GAAAyJ,IAAA;MACA,KAAAjE,iBAAA;IACA;IAEA,mBACAkE,eAAA,WAAAA,gBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAC,gBAAA,MAAAzK,eAAA,EAAAuK,UAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAlN,IAAA;IACA;IAEA,4BACAmN,gBAAA,WAAAA,iBAAAC,UAAA,EAAA/B,EAAA;MACA;MACA,IAAAgC,cAAA,QAAAC,iBAAA,CAAAF,UAAA;;MAEA;MACA,IAAAF,QAAA,GAAAG,cAAA,CAAAT,IAAA,WAAAW,GAAA;QAAA,OAAAA,GAAA,CAAAlC,EAAA,KAAAA,EAAA;MAAA;MACA,OAAA6B,QAAA;IACA;IAEA,iBACAI,iBAAA,WAAAA,kBAAAF,UAAA;MACA,IAAAI,MAAA;MAEA,SAAAC,QAAAC,IAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAvN,OAAA,EACAqN,IAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAjG,CAAA,IAAAqG,IAAA;YAAA,IAAAR,GAAA,GAAAM,KAAA,CAAAjL,KAAA;YACA4K,MAAA,CAAA/B,IAAA,CAAA8B,GAAA;YACA,IAAAA,GAAA,CAAAzK,QAAA,IAAAyK,GAAA,CAAAzK,QAAA,CAAA+C,MAAA;cACA4H,OAAA,CAAAF,GAAA,CAAAzK,QAAA;YACA;UACA;QAAA,SAAAkL,GAAA;UAAAL,SAAA,CAAAM,CAAA,CAAAD,GAAA;QAAA;UAAAL,SAAA,CAAAO,CAAA;QAAA;MACA;MAEAT,OAAA,CAAAL,UAAA;MACA,OAAAI,MAAA;IACA;IAEA,aACAhC,eAAA,WAAAA,gBAAA5F,IAAA;MACAA,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,gBAAA;IACA;IAEA,aACAkD,mBAAA,WAAAA,oBAAAC,aAAA;MACA,IAAAC,eAAA,OAAA3N,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QACA;QAAA;QAAA;MAAA,QACA,gCACA;;MAEA;MACA,IAAAkG,KAAA,CAAA+H,OAAA,CAAAF,aAAA;QACA,OAAAA,aAAA,CAAAxE,GAAA,WAAA2E,CAAA;UAAA,OAAAF,eAAA,CAAAE,CAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,OAAAH,eAAA,CAAAD,aAAA;IACA;IAIA,cACAK,iBAAA,WAAAA,kBAAAC,SAAA;MACA,IAAAA,SAAA,CAAAvO,IAAA;QACA;MACA,WAAAuO,SAAA,CAAAvO,IAAA;QACA;QACA,IAAAuO,SAAA,CAAAnK,qBAAA,IAAAmK,SAAA,CAAAnK,qBAAA,CAAAsB,MAAA;UACA,IAAAwI,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAA,eAAA,CAAAK,SAAA,CAAAnK,qBAAA;QACA;UACA;QACA;MACA;MACA;IACA;IAEA,iCACAoK,gBAAA,WAAAA,iBAAA/I,IAAA;MACA,IAAAA,IAAA,CAAAzF,IAAA;QACA;MACA,WAAAyF,IAAA,CAAAzF,IAAA;QACA;MACA;MACA;IACA;IAEA,+BACAyO,cAAA,WAAAA,eAAAhJ,IAAA;MACA,IAAAA,IAAA,CAAAzF,IAAA;QACA;QACA,IAAAyF,IAAA,CAAAR,aAAA,IAAAQ,IAAA,CAAAR,aAAA,CAAAS,MAAA;UACA,OAAAD,IAAA,CAAAR,aAAA,CAAAwE,GAAA,WAAArE,IAAA;YAAA,OAAAA,IAAA,CAAAN,QAAA;UAAA,GAAAuJ,IAAA;QACA;QACA;MACA,WAAA5I,IAAA,CAAAzF,IAAA;QACA;QACA,IAAAyF,IAAA,CAAArB,qBAAA,IAAAqB,IAAA,CAAArB,qBAAA,CAAAsB,MAAA;UACA,IAAAwI,eAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAzI,IAAA,CAAArB,qBAAA,CAAAqF,GAAA,WAAAzJ,IAAA;YAAA,OAAAkO,eAAA,CAAAlO,IAAA;UAAA,GAAAqO,IAAA;QACA;QACA;MACA;MACA;IACA;IAEA,YACAK,UAAA,WAAAA,WAAAjJ,IAAA;MACA,KAAAhB,gBAAA;MACA,KAAAE,UAAA,GAAAc,IAAA;;MAEA;MACA,IAAAkJ,eAAA;MAEA,IAAAlJ,IAAA,CAAAzF,IAAA;QACA;QACA2O,eAAA;MACA;QACA;QACAA,eAAA;MACA;MAEA,KAAAnK,QAAA,CAAAP,QAAA,GAAA0K,eAAA;MACA,KAAAnK,QAAA,CAAAN,YAAA;MACA,KAAAM,QAAA,CAAAL,aAAA;MACA,KAAAK,QAAA,CAAAJ,qBAAA;MAEA,KAAAG,iBAAA;MACA,KAAAkE,WAAA;QACAtF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA2D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAiG,QAAA,WAAAA,SAAAnJ,IAAA;MAAA,IAAAoJ,MAAA;MACA,KAAApK,gBAAA;MACA,KAAAC,WAAA,GAAAe,IAAA;MACA,KAAAjB,QAAA,CAAAP,QAAA,GAAAwB,IAAA,CAAAzF,IAAA;MACA,KAAAwE,QAAA,CAAAN,YAAA,GAAAuB,IAAA,CAAAvB,YAAA;MAEA,IAAAuB,IAAA,CAAAzF,IAAA;QACA;QACA,KAAAwE,QAAA,CAAAL,aAAA,OAAAuG,mBAAA,CAAAxK,OAAA,EAAAuF,IAAA,CAAAtB,aAAA;MACA,WAAAsB,IAAA,CAAAzF,IAAA;QACA;QACA,KAAAwE,QAAA,CAAAJ,qBAAA,OAAAsG,mBAAA,CAAAxK,OAAA,EAAAuF,IAAA,CAAArB,qBAAA;QACA;QACA,KAAAgI,SAAA;UACAyC,MAAA,CAAArH,uBAAA;QACA;MACA;MAEA,KAAAjD,iBAAA;MACA,KAAAkE,WAAA;QACAtF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA2D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,WACAmG,UAAA,WAAAA,WAAArJ,IAAA;MAAA,IAAAsJ,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlP,IAAA;MACA,GAAAiJ,IAAA;QACA,IAAAgC,KAAA,GAAA8D,MAAA,CAAAzK,KAAA,CAAA6K,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAzF,IAAA,CAAAyF,EAAA;QAAA;QACA,IAAAD,KAAA;UACA8D,MAAA,CAAAzK,KAAA,CAAA6H,MAAA,CAAAlB,KAAA;UACA8D,MAAA,CAAA/G,QAAA,CAAAwC,OAAA;QACA;MACA,GAAAX,KAAA;QACA;MAAA,CACA;IACA;IAEA,YACAwF,eAAA,WAAAA,gBAAA1K,UAAA,EAAA4J,SAAA;MAAA,IAAAe,MAAA;MACA,KAAAN,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlP,IAAA;MACA,GAAAiJ,IAAA;QACA,IAAAgC,KAAA,GAAAtG,UAAA,CAAAhC,QAAA,CAAAwM,SAAA,WAAAxJ,KAAA;UAAA,OAAAA,KAAA,CAAAuF,EAAA,KAAAqD,SAAA,CAAArD,EAAA;QAAA;QACA,IAAAD,KAAA;UACAtG,UAAA,CAAAhC,QAAA,CAAAwJ,MAAA,CAAAlB,KAAA;UACAqE,MAAA,CAAAtH,QAAA,CAAAwC,OAAA;QACA;MACA,GAAAX,KAAA;QACA;MAAA,CACA;IACA;IAEA,cACA0F,cAAA,WAAAA,eAAA;MACA,aAAA9K,gBAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA,iBACA+K,2BAAA,WAAAA,4BAAA;MACA;MACA;IACA;IAEA,iBACAC,kBAAA,WAAAA,mBAAAxL,QAAA;MACA;MACA,SAAAQ,gBAAA;QACA,YAAAC,WAAA,CAAA1E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA,sBAAAE,UAAA;QACA;QACA,SAAAA,UAAA,CAAA3E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,SAAAU,UAAA,CAAA3E,IAAA;UACA,OAAAiE,QAAA;QACA;QACA;QACA,OAAAA,QAAA,UAAAU,UAAA,CAAA3E,IAAA;MACA;;MAEA;MACA,SAAAyE,gBAAA;QACA;QACA,OAAAR,QAAA;MACA;;MAEA;MACA;IACA;IAEA,iBACAyL,sBAAA,WAAAA,uBAAAnM,YAAA;MACA;MACA,SAAAkB,gBAAA,uBAAAE,UAAA;QACA;MACA;;MAEA;MACA,IAAAgL,qBAAA;MACA,SAAAhL,UAAA,CAAAhC,QAAA;QACA,KAAAgC,UAAA,CAAAhC,QAAA,CAAA6C,OAAA,WAAAG,KAAA;UACA,IAAAA,KAAA,CAAA3F,IAAA,UAAA2F,KAAA,CAAAvB,qBAAA;YACAuL,qBAAA,CAAArE,IAAA,CAAAsE,KAAA,CAAAD,qBAAA,MAAAjF,mBAAA,CAAAxK,OAAA,EAAAyF,KAAA,CAAAvB,qBAAA;UACA;QACA;MACA;;MAEA;MACA,QAAAuL,qBAAA,CAAAtK,QAAA,CAAA9B,YAAA;IACA;IAEA,iBACAsM,eAAA,WAAAA,gBAAAC,GAAA;MACA;MACA,SAAArL,gBAAA;QACA;MACA;;MAEA;MACA,SAAAA,gBAAA;QACA;MACA;;MAEA;MACA,aAAAuB,WAAA,CAAAX,QAAA,CAAAyK,GAAA,CAAAxK,MAAA;IACA;IAEA,iBACAyK,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;MACA;MACA,SAAArL,gBAAA,oBAAAA,gBAAA,sBAAAuB,WAAA,CAAAX,QAAA,CAAAyK,GAAA,CAAAxK,MAAA;QACA;MACA;MACA;IACA;IAEA,eACAkC,uBAAA,WAAAA,wBAAA;MAAA,IAAAyI,MAAA;MAAA,WAAAhJ,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAA+I,SAAA;QAAA,IAAAjL,aAAA,EAAAkL,KAAA,EAAAC,EAAA;QAAA,WAAAlJ,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAAgJ,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,CAAA;YAAA;cAAA,MACA0I,MAAA,CAAAzL,QAAA,CAAAP,QAAA,WAAAgM,MAAA,CAAAzL,QAAA,CAAAJ,qBAAA,CAAAsB,MAAA;gBAAA2K,SAAA,CAAA9I,CAAA;gBAAA;cAAA;cACA0I,MAAA,CAAArL,wBAAA;cAAA,OAAAyL,SAAA,CAAA5I,CAAA;YAAA;cAIA;cACAxC,aAAA;cACA,IAAAgL,MAAA,CAAAxL,gBAAA,iBAAAwL,MAAA,CAAAtL,UAAA,IAAAsL,MAAA,CAAAtL,UAAA,CAAA3E,IAAA;gBACA;gBACAiF,aAAA,GAAAgL,MAAA,CAAAtL,UAAA,CAAAM,aAAA;cACA;gBACA;gBACAA,aAAA,GAAAgL,MAAA,CAAAhL,aAAA;cACA;cAAA,IAEAA,aAAA,CAAAS,MAAA;gBAAA2K,SAAA,CAAA9I,CAAA;gBAAA;cAAA;cACA0I,MAAA,CAAArL,wBAAA;cACAqL,MAAA,CAAAjI,QAAA,CAAAoC,OAAA;cAAA,OAAAiG,SAAA,CAAA5I,CAAA;YAAA;cAAA4I,SAAA,CAAAC,CAAA;cAAAD,SAAA,CAAA9I,CAAA;cAAA,OAKA0I,MAAA,CAAAM,sBAAA,CAAAtL,aAAA,EAAAgL,MAAA,CAAAzL,QAAA,CAAAJ,qBAAA;YAAA;cAAA+L,KAAA,GAAAE,SAAA,CAAAG,CAAA;cACAP,MAAA,CAAArL,wBAAA,GAAAuL,KAAA;cAEA,IAAAA,KAAA;gBACAF,MAAA,CAAAjI,QAAA,CAAAoC,OAAA;cACA;cAAAiG,SAAA,CAAA9I,CAAA;cAAA;YAAA;cAAA8I,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAG,CAAA;cAEAC,OAAA,CAAAC,KAAA,cAAAN,EAAA;cACAH,MAAA,CAAArL,wBAAA;cACAqL,MAAA,CAAAjI,QAAA,CAAA0I,KAAA;YAAA;cAAA,OAAAL,SAAA,CAAA5I,CAAA;UAAA;QAAA,GAAAyI,QAAA;MAAA;IAEA;IAEA,oBACAK,sBAAA,WAAAA,uBAAAI,KAAA,EAAA1C,aAAA;MAAA,WAAAhH,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAAyJ,SAAA;QAAA,IAAAhH,UAAA,EAAAiH,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,GAAA;QAAA,WAAA9J,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAA4J,SAAA;UAAA,kBAAAA,SAAA,CAAA1J,CAAA;YAAA;cAAA,MACA,CAAAoJ,KAAA,CAAAjL,MAAA,KAAAuI,aAAA,CAAAvI,MAAA;gBAAAuL,SAAA,CAAA1J,CAAA;gBAAA;cAAA;cAAA,OAAA0J,SAAA,CAAAxJ,CAAA,IACA;YAAA;cAGAmC,UAAA,MAEA;cAAAiH,UAAA,OAAApD,2BAAA,CAAAvN,OAAA,EACAyQ,KAAA;cAAAM,SAAA,CAAAX,CAAA;cAAAS,KAAA,oBAAA7J,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAA4J,MAAA;gBAAA,IAAA3L,IAAA,EAAA8L,QAAA,EAAAC,UAAA,EAAAC,GAAA;gBAAA,WAAAlK,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAAgK,SAAA;kBAAA,kBAAAA,SAAA,CAAA9J,CAAA;oBAAA;sBAAAnC,IAAA,GAAA0L,MAAA,CAAArO,KAAA;sBAAA4O,SAAA,CAAAf,CAAA;sBAAAe,SAAA,CAAA9J,CAAA;sBAAA,OAGA,IAAAmC,+BAAA,EAAAtE,IAAA,CAAAE,MAAA;oBAAA;sBAAA4L,QAAA,GAAAG,SAAA,CAAAb,CAAA;sBACAC,OAAA,CAAAa,GAAA,gBAAAC,MAAA,CAAAnM,IAAA,CAAAE,MAAA,gCAAA4L,QAAA;sBAEA,IAAAA,QAAA,CAAAM,IAAA,YAAAN,QAAA,CAAA5Q,IAAA;wBACA6Q,UAAA,GAAAD,QAAA,CAAA5Q,IAAA,EAEA;wBACA2N,aAAA,CAAAzI,OAAA,WAAAxF,IAAA;0BACA,QAAAA,IAAA;4BACA;8BAAA;8BACA4J,UAAA,IAAAuH,UAAA,CAAAM,YAAA;8BACA;4BACA;8BAAA;8BACA7H,UAAA,IAAAuH,UAAA,CAAAO,cAAA;8BACA;4BACA;8BAAA;8BACA9H,UAAA,IAAAuH,UAAA,CAAAQ,QAAA;8BACA;0BACA;wBACA;sBACA;sBAAAN,SAAA,CAAA9J,CAAA;sBAAA;oBAAA;sBAAA8J,SAAA,CAAAf,CAAA;sBAAAc,GAAA,GAAAC,SAAA,CAAAb,CAAA;sBAEAC,OAAA,CAAAC,KAAA,4BAAAa,MAAA,CAAAnM,IAAA,CAAAE,MAAA,4CAAA8L,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAA5J,CAAA;kBAAA;gBAAA,GAAAsJ,KAAA;cAAA;cAAAF,UAAA,CAAAlD,CAAA;YAAA;cAAA,KAAAmD,MAAA,GAAAD,UAAA,CAAAtJ,CAAA,IAAAqG,IAAA;gBAAAqD,SAAA,CAAA1J,CAAA;gBAAA;cAAA;cAAA,OAAA0J,SAAA,CAAAW,CAAA,KAAAC,mBAAA,CAAA3R,OAAA,EAAA6Q,KAAA;YAAA;cAAAE,SAAA,CAAA1J,CAAA;cAAA;YAAA;cAAA0J,SAAA,CAAA1J,CAAA;cAAA;YAAA;cAAA0J,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAAAK,UAAA,CAAA/C,CAAA,CAAAkD,GAAA;YAAA;cAAAC,SAAA,CAAAX,CAAA;cAAAO,UAAA,CAAA9C,CAAA;cAAA,OAAAkD,SAAA,CAAAlD,CAAA;YAAA;cAIA0C,OAAA,CAAAa,GAAA,oCAAAC,MAAA,CAAA3H,UAAA;cAAA,OAAAqH,SAAA,CAAAxJ,CAAA,IACAmC,UAAA;UAAA;QAAA,GAAAgH,QAAA;MAAA;IACA;IAEA,eACAkB,qBAAA,WAAAA,sBAAA7N,QAAA;MACA;MACA,SAAAQ,gBAAA;QACA,YAAAC,WAAA,CAAA1E,IAAA,KAAAiE,QAAA;MACA;;MAEA;MACA,SAAAQ,gBAAA;QACA,OAAAR,QAAA;MACA;MAEA;IACA;IAEA,cACA8N,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,KAAA7P,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAAyL,QAAA,GAAAD,OAAA,CAAA5P,SAAA;MACA;IACA;IAEA,aACA8P,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAAvO,iBAAA,QAAAzB,cAAA,CAAAgD,MAAA,WAAAiN,CAAA;QAAA,OAAAA,CAAA,CAAAH,QAAA;MAAA;MACA,IAAArO,iBAAA,CAAA8B,MAAA;QACA,KAAAsC,QAAA,CAAAoC,OAAA;QACA;MACA;MAEA,KAAA4E,QAAA,+CAAAuC,MAAA,CAAA3N,iBAAA,CAAA8B,MAAA;QACAuJ,iBAAA;QACAC,gBAAA;QACAlP,IAAA;MACA,GAAAiJ,IAAA;QACAkJ,OAAA,CAAAhQ,cAAA,GAAAgQ,OAAA,CAAAhQ,cAAA,CAAAgD,MAAA,WAAAiN,CAAA;UAAA,QAAAA,CAAA,CAAAH,QAAA;QAAA;QACAE,OAAA,CAAA/P,SAAA;QACA+P,OAAA,CAAAnK,QAAA,CAAAwC,OAAA;MACA,GAAAX,KAAA;QACA;MAAA,CACA;IACA;IAEA,WACAwI,kBAAA,WAAAA,mBAAA;MACA,KAAA/P,sBAAA;MACA,KAAAgQ,oBAAA;IACA;IAEA,gBACAA,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAAxP,YAAA,CAAAC,gBAAA;MACA,KAAAD,YAAA,CAAAE,iBAAA;MACA,KAAAF,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAAX,YAAA,CAAAc,iBAAA;MACA,KAAAd,YAAA,CAAAe,aAAA;;MAEA;MACA,KAAA0O,gBAAA;;MAEA;MACA,KAAAC,6BAAA;IACA;IAEA,cACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,OAAA;MACA,IAAAzJ,sBAAA;QAAA5F,QAAA;MAAA,GAAA6F,IAAA,WAAAiI,QAAA;QACA,IAAAjE,UAAA,GAAAiE,QAAA,CAAA3H,IAAA;QACAkJ,OAAA,CAAAlQ,eAAA,GAAAkQ,OAAA,CAAAC,iBAAA,CAAAzF,UAAA;MACA,GAAApD,KAAA,WAAA6G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA+B,OAAA,CAAAlQ,eAAA;MACA;IACA;IAEA,YACAmQ,iBAAA,WAAAA,kBAAAzF,UAAA;MACA,IAAAxD,GAAA;;MAEA;MACAwD,UAAA,CAAAzH,OAAA,WAAAuH,QAAA;QACAtD,GAAA,CAAAsD,QAAA,CAAA7B,EAAA,QAAAyH,cAAA,CAAAzS,OAAA,MAAAyS,cAAA,CAAAzS,OAAA,MAAA6M,QAAA;UAAApK,QAAA;QAAA;MACA;;MAEA;MACA,IAAA0K,MAAA;MACAJ,UAAA,CAAAzH,OAAA,WAAAuH,QAAA;QACA,IAAAA,QAAA,CAAAhH,QAAA;UACA;UACAsH,MAAA,CAAA/B,IAAA,CAAA7B,GAAA,CAAAsD,QAAA,CAAA7B,EAAA;QACA;UACA;UACA,IAAAzB,GAAA,CAAAsD,QAAA,CAAAhH,QAAA;YACA0D,GAAA,CAAAsD,QAAA,CAAAhH,QAAA,EAAApD,QAAA,CAAA2I,IAAA,CAAA7B,GAAA,CAAAsD,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;;MAEA;MACA,IAAA0H,mBAAA,YAAAA,mBAAAC,IAAA;QACA,IAAAA,IAAA,CAAAlQ,QAAA,IAAAkQ,IAAA,CAAAlQ,QAAA,CAAA+C,MAAA;UACA,OAAAmN,IAAA,CAAAlQ,QAAA;QACA,WAAAkQ,IAAA,CAAAlQ,QAAA,IAAAkQ,IAAA,CAAAlQ,QAAA,CAAA+C,MAAA;UACAmN,IAAA,CAAAlQ,QAAA,CAAA6C,OAAA,WAAAG,KAAA;YAAA,OAAAiN,mBAAA,CAAAjN,KAAA;UAAA;QACA;MACA;;MAEA;MACA0H,MAAA,CAAA7H,OAAA,WAAAqN,IAAA;QAAA,OAAAD,mBAAA,CAAAC,IAAA;MAAA;MAEA,OAAAxF,MAAA;IACA;IAEA,gBACAyF,sBAAA,WAAAA,uBAAAhG,UAAA,EAAAG,UAAA;MACA,IAAAI,MAAA,IAAAP,UAAA;MAEA,IAAAiG,aAAA,YAAAA,aAAAhN,QAAA;QACAkH,UAAA,CAAAzH,OAAA,WAAAuH,QAAA;UACA,IAAAA,QAAA,CAAAhH,QAAA,KAAAA,QAAA;YACAsH,MAAA,CAAA/B,IAAA,CAAAyB,QAAA,CAAA7B,EAAA;YACA6H,aAAA,CAAAhG,QAAA,CAAA7B,EAAA;UACA;QACA;MACA;MAEA6H,aAAA,CAAAjG,UAAA;MACA,OAAAO,MAAA;IACA;IAEA,kBACAmF,6BAAA,WAAAA,8BAAA;MAAA,IAAAQ,OAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAAnQ,YAAA,CAAAC,gBAAA;QACA;QACA,IAAAiG,sBAAA;UAAA5F,QAAA;QAAA,GAAA6F,IAAA,WAAAiI,QAAA;UACA,IAAAgC,aAAA,GAAAhC,QAAA,CAAA3H,IAAA;UACA0J,WAAA,GAAAD,OAAA,CAAAF,sBAAA,CAAAE,OAAA,CAAAlQ,YAAA,CAAAC,gBAAA,EAAAmQ,aAAA;;UAEA;UACAF,OAAA,CAAAG,6BAAA,CAAAF,WAAA;QACA,GAAApJ,KAAA,WAAA6G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACAsC,OAAA,CAAAG,6BAAA;QACA;MACA;QACA;QACA,KAAAA,6BAAA;MACA;IACA;IAEA,mBACAA,6BAAA,WAAAA,8BAAAF,WAAA;MAAA,IAAAG,OAAA;MACA,IAAA3K,WAAA;QACAtF,OAAA,OAAAL,YAAA,CAAAI,cAAA,CAAAC,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAI,cAAA,CAAAE,QAAA;QACA0B,QAAA,OAAAhC,YAAA,CAAAE,iBAAA,IAAA+B;MACA;MAEA,IAAAkO,WAAA,CAAAvN,MAAA;QACA;QACA,IAAA2N,QAAA,GAAAJ,WAAA,CAAAxJ,GAAA,WAAAqD,UAAA;UACA,WAAA/D,8BAAA,MAAA4J,cAAA,CAAAzS,OAAA,MAAAyS,cAAA,CAAAzS,OAAA,MAAAuI,WAAA;YAAAqE,UAAA,EAAAA;UAAA;QACA;QAEAjE,OAAA,CAAAC,GAAA,CAAAuK,QAAA,EAAApK,IAAA,WAAAqK,SAAA;UACA,IAAAC,QAAA;UACA,IAAA3J,UAAA;UAEA0J,SAAA,CAAA9N,OAAA,WAAA0L,QAAA;YACA,IAAAA,QAAA,CAAA3H,IAAA;cACAgK,QAAA,CAAAjI,IAAA,CAAAsE,KAAA,CAAA2D,QAAA,MAAA7I,mBAAA,CAAAxK,OAAA,EAAAgR,QAAA,CAAA3H,IAAA;cACAK,UAAA,IAAAsH,QAAA,CAAA7N,KAAA;YACA;UACA;;UAEA;UACA,IAAAmQ,WAAA,GAAAD,QAAA,CAAApO,MAAA,WAAAC,IAAA,EAAA6F,KAAA,EAAAwI,IAAA;YAAA,OACAxI,KAAA,KAAAwI,IAAA,CAAAtE,SAAA,WAAAuE,CAAA;cAAA,OAAAA,CAAA,CAAApO,MAAA,KAAAF,IAAA,CAAAE,MAAA;YAAA;UAAA,CACA;UAEA8N,OAAA,CAAAtQ,YAAA,CAAAG,aAAA,GAAAuQ,WAAA;UACAJ,OAAA,CAAAtQ,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAAmQ,WAAA,CAAA9N,MAAA;QACA,GAAAmE,KAAA,WAAA6G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAApL,QAAA,CAAA0I,KAAA;UACA0C,OAAA,CAAAtQ,YAAA,CAAAG,aAAA;UACAmQ,OAAA,CAAAtQ,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;QACA;QACA,IAAA0F,8BAAA,EAAAN,WAAA,EAAAQ,IAAA,WAAAiI,QAAA;UACAkC,OAAA,CAAAtQ,YAAA,CAAAG,aAAA,GAAAiO,QAAA,CAAA3H,IAAA;UACA6J,OAAA,CAAAtQ,YAAA,CAAAI,cAAA,CAAAG,KAAA,GAAA6N,QAAA,CAAA7N,KAAA;QACA,GAAAwG,KAAA,WAAA6G,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;UACA0C,OAAA,CAAApL,QAAA,CAAA0I,KAAA;UACA0C,OAAA,CAAAtQ,YAAA,CAAAG,aAAA;UACAmQ,OAAA,CAAAtQ,YAAA,CAAAI,cAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEA,WACAsQ,mBAAA,WAAAA,oBAAA;MACA,KAAA7Q,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAAqP,6BAAA;IACA;IAEA,WACAoB,kBAAA,WAAAA,mBAAA9D,GAAA;MACA,KAAAhN,YAAA,CAAAQ,cAAA,GAAAwM,GAAA,CAAAxK,MAAA;MACA,KAAAuO,aAAA;IACA;IAEA,aACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,UAAAhR,YAAA,CAAAQ,cAAA;QACA,KAAAR,YAAA,CAAAY,SAAA;QACA,KAAAZ,YAAA,CAAAa,kBAAA,CAAAN,KAAA;QACA;MACA;MAEA,IAAAoF,WAAA;QACAtF,OAAA,OAAAL,YAAA,CAAAa,kBAAA,CAAAR,OAAA;QACAC,QAAA,OAAAN,YAAA,CAAAa,kBAAA,CAAAP,QAAA;QACAkC,MAAA,OAAAxC,YAAA,CAAAQ,cAAA;QACAC,YAAA,OAAAT,YAAA,CAAAS,YAAA,IAAAwB,SAAA;QACAvB,UAAA,OAAAV,YAAA,CAAAU,UAAA,IAAAuB,SAAA;QACAgP,eAAA,OAAAjR,YAAA,CAAAW,qBAAA,IAAAsB;MACA;MAEA,IAAAiP,sBAAA,EAAAvL,WAAA,EAAAQ,IAAA,WAAAiI,QAAA;QACA4C,OAAA,CAAAhR,YAAA,CAAAY,SAAA,GAAAwN,QAAA,CAAA3H,IAAA;QACAuK,OAAA,CAAAhR,YAAA,CAAAa,kBAAA,CAAAN,KAAA,GAAA6N,QAAA,CAAA7N,KAAA;MACA,GAAAwG,KAAA,WAAA6G,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAoD,OAAA,CAAA9L,QAAA,CAAA0I,KAAA;QACAoD,OAAA,CAAAhR,YAAA,CAAAY,SAAA;QACAoQ,OAAA,CAAAhR,YAAA,CAAAa,kBAAA,CAAAN,KAAA;MACA;IACA;IAEA,WACA4Q,eAAA,WAAAA,gBAAA;MACA,KAAAnR,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAA0Q,aAAA;IACA;IAEA,aACAK,mBAAA,WAAAA,oBAAA;MACA,KAAApR,YAAA,CAAAS,YAAA;MACA,KAAAT,YAAA,CAAAU,UAAA;MACA,KAAAV,YAAA,CAAAW,qBAAA;MACA,KAAAwQ,eAAA;IACA;IAEA,WACAE,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAArQ,qBAAA;MACA,KAAAsQ,oBAAA;MACA,KAAAzL,iBAAA;IACA;IAEA,mBACAyL,oBAAA,WAAAA,qBAAA;MACA,KAAArQ,gBAAA;MACA,KAAAC,eAAA;QACAC,QAAA;QACAC,YAAA;QACAC,aAAA;QACAC,qBAAA;MACA;IACA;IAEA,mBACAiQ,wBAAA,WAAAA,yBAAA;MACA,KAAA5P,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAF,QAAA,CAAAP,QAAA;MACA,KAAAM,iBAAA;MACA;MACA,KAAAkE,WAAA;QACAtF,OAAA;QACAC,QAAA;QACA0B,QAAA,EAAAC;MACA;MACA,KAAA2D,aAAA;MACA,KAAAC,iBAAA;IACA;IAEA,qBACA6C,0BAAA,WAAAA,2BAAA/F,IAAA;MACAA,IAAA,CAAAzE,UAAA,GAAAyE,IAAA,CAAAG,aAAA,GAAAH,IAAA,CAAAqF,gBAAA;IACA;IAEA,mBACAwJ,qBAAA,WAAAA,sBAAA7O,IAAA;MACA,IAAAwF,KAAA,QAAAlH,gBAAA,CAAAoL,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAlE,EAAA,KAAAzF,IAAA,CAAAyF,EAAA;MAAA;MACA,IAAAD,KAAA;QACA,KAAAlH,gBAAA,CAAAoI,MAAA,CAAAlB,KAAA;MACA;IACA;IAEA,iBACAsJ,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvN,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAAsN,SAAA;QAAA,IAAA7Q,iBAAA,EAAA8Q,GAAA;QAAA,WAAAxN,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAAsN,SAAA;UAAA,kBAAAA,SAAA,CAAApN,CAAA;YAAA;cAAA,MACAiN,OAAA,CAAAzQ,gBAAA,CAAA2B,MAAA;gBAAAiP,SAAA,CAAApN,CAAA;gBAAA;cAAA;cACAiN,OAAA,CAAAxM,QAAA,CAAAoC,OAAA;cAAA,OAAAuK,SAAA,CAAAlN,CAAA;YAAA;cAAAkN,SAAA,CAAArE,CAAA;cAAAqE,SAAA,CAAApN,CAAA;cAAA,OAMAiN,OAAA,CAAAI,yBAAA,CAAAJ,OAAA,CAAAzQ,gBAAA;YAAA;cAAAH,iBAAA,GAAA+Q,SAAA,CAAAnE,CAAA;cAAA,MAEA5M,iBAAA,CAAA8B,MAAA;gBAAAiP,SAAA,CAAApN,CAAA;gBAAA;cAAA;cACAiN,OAAA,CAAAxM,QAAA,CAAAoC,OAAA;cAAA,OAAAuK,SAAA,CAAAlN,CAAA;YAAA;cAIA;cACA7D,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA,EAAAyE,KAAA;gBACAuJ,OAAA,CAAArS,cAAA,CAAAmJ,IAAA;kBACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;kBACA4J,UAAA,EAAArO,QAAA,CAAAqO,UAAA;kBACAC,OAAA,EAAAtO,QAAA,CAAAuN,eAAA;kBACA/T,IAAA,EAAAwG,QAAA,CAAAjD,YAAA;kBACAC,UAAA,EAAAgD,QAAA,CAAAhD,UAAA;kBACAuR,OAAA,EAAAvO,QAAA,CAAAuO,OAAA;kBACAC,KAAA,EAAAxO,QAAA,CAAAwO,KAAA;kBAAA;kBACA/C,QAAA;kBACAgD,QAAA;gBACA;cACA;cAEAT,OAAA,CAAA1Q,qBAAA;cACA0Q,OAAA,CAAAxM,QAAA,CAAAwC,OAAA,2DAAA+G,MAAA,CAAA3N,iBAAA,CAAA8B,MAAA;cAAAiP,SAAA,CAAApN,CAAA;cAAA;YAAA;cAAAoN,SAAA,CAAArE,CAAA;cAAAoE,GAAA,GAAAC,SAAA,CAAAnE,CAAA;cAEAC,OAAA,CAAAC,KAAA,YAAAgE,GAAA;cACAF,OAAA,CAAAxM,QAAA,CAAA0I,KAAA;YAAA;cAAA,OAAAiE,SAAA,CAAAlN,CAAA;UAAA;QAAA,GAAAgN,QAAA;MAAA;IAEA;IAEA,eACAG,yBAAA,WAAAA,0BAAAtQ,KAAA;MAAA,IAAA4Q,OAAA;MAAA,WAAAjO,kBAAA,CAAA/G,OAAA,mBAAAgH,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAAgO,SAAA;QAAA,IAAAC,YAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,GAAA;QAAA,WAAAtO,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAAoO,SAAA;UAAA,kBAAAA,SAAA,CAAAlO,CAAA;YAAA;cACA6N,YAAA;cAAAC,UAAA,OAAA5H,2BAAA,CAAAvN,OAAA,EAEAoE,KAAA;cAAAmR,SAAA,CAAAnF,CAAA;cAAAiF,MAAA,oBAAArO,aAAA,CAAAhH,OAAA,IAAAiH,CAAA,UAAAoO,OAAA;gBAAA,IAAA9P,IAAA,EAAA/B,SAAA,EAAAgS,UAAA,EAAAC,MAAA,EAAAvQ,IAAA,EAAAqD,WAAA,EAAAyI,QAAA,EAAA0E,UAAA,EAAAC,MAAA,EAAAtS,YAAA,EAAAuS,YAAA,EAAAC,SAAA,EAAAC,QAAA,EAAApQ,aAAA,EAAAhC,iBAAA,EAAAqS,GAAA,EAAAC,GAAA,EAAAC,GAAA;gBAAA,WAAAjP,aAAA,CAAAhH,OAAA,IAAAmH,CAAA,WAAA+O,SAAA;kBAAA,kBAAAA,SAAA,CAAA7O,CAAA;oBAAA;sBAAA9B,IAAA,GAAA6P,MAAA,CAAA7S,KAAA;sBAAA2T,SAAA,CAAA9F,CAAA;sBAEA5M,SAAA;sBAAA,MAEA+B,IAAA,CAAAzF,IAAA;wBAAAoW,SAAA,CAAA7O,CAAA;wBAAA;sBAAA;sBACA;sBAAAmO,UAAA,OAAAjI,2BAAA,CAAAvN,OAAA,EACAuF,IAAA,CAAAR,aAAA;sBAAAmR,SAAA,CAAA9F,CAAA;sBAAAoF,UAAA,CAAA/H,CAAA;oBAAA;sBAAA,KAAAgI,MAAA,GAAAD,UAAA,CAAAnO,CAAA,IAAAqG,IAAA;wBAAAwI,SAAA,CAAA7O,CAAA;wBAAA;sBAAA;sBAAAnC,IAAA,GAAAuQ,MAAA,CAAAlT,KAAA;sBACAgG,WAAA;wBACAnD,MAAA,EAAAF,IAAA,CAAAE,MAAA;wBACAnC,OAAA;wBACAC,QAAA;sBACA;sBAAAgT,SAAA,CAAA7O,CAAA;sBAAA,OAEA,IAAAyM,sBAAA,EAAAvL,WAAA;oBAAA;sBAAAyI,QAAA,GAAAkF,SAAA,CAAA5F,CAAA;sBACA,IAAAU,QAAA,CAAA3H,IAAA,IAAA2H,QAAA,CAAA3H,IAAA,CAAA7D,MAAA;wBACAhC,SAAA,GAAAA,SAAA,CAAA6N,MAAA,CAAAL,QAAA,CAAA3H,IAAA;sBACA;oBAAA;sBAAA6M,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA6O,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA6O,SAAA,CAAA9F,CAAA;sBAAA2F,GAAA,GAAAG,SAAA,CAAA5F,CAAA;sBAAAkF,UAAA,CAAA5H,CAAA,CAAAmI,GAAA;oBAAA;sBAAAG,SAAA,CAAA9F,CAAA;sBAAAoF,UAAA,CAAA3H,CAAA;sBAAA,OAAAqI,SAAA,CAAArI,CAAA;oBAAA;sBAAAqI,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA,MAEA9B,IAAA,CAAAzF,IAAA;wBAAAoW,SAAA,CAAA7O,CAAA;wBAAA;sBAAA;sBACA;sBAAAqO,UAAA,OAAAnI,2BAAA,CAAAvN,OAAA,EACAuF,IAAA,CAAArB,qBAAA;sBAAAgS,SAAA,CAAA9F,CAAA;sBAAAsF,UAAA,CAAAjI,CAAA;oBAAA;sBAAA,KAAAkI,MAAA,GAAAD,UAAA,CAAArO,CAAA,IAAAqG,IAAA;wBAAAwI,SAAA,CAAA7O,CAAA;wBAAA;sBAAA;sBAAAhE,YAAA,GAAAsS,MAAA,CAAApT,KAAA;sBACAgG,YAAA;wBACAlF,YAAA,EAAAA,YAAA;wBACAJ,OAAA;wBACAC,QAAA;sBACA;sBAAAgT,SAAA,CAAA7O,CAAA;sBAAA,OAEA,IAAAyM,sBAAA,EAAAvL,YAAA;oBAAA;sBAAAyI,SAAA,GAAAkF,SAAA,CAAA5F,CAAA;sBACA,IAAAU,SAAA,CAAA3H,IAAA,IAAA2H,SAAA,CAAA3H,IAAA,CAAA7D,MAAA;wBACAhC,SAAA,GAAAA,SAAA,CAAA6N,MAAA,CAAAL,SAAA,CAAA3H,IAAA;sBACA;oBAAA;sBAAA6M,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA6O,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA6O,SAAA,CAAA9F,CAAA;sBAAA4F,GAAA,GAAAE,SAAA,CAAA5F,CAAA;sBAAAoF,UAAA,CAAA9H,CAAA,CAAAoI,GAAA;oBAAA;sBAAAE,SAAA,CAAA9F,CAAA;sBAAAsF,UAAA,CAAA7H,CAAA;sBAAA,OAAAqI,SAAA,CAAArI,CAAA;oBAAA;sBAIA;sBACA,IAAArK,SAAA,CAAAgC,MAAA;wBACAsQ,QAAA,GAAAd,OAAA,CAAAmB,YAAA,KAAA3L,mBAAA,CAAAxK,OAAA,EAAAwD,SAAA;wBACAkC,aAAA,GAAA0Q,IAAA,CAAAC,GAAA,CAAA9Q,IAAA,CAAAG,aAAA,EAAAoQ,QAAA,CAAAtQ,MAAA;wBACA9B,iBAAA,GAAAoS,QAAA,CAAAQ,KAAA,IAAA5Q,aAAA,GAEA;wBACAhC,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA;0BACAA,QAAA,CAAAwO,KAAA,GAAAvP,IAAA,CAAAqF,gBAAA;wBACA;wBAEAsK,YAAA,CAAA9J,IAAA,CAAAsE,KAAA,CAAAwF,YAAA,MAAA1K,mBAAA,CAAAxK,OAAA,EAAA0D,iBAAA;sBACA;sBAAAwS,SAAA,CAAA7O,CAAA;sBAAA;oBAAA;sBAAA6O,SAAA,CAAA9F,CAAA;sBAAA6F,GAAA,GAAAC,SAAA,CAAA5F,CAAA;sBAEAC,OAAA,CAAAC,KAAA,YAAAyF,GAAA;oBAAA;sBAAA,OAAAC,SAAA,CAAA3O,CAAA;kBAAA;gBAAA,GAAA8N,MAAA;cAAA;cAAAF,UAAA,CAAA1H,CAAA;YAAA;cAAA,KAAA2H,MAAA,GAAAD,UAAA,CAAA9N,CAAA,IAAAqG,IAAA;gBAAA6H,SAAA,CAAAlO,CAAA;gBAAA;cAAA;cAAA,OAAAkO,SAAA,CAAA7D,CAAA,KAAAC,mBAAA,CAAA3R,OAAA,EAAAqV,MAAA;YAAA;cAAAE,SAAA,CAAAlO,CAAA;cAAA;YAAA;cAAAkO,SAAA,CAAAlO,CAAA;cAAA;YAAA;cAAAkO,SAAA,CAAAnF,CAAA;cAAAkF,GAAA,GAAAC,SAAA,CAAAjF,CAAA;cAAA6E,UAAA,CAAAvH,CAAA,CAAA0H,GAAA;YAAA;cAAAC,SAAA,CAAAnF,CAAA;cAAA+E,UAAA,CAAAtH,CAAA;cAAA,OAAA0H,SAAA,CAAA1H,CAAA;YAAA;cAAA,OAAA0H,SAAA,CAAAhO,CAAA,IAIA2N,YAAA;UAAA;QAAA,GAAAD,QAAA;MAAA;IACA;IAEA,aACAkB,YAAA,WAAAA,aAAAI,KAAA;MACA,SAAAC,CAAA,GAAAD,KAAA,CAAA/Q,MAAA,MAAAgR,CAAA,MAAAA,CAAA;QACA,IAAAC,CAAA,GAAAL,IAAA,CAAAM,KAAA,CAAAN,IAAA,CAAAO,MAAA,MAAAH,CAAA;QAAA,IAAAI,KAAA,GACA,CAAAL,KAAA,CAAAE,CAAA,GAAAF,KAAA,CAAAC,CAAA;QAAAD,KAAA,CAAAC,CAAA,IAAAI,KAAA;QAAAL,KAAA,CAAAE,CAAA,IAAAG,KAAA;MACA;MACA,OAAAL,KAAA;IACA;IAEA,SACAM,UAAA,WAAAA,WAAA;MACA,KAAA/O,QAAA,CAAAC,IAAA;IACA;IAEA,aACA+O,mBAAA,WAAAA,oBAAA;MACA,KAAAhP,QAAA,CAAAC,IAAA;IACA;IAEA,SACAgP,YAAA,WAAAA,aAAA;MACA,KAAAjP,QAAA,CAAAC,IAAA;IACA;IAEA,YACAiP,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAA9U,UAAA,SAAAA,UAAA;MACA;MACA,KAAAF,cAAA,CAAAqD,OAAA,WAAAgB,QAAA;QACAA,QAAA,CAAAyO,QAAA,GAAAkC,OAAA,CAAA9U,UAAA;MACA;MACA,KAAA2F,QAAA,CAAAC,IAAA,UAAAsJ,MAAA,MAAAlP,UAAA;IACA;IAEA,aACA+U,6BAAA,WAAAA,8BAAAtL,SAAA;MACA,KAAAhJ,YAAA,CAAAc,iBAAA,GAAAkI,SAAA;MACA,KAAAuL,mBAAA;IACA;IAEA,eACAA,mBAAA,WAAAA,oBAAA;MACA,IAAA9Q,KAAA;MACA,KAAAzD,YAAA,CAAAc,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA;QACA,IAAAxG,IAAA,GAAAwG,QAAA,CAAAjD,YAAA;QACAgD,KAAA,CAAAvG,IAAA,KAAAuG,KAAA,CAAAvG,IAAA;MACA;MACA,KAAA8C,YAAA,CAAAe,aAAA,GAAA0C,KAAA;IACA;IAEA,aACA+Q,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,SAAAzU,YAAA,CAAAc,iBAAA,CAAA8B,MAAA;QACA,KAAAsC,QAAA,CAAAoC,OAAA;QACA;MACA;;MAEA;MACA,KAAAtH,YAAA,CAAAc,iBAAA,CAAA4B,OAAA,WAAAgB,QAAA,EAAAyE,KAAA;QACAsM,OAAA,CAAApV,cAAA,CAAAmJ,IAAA;UACAJ,EAAA,EAAAC,IAAA,CAAAC,GAAA,KAAAH,KAAA;UACA4J,UAAA,EAAArO,QAAA,CAAAqO,UAAA;UACAC,OAAA,EAAAtO,QAAA,CAAAuN,eAAA;UACA/T,IAAA,EAAAwG,QAAA,CAAAjD,YAAA;UACAC,UAAA,EAAAgD,QAAA,CAAAhD,UAAA;UACAuR,OAAA,EAAAvO,QAAA,CAAAuO,OAAA;UAAA;UACAC,KAAA;UAAA;UACA/C,QAAA;UACAgD,QAAA;QACA;MACA;MAEA,KAAA3S,sBAAA;MACA,KAAA0F,QAAA,CAAAwC,OAAA,6BAAA+G,MAAA,MAAAzO,YAAA,CAAAc,iBAAA,CAAA8B,MAAA;IACA;IAEA,eACA8R,oBAAA,WAAAA,qBAAA5Q,GAAA;MACA,KAAA9D,YAAA,CAAAI,cAAA,CAAAE,QAAA,GAAAwD,GAAA;MACA,KAAA9D,YAAA,CAAAI,cAAA,CAAAC,OAAA;MACA,KAAAqP,6BAAA;IACA;IAEA,cACAiF,uBAAA,WAAAA,wBAAA7Q,GAAA;MACA,KAAA9D,YAAA,CAAAI,cAAA,CAAAC,OAAA,GAAAyD,GAAA;MACA,KAAA4L,6BAAA;IACA;IAEA,eACAkF,wBAAA,WAAAA,yBAAA9Q,GAAA;MACA,KAAA9D,YAAA,CAAAa,kBAAA,CAAAP,QAAA,GAAAwD,GAAA;MACA,KAAA9D,YAAA,CAAAa,kBAAA,CAAAR,OAAA;MACA,KAAA0Q,aAAA;IACA;IAEA,cACA8D,2BAAA,WAAAA,4BAAA/Q,GAAA;MACA,KAAA9D,YAAA,CAAAa,kBAAA,CAAAR,OAAA,GAAAyD,GAAA;MACA,KAAAiN,aAAA;IACA;IAEA,aACA+D,iBAAA,WAAAA,kBAAApU,UAAA;MACA,IAAAqU,aAAA,OAAAtX,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA,MAAAK,gBAAA,CAAAL,OAAA;QAAA;QAAA;QAAA;MAAA;MACA,OAAA2X,aAAA,CAAArU,UAAA;IACA;IAEA,aACAsU,mBAAA,WAAAA,oBAAAtR,QAAA;MACA;MACA,KAAAuR,YAAA;IACA;IAEA,kBACAC,oBAAA,WAAAA,qBAAAxR,QAAA;MACAA,QAAA,CAAAyO,QAAA,IAAAzO,QAAA,CAAAyO,QAAA;IACA;IAEA,aACAgD,cAAA,WAAAA,eAAAzR,QAAA,EAAAyE,KAAA;MAAA,IAAAiN,OAAA;MACA,KAAAlJ,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAlP,IAAA;MACA,GAAAiJ,IAAA;QACAiP,OAAA,CAAA/V,cAAA,CAAAgK,MAAA,CAAAlB,KAAA;QACAiN,OAAA,CAAAlQ,QAAA,CAAAwC,OAAA;MACA,GAAAX,KAAA;QACA;MAAA,CACA;IACA;IAEA,kBACAsO,YAAA,WAAAA,aAAAC,UAAA;MACA;QACA,WAAAA,UAAA;UACA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,UAAA;QACA;QACA,OAAAA,UAAA;MACA,SAAA1H,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACA;MACA;IACA;IAEA,gBACA6H,oBAAA,WAAAA,qBAAAzI,GAAA;MACA;MACA,aAAA3N,cAAA,CAAAqW,IAAA,WAAAhS,QAAA;QAAA,OAAAA,QAAA,CAAAqO,UAAA,KAAA/E,GAAA,CAAA+E,UAAA;MAAA;IACA;IAEA,iBACA4D,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,IAAA5I,GAAA,GAAA4I,KAAA,CAAA5I,GAAA;MACA;MACA,SAAA3N,cAAA,CAAAqW,IAAA,WAAAhS,QAAA;QAAA,OAAAA,QAAA,CAAAqO,UAAA,KAAA/E,GAAA,CAAA+E,UAAA;MAAA;QACA;MACA;MACA;IACA;IAEA,YACA8D,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAA5Y,IAAA,CAAAkM,OAAA;MACA,IAAA4M,MAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,OAAA;QACA,KAAA7Q,QAAA,CAAA0I,KAAA;QACA;MACA;MACA,KAAAoI,MAAA;QACA,KAAA9Q,QAAA,CAAA0I,KAAA;QACA;MACA;MACA;IACA;IAEA,YACAsI,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAAhZ,MAAA,CAAA6Y,IAAA,CAAAI,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAnZ,MAAA,CAAA6Y,IAAA,CAAAO,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAArZ,MAAA,CAAA6Y,IAAA,CAAAS,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAvZ,MAAA,CAAA6Y,IAAA,CAAAW,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAzZ,MAAA,CAAA6Y,IAAA,CAAAa,UAAA,IAAAR,QAAA;MACA,UAAA/H,MAAA,CAAA2H,IAAA,OAAA3H,MAAA,CAAA6H,KAAA,OAAA7H,MAAA,CAAAgI,GAAA,OAAAhI,MAAA,CAAAkI,KAAA,OAAAlI,MAAA,CAAAoI,OAAA,OAAApI,MAAA,CAAAsI,OAAA;IACA;IAEA,aACAhT,aAAA,WAAAA,cAAA;MACA,SAAA1G,OAAA;QACA;QACAsQ,OAAA,CAAAa,GAAA,iBAAAnR,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}