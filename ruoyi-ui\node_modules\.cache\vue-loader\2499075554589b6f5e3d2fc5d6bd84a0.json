{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754447197993}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RRdWVzdGlvbkJhbmsgfSBmcm9tICJAL2FwaS9iaXovcXVlc3Rpb25CYW5rIgppbXBvcnQgeyBsaXN0Q2F0ZWdvcnkgfSBmcm9tICJAL2FwaS9iaXovY2F0ZWdvcnkiCmltcG9ydCB7IGdldFF1ZXN0aW9uU3RhdGlzdGljcywgbGlzdFF1ZXN0aW9uIH0gZnJvbSAiQC9hcGkvYml6L3F1ZXN0aW9uIgoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJQYXBlckNyZWF0ZSIsCiAgcHJvcHM6IHsKICAgIHZpc2libGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICBwYXBlcklkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDlj7PkvqfpnaLmnb/nirbmgIEKICAgICAgcmlnaHRQYW5lbENvbGxhcHNlZDogZmFsc2UsCiAgICAgIHJpZ2h0UGFuZWxXaWR0aDogNDEwLAogICAgICAKICAgICAgLy8g5oqY5Y+g6Z2i5p2/5r+A5rS76aG577yIYWNjb3JkaW9u5qih5byP5LiL5Li65a2X56ym5Liy77yJCiAgICAgIGFjdGl2ZUNvbGxhcHNlOiAnYmFzaWMnLAogICAgICAKICAgICAgLy8g6K+V5Y236KGo5Y2V5pWw5o2uCiAgICAgIHBhcGVyRm9ybTogewogICAgICAgIHBhcGVySWQ6IG51bGwsCiAgICAgICAgcGFwZXJOYW1lOiAnJywKICAgICAgICBwYXBlckRlc2M6ICcnLAogICAgICAgIHBhcGVyVHlwZTogMSwgLy8gMDog5Zu65a6a6K+V5Y23LCAxOiDpmo/mnLror5XljbcKICAgICAgICBjb3ZlckltZzogJycsCiAgICAgICAgdG90YWxTY29yZTogMTAwLAogICAgICAgIHBhc3NTY29yZTogNjAsCiAgICAgICAgc3RhcnRUaW1lOiBudWxsLAogICAgICAgIGVuZFRpbWU6IG51bGwsCiAgICAgICAgZHVyYXRpb246IDkwLCAvLyDogIPor5Xml7bplb/vvIzliIbpkp8KICAgICAgICBsYXRlTGltaXQ6IDAsIC8vIOi/n+WIsOmZkOWItu+8jOWIhumSnwogICAgICAgIGFsbG93RWFybHlTdWJtaXQ6IDAsIC8vIOaYr+WQpuWFgeiuuOaPkOWJjeS6pOWNtwogICAgICAgIGVhcmx5U3VibWl0VGltZTogNDAsIC8vIOaPkOWJjeS6pOWNt+aXtumXtO+8jOWIhumSnwogICAgICAgIHNob3dTY29yZTogMCwgLy8g5piv5ZCm5pi+56S65YiG5YC8CiAgICAgICAgc2hvd1R5cGU6IDAsIC8vIOaYr+WQpuaYvuekuumimOWeiwogICAgICAgIHJlcXVpcmVBbGxBbnN3ZXJlZDogMCwgLy8g5piv5ZCm6KaB5rGC5YWo6YOo562U5a6M5omN6IO95Lqk5Y23CiAgICAgICAgc2hvd1Jlc3VsdDogMCwgLy8g5piv5ZCm5pi+56S65oiQ57upCiAgICAgICAgc2hvd0NvcnJlY3Q6IDAsIC8vIOaYr+WQpuaYvuekuuWvuemUmQogICAgICAgIHNob3dBbnN3ZXI6IDAsIC8vIOaYr+WQpuaYvuekuuetlOahiAogICAgICAgIHNob3dBbmFseXNpczogMCwgLy8g5piv5ZCm5pi+56S66Kej5p6QCiAgICAgICAgc3RhdHVzOiAwLCAvLyDnirbmgIHvvJow5pyq5Y+R5biDIDHlt7Llj5HluIMKICAgICAgICBlbmFibGVUaW1lTGltaXQ6IDAsIC8vIOaYr+WQpuWQr+eUqOiAg+ivleaXtumXtOmZkOWItgogICAgICAgIGR1cmF0aW9uU2Vjb25kczogMCwgLy8g5pe26ZW/56eS5pWwCiAgICAgICAgY3JlYXRlVGltZTogbnVsbAogICAgICB9LAogICAgICAKICAgICAgLy8g57uf6K6h5pWw5o2u77yI5rOo77ya6aKY55uu5pWw6YeP5ZKM5oC75YiG546w5Zyo6YCa6L+H6K6h566X5bGe5oCndG90YWxRdWVzdGlvbnPlkox0b3RhbFJ1bGVTY29yZeWKqOaAgeiuoeeul++8iQoKICAgICAgLy8g5Zu65a6a6K+V5Y2355u45YWz5pWw5o2uCiAgICAgIGZpeGVkUXVlc3Rpb25zOiBbXSwgLy8g5Zu65a6a6K+V5Y2355qE6aKY55uu5YiX6KGoCiAgICAgIHNlbGVjdEFsbDogZmFsc2UsIC8vIOWFqOmAieeKtuaAgQogICAgICBpc0V4cGFuZGVkOiBmYWxzZSwgLy8g5bGV5byA54q25oCBCgogICAgICAvLyDmiYvliqjpgInpopjlr7nor53moYYKICAgICAgc2hvd01hbnVhbFNlbGVjdERpYWxvZzogZmFsc2UsCiAgICAgIGNhdGVnb3J5T3B0aW9uczogW10sIC8vIOWIhuexu+mAiemhueaVsOaNrgogICAgICBjYXNjYWRlclByb3BzOiB7CiAgICAgICAgdmFsdWU6ICdpZCcsCiAgICAgICAgbGFiZWw6ICduYW1lJywKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICBjaGVja1N0cmljdGx5OiBmYWxzZSwKICAgICAgICBlbWl0UGF0aDogZmFsc2UKICAgICAgfSwKICAgICAgbWFudWFsU2VsZWN0OiB7CiAgICAgICAgc2VsZWN0ZWRDYXRlZ29yeTogJycsIC8vIOmAieaLqeeahOmimOW6k+ebruW9lQogICAgICAgIGJhbmtTZWFyY2hLZXl3b3JkOiAnJywgLy8g6aKY5bqT5pCc57Si5YWz6ZSu6K+NCiAgICAgICAgcXVlc3Rpb25CYW5rczogW10sIC8vIOmimOW6k+WIl+ihqAogICAgICAgIGJhbmtQYWdpbmF0aW9uOiB7CiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgdG90YWw6IDAKICAgICAgICB9LAogICAgICAgIHNlbGVjdGVkQmFua0lkOiBudWxsLCAvLyDpgInmi6nnmoTpopjlupNJRAogICAgICAgIHF1ZXN0aW9uVHlwZTogJycsIC8vIOmimOWei+etm+mAiQogICAgICAgIGRpZmZpY3VsdHk6ICcnLCAvLyDpmr7luqbnrZvpgIkKICAgICAgICBxdWVzdGlvblNlYXJjaEtleXdvcmQ6ICcnLCAvLyDpopjnm67mkJzntKLlhbPplK7or40KICAgICAgICBxdWVzdGlvbnM6IFtdLCAvLyDpopjnm67liJfooagKICAgICAgICBxdWVzdGlvblBhZ2luYXRpb246IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICB0b3RhbDogMAogICAgICAgIH0sCiAgICAgICAgc2VsZWN0ZWRRdWVzdGlvbnM6IFtdLCAvLyDpgInkuK3nmoTpopjnm64KICAgICAgICBzZWxlY3RlZFN0YXRzOiB7fSAvLyDpgInkuK3popjnm67nmoTnu5/orqHkv6Hmga8KICAgICAgfSwKCiAgICAgIC8vIOWbuuWumuivleWNt+maj+acuuaKvemimOWvueivneahhgogICAgICBzaG93Rml4ZWRSYW5kb21EaWFsb2c6IGZhbHNlLAoKICAgICAgLy8g5Zu65a6a6K+V5Y236ZqP5py65oq96aKY55u45YWz5pWw5o2uCiAgICAgIGZpeGVkUmFuZG9tUnVsZXM6IFtdLCAvLyDkuLTml7bop4TliJnliJfooajvvIznlKjkuo7lm7rlrpror5Xljbfpmo/mnLrmir3popgKICAgICAgZml4ZWRSYW5kb21Gb3JtOiB7CiAgICAgICAgcnVsZVR5cGU6IDMsIC8vIDE66aKY5Z6LIDM66aKY5bqTCiAgICAgICAgZ2VuZXJhdGVUeXBlOiAnZGl2aWRlZCcsIC8vIG9uZTrmlbTkvZPorr7nva4gZGl2aWRlZDrliIblvIDorr7nva4KICAgICAgICBzZWxlY3RlZEl0ZW1zOiBbXSwKICAgICAgICBzZWxlY3RlZFF1ZXN0aW9uVHlwZXM6IFtdIC8vIOmAieaLqeeahOmimOWeiwogICAgICB9LAoKICAgICAgLy8g5om56YeP6K6+572u5YiG5pWw5a+56K+d5qGGCiAgICAgIHNob3dCYXRjaFNjb3JlRGlhbG9nOiBmYWxzZSwKICAgICAgYmF0Y2hTY29yZUZvcm06IHsKICAgICAgICBzY29yZVR5cGU6ICdhbGwnLCAvLyBhbGw6IOWFqOmDqOmimOebriwgc2VsZWN0ZWQ6IOmAieS4remimOebriwgYnlUeXBlOiDmjInpopjlnosKICAgICAgICBzY29yZTogMSwgLy8g5YiG5pWw5YC8CiAgICAgICAgcXVlc3Rpb25UeXBlOiAnJyAvLyDpopjlnovvvIjlvZNzY29yZVR5cGXkuLpieVR5cGXml7bkvb/nlKjvvIkKICAgICAgfSwKCiAgICAgIC8vIOmaj+acuuinhOWImeWvueivneahhgogICAgICBzaG93UnVsZURpYWxvZzogZmFsc2UsCiAgICAgIC8vIOinhOWImeWIl+ihqAogICAgICBydWxlczogW10sCgogICAgICAvLyDmt7vliqDop4TliJnlr7nor53moYYKICAgICAgc2hvd0FkZFJ1bGVEaWFsb2c6IGZhbHNlLAogICAgICBydWxlRm9ybTogewogICAgICAgIHJ1bGVUeXBlOiAzLCAvLyAxOumimOWeiyAzOumimOW6kwogICAgICAgIGdlbmVyYXRlVHlwZTogJ2RpdmlkZWQnLCAvLyBvbmU65pW05L2T6K6+572uIGRpdmlkZWQ65YiG5byA6K6+572uCiAgICAgICAgc2VsZWN0ZWRJdGVtczogW10sCiAgICAgICAgc2VsZWN0ZWRRdWVzdGlvblR5cGVzOiBbXSAvLyDpgInmi6nnmoTpopjlnosKICAgICAgfSwKICAgICAgLy8g5b2T5YmN5pON5L2c54q25oCBCiAgICAgIGN1cnJlbnRPcGVyYXRpb246ICdhZGQnLCAvLyBhZGQ6IOa3u+WKoCwgZWRpdDog57yW6L6RLCBhZGRTdWI6IOa3u+WKoOWtkOinhOWImQogICAgICBlZGl0aW5nUnVsZTogbnVsbCwgLy8g5q2j5Zyo57yW6L6R55qE6KeE5YiZCiAgICAgIHBhcmVudFJ1bGU6IG51bGwsIC8vIOeItuinhOWIme+8iOa3u+WKoOWtkOinhOWImeaXtuS9v+eUqO+8iQogICAgICBjdXJyZW50UXVlc3Rpb25UeXBlQ291bnQ6IDAsIC8vIOW9k+WJjemAieaLqemimOWei+eahOWPr+eUqOmimOebruaVsOmHjwogICAgICBxdWVzdGlvbkJhbmtzOiBbXSwKICAgICAgcXVlc3Rpb25CYW5rTG9hZGluZzogZmFsc2UsCiAgICAgIGNhdGVnb3J5T3B0aW9uczogW10sCiAgICAgIHNlYXJjaEtleXdvcmQ6ICcnLAogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgcGFnZVNpemU6IDEwLAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDlt7LpgInmi6nnmoTpopjlupMKICAgIHNlbGVjdGVkQmFua3MoKSB7CiAgICAgIHJldHVybiB0aGlzLnF1ZXN0aW9uQmFua3MuZmlsdGVyKGJhbmsgPT4KICAgICAgICB0aGlzLnJ1bGVGb3JtLnNlbGVjdGVkSXRlbXMuaW5jbHVkZXMoYmFuay5iYW5rSWQpCiAgICAgICkKICAgIH0sCgogICAgLy8g6K6h566X5oC76aKY55uu5pWw6YePCiAgICB0b3RhbFF1ZXN0aW9ucygpIHsKICAgICAgbGV0IHRvdGFsID0gMAogICAgICB0aGlzLnJ1bGVzLmZvckVhY2gocnVsZSA9PiB7CiAgICAgICAgaWYgKHJ1bGUuY2hpbGRyZW4gJiYgcnVsZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7CiAgICAgICAgICAvLyDlpoLmnpzmnInlrZDop4TliJnvvIzorqHnrpflrZDop4TliJnnmoTpopjnm67mlbAKICAgICAgICAgIHJ1bGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiB7CiAgICAgICAgICAgIHRvdGFsICs9IGNoaWxkLnNlbGVjdGVkQ291bnQgfHwgMAogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5a2Q6KeE5YiZ77yM6K6h566X54i26KeE5YiZ55qE6aKY55uu5pWwCiAgICAgICAgICB0b3RhbCArPSBydWxlLnNlbGVjdGVkQ291bnQgfHwgMAogICAgICAgIH0KICAgICAgfSkKICAgICAgcmV0dXJuIHRvdGFsCiAgICB9LAoKICAgIC8vIOiuoeeul+aAu+WIhuaVsAogICAgdG90YWxSdWxlU2NvcmUoKSB7CiAgICAgIGxldCB0b3RhbCA9IDAKICAgICAgdGhpcy5ydWxlcy5mb3JFYWNoKHJ1bGUgPT4gewogICAgICAgIGlmIChydWxlLmNoaWxkcmVuICYmIHJ1bGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgewogICAgICAgICAgLy8g5aaC5p6c5pyJ5a2Q6KeE5YiZ77yM6K6h566X5a2Q6KeE5YiZ55qE5oC75YiGCiAgICAgICAgICBydWxlLmNoaWxkcmVuLmZvckVhY2goY2hpbGQgPT4gewogICAgICAgICAgICB0b3RhbCArPSBjaGlsZC50b3RhbFNjb3JlIHx8IDAKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOWmguaenOayoeacieWtkOinhOWIme+8jOiuoeeul+eItuinhOWImeeahOaAu+WIhgogICAgICAgICAgdG90YWwgKz0gcnVsZS50b3RhbFNjb3JlIHx8IDAKICAgICAgICB9CiAgICAgIH0pCiAgICAgIHJldHVybiB0b3RhbAogICAgfSwKCiAgICAvLyDojrflj5bniLbop4TliJnliJfooajvvIjlj6rljIXlkKvmsqHmnIlwYXJlbnRJZOeahOinhOWIme+8iQogICAgcGFyZW50UnVsZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLnJ1bGVzLmZpbHRlcihydWxlID0+ICFydWxlLnBhcmVudElkKQogICAgfSwKCiAgICAvLyDojrflj5blt7Lkvb/nlKjnmoTpopjlupNJROWIl+ihqAogICAgdXNlZEJhbmtJZHMoKSB7CiAgICAgIGNvbnN0IHVzZWRJZHMgPSBuZXcgU2V0KCkKICAgICAgdGhpcy5ydWxlcy5mb3JFYWNoKHJ1bGUgPT4gewogICAgICAgIGlmIChydWxlLnR5cGUgPT09IDMgJiYgcnVsZS5zZWxlY3RlZEl0ZW1zKSB7CiAgICAgICAgICBydWxlLnNlbGVjdGVkSXRlbXMuZm9yRWFjaChiYW5rSWQgPT4gewogICAgICAgICAgICB1c2VkSWRzLmFkZChiYW5rSWQpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgcmV0dXJuIEFycmF5LmZyb20odXNlZElkcykKICAgIH0sCgogICAgLy8g6K6h566X5bey5re75Yqg5Yiw6K+V5Y2355qE6aKY55uu57uf6K6hCiAgICBmaXhlZFF1ZXN0aW9uU3RhdHMoKSB7CiAgICAgIGNvbnN0IHN0YXRzID0ge30KICAgICAgdGhpcy5maXhlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICBjb25zdCB0eXBlID0gcXVlc3Rpb24udHlwZQogICAgICAgIHN0YXRzW3R5cGVdID0gKHN0YXRzW3R5cGVdIHx8IDApICsgMQogICAgICB9KQogICAgICByZXR1cm4gc3RhdHMKICAgIH0sCgogICAgLy8g5Zu65a6a6K+V5Y236ZqP5py65oq96aKY5oC76aKY5pWwCiAgICBmaXhlZFJhbmRvbVRvdGFsUXVlc3Rpb25zKCkgewogICAgICBsZXQgdG90YWwgPSAwCiAgICAgIHRoaXMuZml4ZWRSYW5kb21SdWxlcy5mb3JFYWNoKHJ1bGUgPT4gewogICAgICAgIHRvdGFsICs9IHJ1bGUuc2VsZWN0ZWRDb3VudCB8fCAwCiAgICAgIH0pCiAgICAgIHJldHVybiB0b3RhbAogICAgfSwKCiAgICAvLyDlm7rlrpror5Xljbfpmo/mnLrmir3popjmgLvliIbmlbAKICAgIGZpeGVkUmFuZG9tVG90YWxTY29yZSgpIHsKICAgICAgbGV0IHRvdGFsID0gMAogICAgICB0aGlzLmZpeGVkUmFuZG9tUnVsZXMuZm9yRWFjaChydWxlID0+IHsKICAgICAgICB0b3RhbCArPSBydWxlLnRvdGFsU2NvcmUgfHwgMAogICAgICB9KQogICAgICByZXR1cm4gdG90YWwKICAgIH0sCgogICAgLy8g6I635Y+W5Zu65a6a6K+V5Y236ZqP5py65oq96aKY54i26KeE5YiZ5YiX6KGo77yI5Y+q5YyF5ZCr5rKh5pyJcGFyZW50SWTnmoTop4TliJnvvIkKICAgIGZpeGVkUmFuZG9tUGFyZW50UnVsZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLmZpeGVkUmFuZG9tUnVsZXMuZmlsdGVyKHJ1bGUgPT4gIXJ1bGUucGFyZW50SWQpCiAgICB9LAoKICAgIC8vIOiOt+WPluWbuuWumuivleWNt+maj+acuuaKvemimOW3suS9v+eUqOeahOmimOW6k0lE5YiX6KGoCiAgICB1c2VkRml4ZWRSYW5kb21CYW5rSWRzKCkgewogICAgICBjb25zdCB1c2VkSWRzID0gbmV3IFNldCgpCiAgICAgIHRoaXMuZml4ZWRSYW5kb21SdWxlcy5mb3JFYWNoKHJ1bGUgPT4gewogICAgICAgIGlmIChydWxlLnR5cGUgPT09IDMgJiYgcnVsZS5zZWxlY3RlZEl0ZW1zKSB7CiAgICAgICAgICBydWxlLnNlbGVjdGVkSXRlbXMuZm9yRWFjaChiYW5rSWQgPT4gewogICAgICAgICAgICB1c2VkSWRzLmFkZChiYW5rSWQpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgICAgcmV0dXJuIEFycmF5LmZyb20odXNlZElkcykKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB2aXNpYmxlKHZhbCkgewogICAgICBpZiAodmFsICYmIHRoaXMucGFwZXJJZCkgewogICAgICAgIHRoaXMubG9hZFBhcGVyRGF0YSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g55uR5ZCs6aKY5Z6L6YCJ5oup5Y+Y5YyWCiAgICAncnVsZUZvcm0uc2VsZWN0ZWRRdWVzdGlvblR5cGVzJzogewogICAgICBhc3luYyBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAxICYmIG5ld1ZhbCAmJiBuZXdWYWwubGVuZ3RoID4gMCkgewogICAgICAgICAgYXdhaXQgdGhpcy51cGRhdGVRdWVzdGlvblR5cGVDb3VudCgpCiAgICAgICAgfQogICAgICB9LAogICAgICBkZWVwOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog6L+U5Zue6K+V5Y235YiX6KGoICovCiAgICBoYW5kbGVCYWNrKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpCiAgICB9LAogICAgCiAgICAvKiog5YiH5o2i5Y+z5L6n6Z2i5p2/ICovCiAgICB0b2dnbGVSaWdodFBhbmVsKCkgewogICAgICB0aGlzLnJpZ2h0UGFuZWxDb2xsYXBzZWQgPSAhdGhpcy5yaWdodFBhbmVsQ29sbGFwc2VkCiAgICAgIHRoaXMucmlnaHRQYW5lbFdpZHRoID0gdGhpcy5yaWdodFBhbmVsQ29sbGFwc2VkID8gNTAgOiA0MDAKICAgIH0sCiAgICAKICAgIC8qKiDogIPor5XlhaXlj6MgKi8KICAgIGhhbmRsZUV4YW1FbnRyeSgpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfogIPor5XlhaXlj6Plip/og73lvIDlj5HkuK0uLi4nKQogICAgfSwKICAgIAogICAgLyoqIOiuvue9ruiAg+ivlemhtemdoiAqLwogICAgaGFuZGxlUGFnZVNldHRpbmcoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn6K6+572u6ICD6K+V6aG16Z2i5Yqf6IO95byA5Y+R5LitLi4uJykKICAgIH0sCiAgICAKICAgIC8qKiDlj5HluIPor5XljbcgKi8KICAgIGhhbmRsZVB1Ymxpc2goKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5Y+R5biD6K+V5Y235Yqf6IO95byA5Y+R5LitLi4uJykKICAgIH0sCiAgICAKICAgIC8qKiDpgoDor7fogIPnlJ8gKi8KICAgIGhhbmRsZUludml0ZVN0dWRlbnRzKCkgewogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+mCgOivt+iAg+eUn+WKn+iDveW8gOWPkeS4rS4uLicpCiAgICB9LAogICAgCiAgICAvKiog5bey6YKA6K+35YiX6KGoICovCiAgICBoYW5kbGVJbnZpdGVMaXN0KCkgewogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3sumCgOivt+WIl+ihqOWKn+iDveW8gOWPkeS4rS4uLicpCiAgICB9LAogICAgCgogICAgCiAgICAvKiog5re75Yqg6KeE5YiZ77yI6ZqP5py66K+V5Y2377yJICovCiAgICBoYW5kbGVBZGRSdWxlKCkgewogICAgICB0aGlzLnNob3dSdWxlRGlhbG9nID0gdHJ1ZQogICAgfSwKCiAgICAvKiog57yW6L6R6KeE5YiZ77yI5omT5byA6KeE5YiZ57yW6L6R55WM6Z2i77yJICovCiAgICBoYW5kbGVFZGl0UnVsZXMoKSB7CiAgICAgIHRoaXMuc2hvd1J1bGVEaWFsb2cgPSB0cnVlCiAgICB9LAoKICAgIC8qKiDmt7vliqDkuIDnuqfop4TliJkgKi8KICAgIGhhbmRsZUFkZEZpcnN0UnVsZSgpIHsKICAgICAgdGhpcy5jdXJyZW50T3BlcmF0aW9uID0gJ2FkZCcKICAgICAgdGhpcy5lZGl0aW5nUnVsZSA9IG51bGwKCiAgICAgIC8vIOWmguaenOW3suacieinhOWIme+8jOmZkOWItuWPquiDvemAieaLqeebuOWQjOexu+WeiwogICAgICBpZiAodGhpcy5ydWxlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9IHRoaXMucnVsZXNbMF0udHlwZQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucnVsZUZvcm0ucnVsZVR5cGUgPSAzIC8vIOm7mOiupOmimOW6kwogICAgICB9CgogICAgICB0aGlzLnNob3dBZGRSdWxlRGlhbG9nID0gdHJ1ZQogICAgICAvLyDph43nva7mn6Xor6Llj4LmlbAKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gJycKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDliqDovb3popjlupPliJfooaggKi8KICAgIGxvYWRRdWVzdGlvbkJhbmtzKCkgewogICAgICB0aGlzLnF1ZXN0aW9uQmFua0xvYWRpbmcgPSB0cnVlCiAgICAgIC8vIOWQjOaXtuWKoOi9vemimOW6k+WSjOWIhuexu+aVsOaNrgogICAgICBQcm9taXNlLmFsbChbCiAgICAgICAgbGlzdFF1ZXN0aW9uQmFuayh0aGlzLnF1ZXJ5UGFyYW1zKSwKICAgICAgICBsaXN0Q2F0ZWdvcnkoeyBwYWdlU2l6ZTogMTAwMCB9KQogICAgICBdKS50aGVuKChbYmFua1Jlc3BvbnNlLCBjYXRlZ29yeVJlc3BvbnNlXSkgPT4gewogICAgICAgIGNvbnN0IHF1ZXN0aW9uQmFua3MgPSBiYW5rUmVzcG9uc2Uucm93cyB8fCBbXQogICAgICAgIHRoaXMudG90YWwgPSBiYW5rUmVzcG9uc2UudG90YWwgfHwgMAogICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gY2F0ZWdvcnlSZXNwb25zZS5yb3dzIHx8IGNhdGVnb3J5UmVzcG9uc2UuZGF0YSB8fCBbXQoKICAgICAgICAvLyDkuLrmr4/kuKrpopjlupPojrflj5bpopjnm67nu5/orqEKICAgICAgICBjb25zdCBzdGF0aXN0aWNzUHJvbWlzZXMgPSBxdWVzdGlvbkJhbmtzLm1hcChiYW5rID0+CiAgICAgICAgICBnZXRRdWVzdGlvblN0YXRpc3RpY3MoYmFuay5iYW5rSWQpLnRoZW4oc3RhdHMgPT4gewogICAgICAgICAgICBiYW5rLnF1ZXN0aW9uQ291bnQgPSBzdGF0cy5kYXRhID8gKHN0YXRzLmRhdGEudG90YWxDb3VudCB8fCBzdGF0cy5kYXRhLnRvdGFsIHx8IDApIDogMAogICAgICAgICAgICByZXR1cm4gYmFuawogICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICBiYW5rLnF1ZXN0aW9uQ291bnQgPSAwCiAgICAgICAgICAgIHJldHVybiBiYW5rCiAgICAgICAgICB9KQogICAgICAgICkKCiAgICAgICAgUHJvbWlzZS5hbGwoc3RhdGlzdGljc1Byb21pc2VzKS50aGVuKGJhbmtzV2l0aFN0YXRzID0+IHsKICAgICAgICAgIHRoaXMucXVlc3Rpb25CYW5rcyA9IGJhbmtzV2l0aFN0YXRzCiAgICAgICAgICB0aGlzLnF1ZXN0aW9uQmFua0xvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLnF1ZXN0aW9uQmFua0xvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICAvKiog5L+d5a2Y6KeE5YiZICovCiAgICBoYW5kbGVTYXZlUnVsZXMoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5L+d5a2Y6KeE5YiZ5Yqf6IO95byA5Y+R5LitLi4uJykKICAgICAgdGhpcy5zaG93UnVsZURpYWxvZyA9IGZhbHNlCiAgICB9LAoKICAgIC8qKiDkv53lrZjljZXkuKrop4TliJkgKi8KICAgIGFzeW5jIGhhbmRsZVNhdmVSdWxlKCkgewogICAgICBpZiAodGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9PT0gMyAmJiB0aGlzLnJ1bGVGb3JtLnNlbGVjdGVkSXRlbXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHpgInmi6nkuIDkuKrpopjlupMnKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAxICYmIHRoaXMucnVsZUZvcm0uc2VsZWN0ZWRRdWVzdGlvblR5cGVzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA5Liq6aKY5Z6LJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g6aKY5Z6L6KeE5YiZ6aqM6K+B77ya5qOA5p+l5piv5ZCm5pyJ5Y+v55So6aKY55uuCiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAxKSB7CiAgICAgICAgaWYgKHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZUNvdW50ID09PSAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aJgOmAiemimOWei+WcqOW9k+WJjemimOW6k+S4reayoeacieWPr+eUqOmimOebru+8jOaXoOazleS/neWtmCcpCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdlZGl0JykgewogICAgICAgIC8vIOe8lui+keeOsOacieinhOWImQogICAgICAgIHRoaXMudXBkYXRlRXhpc3RpbmdSdWxlKCkKICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdhZGRGaXhlZFJhbmRvbScpIHsKICAgICAgICAvLyDmt7vliqDlm7rlrpror5Xljbfpmo/mnLrmir3popjop4TliJkKICAgICAgICB0aGlzLmFkZEZpeGVkUmFuZG9tUnVsZSgpCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkRml4ZWRSYW5kb21TdWInKSB7CiAgICAgICAgLy8g5re75Yqg5Zu65a6a6K+V5Y236ZqP5py65oq96aKY5a2Q6KeE5YiZCiAgICAgICAgdGhpcy5hZGRGaXhlZFJhbmRvbVN1YlJ1bGUoKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOa3u+WKoOaWsOinhOWIme+8iOWMheaLrOa3u+WKoOWtkOinhOWIme+8iQogICAgICAgIHRoaXMuYWRkTmV3UnVsZSgpCiAgICAgIH0KCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdlZGl0JyA/ICfop4TliJnmm7TmlrDmiJDlip8nIDogJ+inhOWImeS/neWtmOaIkOWKnycpCiAgICAgIHRoaXMuc2hvd0FkZFJ1bGVEaWFsb2cgPSBmYWxzZQoKICAgICAgLy8g6YeN572u6KGo5Y2VCiAgICAgIHRoaXMucmVzZXRSdWxlRm9ybSgpCiAgICB9LAoKICAgIC8qKiDmm7TmlrDnjrDmnInop4TliJkgKi8KICAgIHVwZGF0ZUV4aXN0aW5nUnVsZSgpIHsKICAgICAgY29uc3QgcnVsZSA9IHRoaXMuZWRpdGluZ1J1bGUKICAgICAgcnVsZS50eXBlID0gdGhpcy5ydWxlRm9ybS5ydWxlVHlwZQogICAgICBydWxlLmdlbmVyYXRlVHlwZSA9IHRoaXMucnVsZUZvcm0uZ2VuZXJhdGVUeXBlCgogICAgICBpZiAodGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9PT0gMykgewogICAgICAgIC8vIOmimOW6k+inhOWImQogICAgICAgIHJ1bGUuc2VsZWN0ZWRJdGVtcyA9IFsuLi50aGlzLnJ1bGVGb3JtLnNlbGVjdGVkSXRlbXNdCiAgICAgICAgcnVsZS5zZWxlY3RlZEJhbmtzID0gdGhpcy5zZWxlY3RlZEJhbmtzLm1hcChiYW5rID0+ICh7CiAgICAgICAgICBiYW5rSWQ6IGJhbmsuYmFua0lkLAogICAgICAgICAgYmFua05hbWU6IGJhbmsuYmFua05hbWUsCiAgICAgICAgICBxdWVzdGlvbkNvdW50OiBiYW5rLnF1ZXN0aW9uQ291bnQKICAgICAgICB9KSkKICAgICAgICBydWxlLm1heFF1ZXN0aW9ucyA9IHJ1bGUuc2VsZWN0ZWRCYW5rcy5yZWR1Y2UoKHN1bSwgYmFuaykgPT4gc3VtICsgKGJhbmsucXVlc3Rpb25Db3VudCB8fCAwKSwgMCkKCiAgICAgICAgLy8g6YeN5paw6K6h566X5oC75YiG77yM56Gu5L+d6YCJ5Y+W5pWw6YeP5LiN6LaF6L+H5pyA5aSn6aKY55uu5pWwCiAgICAgICAgaWYgKHJ1bGUuc2VsZWN0ZWRDb3VudCA+IHJ1bGUubWF4UXVlc3Rpb25zKSB7CiAgICAgICAgICBydWxlLnNlbGVjdGVkQ291bnQgPSBydWxlLm1heFF1ZXN0aW9ucwogICAgICAgIH0KICAgICAgICBydWxlLnRvdGFsU2NvcmUgPSBydWxlLnNlbGVjdGVkQ291bnQgKiBydWxlLnNjb3JlUGVyUXVlc3Rpb24KICAgICAgfSBlbHNlIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAxKSB7CiAgICAgICAgLy8g6aKY5Z6L6KeE5YiZCiAgICAgICAgcnVsZS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMgPSBbLi4udGhpcy5ydWxlRm9ybS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXNdCiAgICAgICAgcnVsZS5tYXhRdWVzdGlvbnMgPSB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGVDb3VudCAvLyDkvb/nlKjlrp7pmYXmn6Xor6LliLDnmoTpopjnm67mlbDph48KICAgICAgfQogICAgfSwKCiAgICAvKiog5re75Yqg5paw6KeE5YiZICovCiAgICBhZGROZXdSdWxlKCkgewogICAgICAvLyDlpoLmnpzmmK/mt7vliqDlrZDop4TliJnvvIzmjInljp/pgLvovpHlpITnkIYKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZFN1YicgJiYgdGhpcy5wYXJlbnRSdWxlKSB7CiAgICAgICAgdGhpcy5hZGRTaW5nbGVSdWxlKCkKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5piv6aKY5bqT6KeE5YiZ5LiU6YCJ5oup5LqG5YiG5byA6K6+572u77yM5Li65q+P5Liq6aKY5bqT5Yib5bu654us56uL6KeE5YiZCiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAzICYmIHRoaXMucnVsZUZvcm0uZ2VuZXJhdGVUeXBlID09PSAnZGl2aWRlZCcgJiYgdGhpcy5zZWxlY3RlZEJhbmtzLmxlbmd0aCA+IDEpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkQmFua3MuZm9yRWFjaCgoYmFuaywgaW5kZXgpID0+IHsKICAgICAgICAgIGNvbnN0IHJ1bGUgPSB7CiAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgaW5kZXgsIC8vIOehruS/neavj+S4quinhOWImeacieWUr+S4gElECiAgICAgICAgICAgIHR5cGU6IHRoaXMucnVsZUZvcm0ucnVsZVR5cGUsCiAgICAgICAgICAgIGdlbmVyYXRlVHlwZTogdGhpcy5ydWxlRm9ybS5nZW5lcmF0ZVR5cGUsCiAgICAgICAgICAgIHNlbGVjdGVkQ291bnQ6IDEsCiAgICAgICAgICAgIHNjb3JlUGVyUXVlc3Rpb246IDAuNSwKICAgICAgICAgICAgdG90YWxTY29yZTogMC41LAogICAgICAgICAgICBzZWxlY3RlZEl0ZW1zOiBbYmFuay5iYW5rSWRdLAogICAgICAgICAgICBzZWxlY3RlZEJhbmtzOiBbewogICAgICAgICAgICAgIGJhbmtJZDogYmFuay5iYW5rSWQsCiAgICAgICAgICAgICAgYmFua05hbWU6IGJhbmsuYmFua05hbWUsCiAgICAgICAgICAgICAgcXVlc3Rpb25Db3VudDogYmFuay5xdWVzdGlvbkNvdW50CiAgICAgICAgICAgIH1dLAogICAgICAgICAgICBtYXhRdWVzdGlvbnM6IGJhbmsucXVlc3Rpb25Db3VudCB8fCAwCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnVwZGF0ZVJ1bGVTY29yZShydWxlKQogICAgICAgICAgdGhpcy5ydWxlcy5wdXNoKHJ1bGUpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDmlbTkvZPorr7nva7miJblhbbku5bmg4XlhrXvvIzliJvlu7rljZXkuKrop4TliJkKICAgICAgICB0aGlzLmFkZFNpbmdsZVJ1bGUoKQogICAgICB9CiAgICB9LAoKICAgIC8qKiDmt7vliqDlm7rlrpror5Xljbfpmo/mnLrmir3popjop4TliJkgKi8KICAgIGFkZEZpeGVkUmFuZG9tUnVsZSgpIHsKICAgICAgLy8g5aaC5p6c5piv6aKY5bqT6KeE5YiZ5LiU6YCJ5oup5LqG5YiG5byA6K6+572u77yM5Li65q+P5Liq6aKY5bqT5Yib5bu654us56uL6KeE5YiZCiAgICAgIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAzICYmIHRoaXMucnVsZUZvcm0uZ2VuZXJhdGVUeXBlID09PSAnZGl2aWRlZCcgJiYgdGhpcy5zZWxlY3RlZEJhbmtzLmxlbmd0aCA+IDEpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkQmFua3MuZm9yRWFjaCgoYmFuaywgaW5kZXgpID0+IHsKICAgICAgICAgIGNvbnN0IHJ1bGUgPSB7CiAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgaW5kZXgsCiAgICAgICAgICAgIHR5cGU6IHRoaXMucnVsZUZvcm0ucnVsZVR5cGUsCiAgICAgICAgICAgIGdlbmVyYXRlVHlwZTogdGhpcy5ydWxlRm9ybS5nZW5lcmF0ZVR5cGUsCiAgICAgICAgICAgIHNlbGVjdGVkQ291bnQ6IDEsCiAgICAgICAgICAgIHNjb3JlUGVyUXVlc3Rpb246IDEsCiAgICAgICAgICAgIHRvdGFsU2NvcmU6IDEsCiAgICAgICAgICAgIHNlbGVjdGVkSXRlbXM6IFtiYW5rLmJhbmtJZF0sCiAgICAgICAgICAgIHNlbGVjdGVkQmFua3M6IFt7CiAgICAgICAgICAgICAgYmFua0lkOiBiYW5rLmJhbmtJZCwKICAgICAgICAgICAgICBiYW5rTmFtZTogYmFuay5iYW5rTmFtZSwKICAgICAgICAgICAgICBxdWVzdGlvbkNvdW50OiBiYW5rLnF1ZXN0aW9uQ291bnQKICAgICAgICAgICAgfV0sCiAgICAgICAgICAgIG1heFF1ZXN0aW9uczogYmFuay5xdWVzdGlvbkNvdW50IHx8IDAKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMudXBkYXRlRml4ZWRSYW5kb21SdWxlU2NvcmUocnVsZSkKICAgICAgICAgIHRoaXMuZml4ZWRSYW5kb21SdWxlcy5wdXNoKHJ1bGUpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDmlbTkvZPorr7nva7miJblhbbku5bmg4XlhrXvvIzliJvlu7rljZXkuKrop4TliJkKICAgICAgICB0aGlzLmFkZFNpbmdsZUZpeGVkUmFuZG9tUnVsZSgpCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOa3u+WKoOWNleS4quWbuuWumuivleWNt+maj+acuuaKvemimOinhOWImSAqLwogICAgYWRkU2luZ2xlRml4ZWRSYW5kb21SdWxlKCkgewogICAgICBjb25zdCBydWxlID0gewogICAgICAgIGlkOiBEYXRlLm5vdygpLAogICAgICAgIHR5cGU6IHRoaXMucnVsZUZvcm0ucnVsZVR5cGUsCiAgICAgICAgZ2VuZXJhdGVUeXBlOiB0aGlzLnJ1bGVGb3JtLmdlbmVyYXRlVHlwZSwKICAgICAgICBzZWxlY3RlZENvdW50OiAxLAogICAgICAgIHNjb3JlUGVyUXVlc3Rpb246IDEsCiAgICAgICAgdG90YWxTY29yZTogMSwKICAgICAgICBtYXhRdWVzdGlvbnM6IDAKICAgICAgfQoKICAgICAgaWYgKHRoaXMucnVsZUZvcm0ucnVsZVR5cGUgPT09IDMpIHsKICAgICAgICAvLyDpopjlupPop4TliJkKICAgICAgICBydWxlLnNlbGVjdGVkSXRlbXMgPSBbLi4udGhpcy5ydWxlRm9ybS5zZWxlY3RlZEl0ZW1zXQogICAgICAgIHJ1bGUuc2VsZWN0ZWRCYW5rcyA9IHRoaXMuc2VsZWN0ZWRCYW5rcy5tYXAoYmFuayA9PiAoewogICAgICAgICAgYmFua0lkOiBiYW5rLmJhbmtJZCwKICAgICAgICAgIGJhbmtOYW1lOiBiYW5rLmJhbmtOYW1lLAogICAgICAgICAgcXVlc3Rpb25Db3VudDogYmFuay5xdWVzdGlvbkNvdW50CiAgICAgICAgfSkpCiAgICAgICAgcnVsZS5tYXhRdWVzdGlvbnMgPSBydWxlLnNlbGVjdGVkQmFua3MucmVkdWNlKChzdW0sIGJhbmspID0+IHN1bSArIChiYW5rLnF1ZXN0aW9uQ291bnQgfHwgMCksIDApCiAgICAgIH0gZWxzZSBpZiAodGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9PT0gMSkgewogICAgICAgIC8vIOmimOWei+inhOWImQogICAgICAgIHJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzID0gWy4uLnRoaXMucnVsZUZvcm0uc2VsZWN0ZWRRdWVzdGlvblR5cGVzXQogICAgICAgIHJ1bGUubWF4UXVlc3Rpb25zID0gdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlQ291bnQKICAgICAgfQoKICAgICAgdGhpcy5maXhlZFJhbmRvbVJ1bGVzLnB1c2gocnVsZSkKICAgICAgdGhpcy51cGRhdGVGaXhlZFJhbmRvbVJ1bGVTY29yZShydWxlKQogICAgfSwKCiAgICAvKiog5re75Yqg5Zu65a6a6K+V5Y236ZqP5py65oq96aKY5a2Q6KeE5YiZICovCiAgICBhZGRGaXhlZFJhbmRvbVN1YlJ1bGUoKSB7CiAgICAgIGNvbnN0IHJ1bGUgPSB7CiAgICAgICAgaWQ6IERhdGUubm93KCksCiAgICAgICAgdHlwZTogdGhpcy5ydWxlRm9ybS5ydWxlVHlwZSwKICAgICAgICBwYXJlbnRJZDogdGhpcy5wYXJlbnRSdWxlLmlkLAogICAgICAgIHNlbGVjdGVkQ291bnQ6IDEsCiAgICAgICAgc2NvcmVQZXJRdWVzdGlvbjogMSwKICAgICAgICB0b3RhbFNjb3JlOiAxLAogICAgICAgIG1heFF1ZXN0aW9uczogMAogICAgICB9CgogICAgICBpZiAodGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9PT0gMSkgewogICAgICAgIC8vIOmimOWei+inhOWImQogICAgICAgIHJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzID0gWy4uLnRoaXMucnVsZUZvcm0uc2VsZWN0ZWRRdWVzdGlvblR5cGVzXQogICAgICAgIHJ1bGUubWF4UXVlc3Rpb25zID0gdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlQ291bnQKICAgICAgfSBlbHNlIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAzKSB7CiAgICAgICAgLy8g6aKY5bqT6KeE5YiZ77yI6Jm954S25Zyo5Zu65a6a6K+V5Y236ZqP5py65oq96aKY5Lit5a2Q6KeE5YiZ5Li76KaB5piv6aKY5Z6L77yM5L2G5L+d5oyB5YW85a655oCn77yJCiAgICAgICAgcnVsZS5zZWxlY3RlZEl0ZW1zID0gWy4uLnRoaXMucnVsZUZvcm0uc2VsZWN0ZWRJdGVtc10KICAgICAgICBydWxlLnNlbGVjdGVkQmFua3MgPSB0aGlzLnNlbGVjdGVkQmFua3MubWFwKGJhbmsgPT4gKHsKICAgICAgICAgIGJhbmtJZDogYmFuay5iYW5rSWQsCiAgICAgICAgICBiYW5rTmFtZTogYmFuay5iYW5rTmFtZSwKICAgICAgICAgIHF1ZXN0aW9uQ291bnQ6IGJhbmsucXVlc3Rpb25Db3VudAogICAgICAgIH0pKQogICAgICAgIHJ1bGUubWF4UXVlc3Rpb25zID0gcnVsZS5zZWxlY3RlZEJhbmtzLnJlZHVjZSgoc3VtLCBiYW5rKSA9PiBzdW0gKyAoYmFuay5xdWVzdGlvbkNvdW50IHx8IDApLCAwKQogICAgICB9CgogICAgICAvLyDnoa7kv53niLbop4TliJnmnIljaGlsZHJlbuaVsOe7hAogICAgICBpZiAoIXRoaXMucGFyZW50UnVsZS5jaGlsZHJlbikgewogICAgICAgIHRoaXMuJHNldCh0aGlzLnBhcmVudFJ1bGUsICdjaGlsZHJlbicsIFtdKQogICAgICB9CgogICAgICAvLyDmt7vliqDliLDniLbop4TliJnnmoRjaGlsZHJlbuS4rQogICAgICB0aGlzLnBhcmVudFJ1bGUuY2hpbGRyZW4ucHVzaChydWxlKQoKICAgICAgLy8g5ZCM5pe25re75Yqg5Yiw5Li76KeE5YiZ5YiX6KGo5Lit77yI55So5LqO57uf6K6h5ZKM5oq96aKY77yJCiAgICAgIHRoaXMuZml4ZWRSYW5kb21SdWxlcy5wdXNoKHJ1bGUpCiAgICAgIHRoaXMudXBkYXRlRml4ZWRSYW5kb21SdWxlU2NvcmUocnVsZSkKICAgIH0sCgogICAgLyoqIOa3u+WKoOWNleS4quinhOWImSAqLwogICAgYWRkU2luZ2xlUnVsZSgpIHsKICAgICAgY29uc3QgcnVsZSA9IHsKICAgICAgICBpZDogRGF0ZS5ub3coKSwgLy8g5Li05pe2SUQKICAgICAgICB0eXBlOiB0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlLAogICAgICAgIGdlbmVyYXRlVHlwZTogdGhpcy5ydWxlRm9ybS5nZW5lcmF0ZVR5cGUsCiAgICAgICAgc2VsZWN0ZWRDb3VudDogMSwgLy8g6buY6K6k6YCJ5Y+WMemimAogICAgICAgIHNjb3JlUGVyUXVlc3Rpb246IDAuNSwgLy8g6buY6K6k5q+P6aKYMC415YiGCiAgICAgICAgdG90YWxTY29yZTogMC41LCAvLyDpu5jorqTmgLvliIYwLjXliIYKICAgICAgICBtYXhRdWVzdGlvbnM6IDAgLy8g5pyA5aSn6aKY55uu5pWwCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOaYr+a3u+WKoOWtkOinhOWImQogICAgICBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkU3ViJyAmJiB0aGlzLnBhcmVudFJ1bGUpIHsKICAgICAgICBydWxlLnBhcmVudElkID0gdGhpcy5wYXJlbnRSdWxlLmlkCgogICAgICAgIC8vIOehruS/neeItuinhOWImeaciWNoaWxkcmVu5pWw57uECiAgICAgICAgaWYgKCF0aGlzLnBhcmVudFJ1bGUuY2hpbGRyZW4pIHsKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnBhcmVudFJ1bGUsICdjaGlsZHJlbicsIFtdKQogICAgICAgIH0KCiAgICAgICAgLy8g5re75Yqg5Yiw54i26KeE5YiZ55qEY2hpbGRyZW7kuK0KICAgICAgICB0aGlzLnBhcmVudFJ1bGUuY2hpbGRyZW4ucHVzaChydWxlKQogICAgICB9CgogICAgICBpZiAodGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9PT0gMykgewogICAgICAgIC8vIOmimOW6k+inhOWImQogICAgICAgIHJ1bGUuc2VsZWN0ZWRJdGVtcyA9IFsuLi50aGlzLnJ1bGVGb3JtLnNlbGVjdGVkSXRlbXNdCiAgICAgICAgcnVsZS5zZWxlY3RlZEJhbmtzID0gdGhpcy5zZWxlY3RlZEJhbmtzLm1hcChiYW5rID0+ICh7CiAgICAgICAgICBiYW5rSWQ6IGJhbmsuYmFua0lkLAogICAgICAgICAgYmFua05hbWU6IGJhbmsuYmFua05hbWUsCiAgICAgICAgICBxdWVzdGlvbkNvdW50OiBiYW5rLnF1ZXN0aW9uQ291bnQKICAgICAgICB9KSkKICAgICAgICBydWxlLm1heFF1ZXN0aW9ucyA9IHJ1bGUuc2VsZWN0ZWRCYW5rcy5yZWR1Y2UoKHN1bSwgYmFuaykgPT4gc3VtICsgKGJhbmsucXVlc3Rpb25Db3VudCB8fCAwKSwgMCkKICAgICAgfSBlbHNlIGlmICh0aGlzLnJ1bGVGb3JtLnJ1bGVUeXBlID09PSAxKSB7CiAgICAgICAgLy8g6aKY5Z6L6KeE5YiZCiAgICAgICAgcnVsZS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMgPSBbLi4udGhpcy5ydWxlRm9ybS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXNdCiAgICAgICAgcnVsZS5tYXhRdWVzdGlvbnMgPSB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGVDb3VudCAvLyDkvb/nlKjlrp7pmYXmn6Xor6LliLDnmoTpopjnm67mlbDph48KICAgICAgfQoKICAgICAgLy8g5Y+q5pyJ54i26KeE5YiZ5omN5re75Yqg5Yiw5Li76KeE5YiZ5YiX6KGoCiAgICAgIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gIT09ICdhZGRTdWInKSB7CiAgICAgICAgdGhpcy5ydWxlcy5wdXNoKHJ1bGUpCiAgICAgIH0KCiAgICAgIC8vIOabtOaWsOinhOWImeWIhuaVsAogICAgICB0aGlzLnVwZGF0ZVJ1bGVTY29yZShydWxlKQogICAgfSwKCiAgICAvKiog6YeN572u6KeE5YiZ6KGo5Y2VICovCiAgICByZXNldFJ1bGVGb3JtKCkgewogICAgICB0aGlzLnJ1bGVGb3JtID0gewogICAgICAgIHJ1bGVUeXBlOiAzLAogICAgICAgIGdlbmVyYXRlVHlwZTogJ2RpdmlkZWQnLAogICAgICAgIHNlbGVjdGVkSXRlbXM6IFtdLAogICAgICAgIHNlbGVjdGVkUXVlc3Rpb25UeXBlczogW10KICAgICAgfQogICAgICAvLyDlj6rmnInkuI3mmK/lm7rlrpror5Xljbfpmo/mnLrmir3popjml7bmiY3ph43nva7mk43kvZznirbmgIEKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiAhPT0gJ2FkZEZpeGVkUmFuZG9tJyAmJiB0aGlzLmN1cnJlbnRPcGVyYXRpb24gIT09ICdhZGRGaXhlZFJhbmRvbVN1YicpIHsKICAgICAgICB0aGlzLmN1cnJlbnRPcGVyYXRpb24gPSAnYWRkJwogICAgICAgIHRoaXMuZWRpdGluZ1J1bGUgPSBudWxsCiAgICAgICAgdGhpcy5wYXJlbnRSdWxlID0gbnVsbAogICAgICB9CiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZUNvdW50ID0gMAogICAgfSwKCiAgICAvKiog5pCc57Si6aKY5bqTICovCiAgICBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iYW5rTmFtZSA9IHRoaXMuc2VhcmNoS2V5d29yZCB8fCB1bmRlZmluZWQKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDph43nva7mkJzntKIgKi8KICAgIGhhbmRsZVJlc2V0KCkgewogICAgICB0aGlzLnNlYXJjaEtleXdvcmQgPSAnJwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYmFua05hbWUgPSB1bmRlZmluZWQKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDooajmoLzpgInmi6nlj5jljJYgKi8KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5ydWxlRm9ybS5zZWxlY3RlZEl0ZW1zID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uYmFua0lkKQogICAgfSwKCiAgICAvKiog56e76Zmk5bey6YCJ5oup55qE6aKY5bqTICovCiAgICByZW1vdmVTZWxlY3RlZEJhbmsoYmFua0lkKSB7CiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5ydWxlRm9ybS5zZWxlY3RlZEl0ZW1zLmluZGV4T2YoYmFua0lkKQogICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgIHRoaXMucnVsZUZvcm0uc2VsZWN0ZWRJdGVtcy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgIH0KCiAgICAgIC8vIOWQjOatpeWPlua2iOihqOagvOS4reeahOWLvumAieeKtuaAgQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLnF1ZXN0aW9uQmFua1RhYmxlCiAgICAgICAgaWYgKHRhYmxlKSB7CiAgICAgICAgICAvLyDmib7liLDlr7nlupTnmoTooYzmlbDmja4KICAgICAgICAgIGNvbnN0IHJvd1RvRGVzZWxlY3QgPSB0aGlzLnF1ZXN0aW9uQmFua3MuZmluZChiYW5rID0+IGJhbmsuYmFua0lkID09PSBiYW5rSWQpCiAgICAgICAgICBpZiAocm93VG9EZXNlbGVjdCkgewogICAgICAgICAgICB0YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93VG9EZXNlbGVjdCwgZmFsc2UpCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvKiog5YiG6aG15Y+Y5YyWICovCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2UpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gcGFnZQogICAgICB0aGlzLmxvYWRRdWVzdGlvbkJhbmtzKCkKICAgIH0sCgogICAgLyoqIOagueaNruWIhuexu0lE6I635Y+W5YiG57G75ZCN56ewICovCiAgICBnZXRDYXRlZ29yeU5hbWUoY2F0ZWdvcnlJZCkgewogICAgICBjb25zdCBjYXRlZ29yeSA9IHRoaXMuZmluZENhdGVnb3J5QnlJZCh0aGlzLmNhdGVnb3J5T3B0aW9ucywgY2F0ZWdvcnlJZCkKICAgICAgcmV0dXJuIGNhdGVnb3J5ID8gY2F0ZWdvcnkubmFtZSA6ICfmnKrliIbnsbsnCiAgICB9LAoKICAgIC8qKiDlnKjliIbnsbvmlbDmja7kuK3mn6Xmib7liIbnsbvvvIjmlK/mjIHmiYHlubPlkozmoJHlvaLnu5PmnoTvvIkgKi8KICAgIGZpbmRDYXRlZ29yeUJ5SWQoY2F0ZWdvcmllcywgaWQpIHsKICAgICAgLy8g5Yib5bu65omB5bmz5YyW55qE5YiG57G75YiX6KGoCiAgICAgIGNvbnN0IGZsYXRDYXRlZ29yaWVzID0gdGhpcy5mbGF0dGVuQ2F0ZWdvcmllcyhjYXRlZ29yaWVzKQoKICAgICAgLy8g5Zyo5omB5bmz5YyW5YiX6KGo5Lit5p+l5om+CiAgICAgIGNvbnN0IGNhdGVnb3J5ID0gZmxhdENhdGVnb3JpZXMuZmluZChjYXQgPT4gY2F0LmlkID09PSBpZCkKICAgICAgcmV0dXJuIGNhdGVnb3J5IHx8IG51bGwKICAgIH0sCgogICAgLyoqIOWwhuagkeW9ouWIhuexu+aVsOaNruaJgeW5s+WMliAqLwogICAgZmxhdHRlbkNhdGVnb3JpZXMoY2F0ZWdvcmllcykgewogICAgICBsZXQgcmVzdWx0ID0gW10KCiAgICAgIGZ1bmN0aW9uIGZsYXR0ZW4oY2F0cykgewogICAgICAgIGZvciAoY29uc3QgY2F0IG9mIGNhdHMpIHsKICAgICAgICAgIHJlc3VsdC5wdXNoKGNhdCkKICAgICAgICAgIGlmIChjYXQuY2hpbGRyZW4gJiYgY2F0LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgZmxhdHRlbihjYXQuY2hpbGRyZW4pCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICBmbGF0dGVuKGNhdGVnb3JpZXMpCiAgICAgIHJldHVybiByZXN1bHQKICAgIH0sCgogICAgLyoqIOabtOaWsOinhOWImeWIhuaVsCAqLwogICAgdXBkYXRlUnVsZVNjb3JlKHJ1bGUpIHsKICAgICAgcnVsZS50b3RhbFNjb3JlID0gcnVsZS5zZWxlY3RlZENvdW50ICogcnVsZS5zY29yZVBlclF1ZXN0aW9uCiAgICB9LAoKICAgIC8qKiDojrflj5bpopjlnovmlofmnKwgKi8KICAgIGdldFF1ZXN0aW9uVHlwZVRleHQocXVlc3Rpb25UeXBlcykgewogICAgICBjb25zdCBxdWVzdGlvblR5cGVNYXAgPSB7CiAgICAgICAgJzEnOiAn5Y2V6YCJ6aKYJywgJzInOiAn5aSa6YCJ6aKYJywgJzMnOiAn5Yik5pat6aKYJywKICAgICAgICAxOiAn5Y2V6YCJ6aKYJywgMjogJ+WkmumAiemimCcsIDM6ICfliKTmlq3popgnCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOaYr+aVsOe7hO+8jOWkhOeQhuWkmuS4qumimOWeiwogICAgICBpZiAoQXJyYXkuaXNBcnJheShxdWVzdGlvblR5cGVzKSkgewogICAgICAgIHJldHVybiBxdWVzdGlvblR5cGVzLm1hcCh0ID0+IHF1ZXN0aW9uVHlwZU1hcFt0XSkuam9pbign44CBJykKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5piv5Y2V5Liq5YC877yM55u05o6l6L+U5Zue5a+55bqU5paH5pysCiAgICAgIHJldHVybiBxdWVzdGlvblR5cGVNYXBbcXVlc3Rpb25UeXBlc10gfHwgJ+acquefpScKICAgIH0sCgoKCiAgICAvKiog6I635Y+W5a2Q6KeE5YiZ5qCH562+ICovCiAgICBnZXRDaGlsZFJ1bGVMYWJlbChjaGlsZFJ1bGUpIHsKICAgICAgaWYgKGNoaWxkUnVsZS50eXBlID09PSAzKSB7CiAgICAgICAgcmV0dXJuICfpopjlupPvvJonCiAgICAgIH0gZWxzZSBpZiAoY2hpbGRSdWxlLnR5cGUgPT09IDEpIHsKICAgICAgICAvLyDpopjlnovlrZDop4TliJnlj6rmmL7npLrlhbfkvZPnmoTpopjlnovlkI3np7DvvIzkuI3mmL7npLoi6aKY5Z6L77yaIuWJjee8gAogICAgICAgIGlmIChjaGlsZFJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzICYmIGNoaWxkUnVsZS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICBjb25zdCBxdWVzdGlvblR5cGVNYXAgPSB7ICcxJzogJ+WNlemAiemimCcsICcyJzogJ+WkmumAiemimCcsICczJzogJ+WIpOaWremimCcgfQogICAgICAgICAgcmV0dXJuIHF1ZXN0aW9uVHlwZU1hcFtjaGlsZFJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzWzBdXQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gJ+mimOWeiycKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuICfop4TliJnvvJonCiAgICB9LAoKICAgIC8qKiDojrflj5bop4TliJnnsbvlnovmoIfnrb7vvIjnlKjkuo5yYW5kb20tcGFwZXLmmL7npLrvvIkgKi8KICAgIGdldFJ1bGVUeXBlTGFiZWwocnVsZSkgewogICAgICBpZiAocnVsZS50eXBlID09PSAzKSB7CiAgICAgICAgcmV0dXJuICfpopjlupPvvJonCiAgICAgIH0gZWxzZSBpZiAocnVsZS50eXBlID09PSAxKSB7CiAgICAgICAgcmV0dXJuICfpopjlnovvvJonCiAgICAgIH0KICAgICAgcmV0dXJuICfop4TliJnvvJonCiAgICB9LAoKICAgIC8qKiDojrflj5bop4TliJnlhoXlrrnvvIjnlKjkuo5yYW5kb20tcGFwZXLmmL7npLrvvIkgKi8KICAgIGdldFJ1bGVDb250ZW50KHJ1bGUpIHsKICAgICAgaWYgKHJ1bGUudHlwZSA9PT0gMykgewogICAgICAgIC8vIOmimOW6k+inhOWImeaYvuekuumimOW6k+WQjeensAogICAgICAgIGlmIChydWxlLnNlbGVjdGVkQmFua3MgJiYgcnVsZS5zZWxlY3RlZEJhbmtzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHJldHVybiBydWxlLnNlbGVjdGVkQmFua3MubWFwKGJhbmsgPT4gYmFuay5iYW5rTmFtZSkuam9pbign44CBJykKICAgICAgICB9CiAgICAgICAgcmV0dXJuICfmnKrpgInmi6npopjlupMnCiAgICAgIH0gZWxzZSBpZiAocnVsZS50eXBlID09PSAxKSB7CiAgICAgICAgLy8g6aKY5Z6L6KeE5YiZ5pi+56S66aKY5Z6L5ZCN56ewCiAgICAgICAgaWYgKHJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzICYmIHJ1bGUuc2VsZWN0ZWRRdWVzdGlvblR5cGVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGNvbnN0IHF1ZXN0aW9uVHlwZU1hcCA9IHsgJzEnOiAn5Y2V6YCJ6aKYJywgJzInOiAn5aSa6YCJ6aKYJywgJzMnOiAn5Yik5pat6aKYJyB9CiAgICAgICAgICByZXR1cm4gcnVsZS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMubWFwKHR5cGUgPT4gcXVlc3Rpb25UeXBlTWFwW3R5cGVdKS5qb2luKCfjgIEnKQogICAgICAgIH0KICAgICAgICByZXR1cm4gJ+acqumAieaLqemimOWeiycKICAgICAgfQogICAgICByZXR1cm4gJ+acqumFjee9ricKICAgIH0sCgogICAgLyoqIOa3u+WKoOWtkOinhOWImSAqLwogICAgYWRkU3ViUnVsZShydWxlKSB7CiAgICAgIHRoaXMuY3VycmVudE9wZXJhdGlvbiA9ICdhZGRTdWInCiAgICAgIHRoaXMucGFyZW50UnVsZSA9IHJ1bGUKCiAgICAgIC8vIOagueaNrueItuinhOWImeexu+Wei+ehruWumuWtkOinhOWImeexu+WeiwogICAgICBsZXQgZGVmYXVsdFJ1bGVUeXBlCgogICAgICBpZiAocnVsZS50eXBlID09PSAzKSB7CiAgICAgICAgLy8g6aKY5bqT6KeE5YiZ55qE5a2Q6KeE5YiZ5Y+q6IO95piv6aKY5Z6L6KeE5YiZCiAgICAgICAgZGVmYXVsdFJ1bGVUeXBlID0gMQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWFtuS7luaDheWGteeahOm7mOiupOWkhOeQhu+8iOiZveeEtuebruWJjeWPquaUr+aMgemimOW6k+S9nOS4uuS4gOe6p+inhOWIme+8iQogICAgICAgIGRlZmF1bHRSdWxlVHlwZSA9IDEKICAgICAgfQoKICAgICAgdGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9IGRlZmF1bHRSdWxlVHlwZQogICAgICB0aGlzLnJ1bGVGb3JtLmdlbmVyYXRlVHlwZSA9ICdkaXZpZGVkJwogICAgICB0aGlzLnJ1bGVGb3JtLnNlbGVjdGVkSXRlbXMgPSBbXQogICAgICB0aGlzLnJ1bGVGb3JtLnNlbGVjdGVkUXVlc3Rpb25UeXBlcyA9IFtdCgogICAgICB0aGlzLnNob3dBZGRSdWxlRGlhbG9nID0gdHJ1ZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGJhbmtOYW1lOiB1bmRlZmluZWQKICAgICAgfQogICAgICB0aGlzLnNlYXJjaEtleXdvcmQgPSAnJwogICAgICB0aGlzLmxvYWRRdWVzdGlvbkJhbmtzKCkKICAgIH0sCgogICAgLyoqIOe8lui+keinhOWImSAqLwogICAgZWRpdFJ1bGUocnVsZSkgewogICAgICB0aGlzLmN1cnJlbnRPcGVyYXRpb24gPSAnZWRpdCcKICAgICAgdGhpcy5lZGl0aW5nUnVsZSA9IHJ1bGUKICAgICAgdGhpcy5ydWxlRm9ybS5ydWxlVHlwZSA9IHJ1bGUudHlwZQogICAgICB0aGlzLnJ1bGVGb3JtLmdlbmVyYXRlVHlwZSA9IHJ1bGUuZ2VuZXJhdGVUeXBlCgogICAgICBpZiAocnVsZS50eXBlID09PSAzKSB7CiAgICAgICAgLy8g6aKY5bqT6KeE5YiZCiAgICAgICAgdGhpcy5ydWxlRm9ybS5zZWxlY3RlZEl0ZW1zID0gWy4uLnJ1bGUuc2VsZWN0ZWRJdGVtc10KICAgICAgfSBlbHNlIGlmIChydWxlLnR5cGUgPT09IDEpIHsKICAgICAgICAvLyDpopjlnovop4TliJkKICAgICAgICB0aGlzLnJ1bGVGb3JtLnNlbGVjdGVkUXVlc3Rpb25UeXBlcyA9IFsuLi5ydWxlLnNlbGVjdGVkUXVlc3Rpb25UeXBlc10KICAgICAgICAvLyDnvJbovpHpopjlnovop4TliJnml7bvvIzmm7TmlrDpopjnm67mlbDph48KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVF1ZXN0aW9uVHlwZUNvdW50KCkKICAgICAgICB9KQogICAgICB9CgogICAgICB0aGlzLnNob3dBZGRSdWxlRGlhbG9nID0gdHJ1ZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGJhbmtOYW1lOiB1bmRlZmluZWQKICAgICAgfQogICAgICB0aGlzLnNlYXJjaEtleXdvcmQgPSAnJwogICAgICB0aGlzLmxvYWRRdWVzdGlvbkJhbmtzKCkKICAgIH0sCgogICAgLyoqIOWIoOmZpOinhOWImSAqLwogICAgZGVsZXRlUnVsZShydWxlKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuimgeWIoOmZpOi/meadoeinhOWImeWQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMucnVsZXMuZmluZEluZGV4KHIgPT4gci5pZCA9PT0gcnVsZS5pZCkKICAgICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgICAgdGhpcy5ydWxlcy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+inhOWImeWIoOmZpOaIkOWKnycpCiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDliKDpmaTlrZDop4TliJkgKi8KICAgIGRlbGV0ZUNoaWxkUnVsZShwYXJlbnRSdWxlLCBjaGlsZFJ1bGUpIHsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6L+Z5p2h5a2Q6KeE5YiZ5ZCX77yfJywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGNvbnN0IGluZGV4ID0gcGFyZW50UnVsZS5jaGlsZHJlbi5maW5kSW5kZXgoY2hpbGQgPT4gY2hpbGQuaWQgPT09IGNoaWxkUnVsZS5pZCkKICAgICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgICAgcGFyZW50UnVsZS5jaGlsZHJlbi5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WtkOinhOWImeWIoOmZpOaIkOWKnycpCiAgICAgICAgfQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDojrflj5blr7nor53moYbmoIfpopggKi8KICAgIGdldERpYWxvZ1RpdGxlKCkgewogICAgICBzd2l0Y2ggKHRoaXMuY3VycmVudE9wZXJhdGlvbikgewogICAgICAgIGNhc2UgJ2FkZCc6CiAgICAgICAgICByZXR1cm4gJ+a3u+WKoOinhOWImScKICAgICAgICBjYXNlICdlZGl0JzoKICAgICAgICAgIHJldHVybiAn57yW6L6R6KeE5YiZJwogICAgICAgIGNhc2UgJ2FkZFN1Yic6CiAgICAgICAgICByZXR1cm4gJ+a3u+WKoOWtkOinhOWImScKICAgICAgICBjYXNlICdhZGRGaXhlZFJhbmRvbSc6CiAgICAgICAgICByZXR1cm4gJ+a3u+WKoOaKvemimOinhOWImScKICAgICAgICBjYXNlICdhZGRGaXhlZFJhbmRvbVN1Yic6CiAgICAgICAgICByZXR1cm4gJ+a3u+WKoOmimOWei+inhOWImScKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuICfmt7vliqDop4TliJknCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOaYr+WQpuaYvuekuuinhOWImeexu+Wei+mAieaLqSAqLwogICAgc2hvdWxkU2hvd1J1bGVUeXBlU2VsZWN0aW9uKCkgewogICAgICAvLyDlm7rlrpror5Xljbfpmo/mnLrmir3popjnmoTkuIDnuqfop4TliJnlj6rog73pgInmi6npopjlupPvvIzkvYbku43mmL7npLrpgInmi6nnlYzpnaIKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZEZpeGVkUmFuZG9tJykgewogICAgICAgIHJldHVybiB0cnVlCiAgICAgIH0KICAgICAgLy8g5Zu65a6a6K+V5Y236ZqP5py65oq96aKY55qE5a2Q6KeE5YiZ5Y+v5Lul6YCJ5oup6KeE5YiZ57G75Z6LCiAgICAgIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdhZGRGaXhlZFJhbmRvbVN1YicpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9CiAgICAgIC8vIOWFtuS7luaTjeS9nOmDveaYvuekuuinhOWImeexu+Wei+mAieaLqQogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKCiAgICAvKiog5piv5ZCm5pi+56S66KeE5YiZ57G75Z6L6YCJ6aG5ICovCiAgICBzaG91bGRTaG93UnVsZVR5cGUocnVsZVR5cGUpIHsKICAgICAgLy8g57yW6L6R5pe25Y+q5pi+56S65b2T5YmN6KeE5YiZ55qE57G75Z6LCiAgICAgIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdlZGl0JykgewogICAgICAgIHJldHVybiB0aGlzLmVkaXRpbmdSdWxlLnR5cGUgPT09IHJ1bGVUeXBlCiAgICAgIH0KCiAgICAgIC8vIOa3u+WKoOWtkOinhOWImeaXtueahOmAu+i+kQogICAgICBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkU3ViJyAmJiB0aGlzLnBhcmVudFJ1bGUpIHsKICAgICAgICAvLyDpopjlnovop4TliJnnmoTlrZDop4TliJnlj6rog73mmK/popjlupPop4TliJkKICAgICAgICBpZiAodGhpcy5wYXJlbnRSdWxlLnR5cGUgPT09IDEpIHsKICAgICAgICAgIHJldHVybiBydWxlVHlwZSA9PT0gMwogICAgICAgIH0KICAgICAgICAvLyDpopjlupPop4TliJnnmoTlrZDop4TliJnlj6rog73mmK/popjlnovop4TliJkKICAgICAgICBpZiAodGhpcy5wYXJlbnRSdWxlLnR5cGUgPT09IDMpIHsKICAgICAgICAgIHJldHVybiBydWxlVHlwZSA9PT0gMQogICAgICAgIH0KICAgICAgICAvLyDlhbbku5bmg4XlhrXkuI3og73pgInmi6nniLbop4TliJnnmoTnsbvlnosKICAgICAgICByZXR1cm4gcnVsZVR5cGUgIT09IHRoaXMucGFyZW50UnVsZS50eXBlCiAgICAgIH0KCiAgICAgIC8vIOa3u+WKoOS4gOe6p+inhOWImeaXtueahOmAu+i+kQogICAgICBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkJykgewogICAgICAgIC8vIOS4gOe6p+inhOWImeWPquiDvemAieaLqemimOW6kwogICAgICAgIHJldHVybiBydWxlVHlwZSA9PT0gMwogICAgICB9CgogICAgICAvLyDlm7rlrpror5Xljbfpmo/mnLrmir3popjkuIDnuqfop4TliJnml7bnmoTpgLvovpEKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZEZpeGVkUmFuZG9tJykgewogICAgICAgIC8vIOS4gOe6p+inhOWImeWPquiDvemAieaLqemimOW6kwogICAgICAgIHJldHVybiBydWxlVHlwZSA9PT0gMwogICAgICB9CgogICAgICAvLyDlm7rlrpror5Xljbfpmo/mnLrmir3popjlrZDop4TliJnml7bnmoTpgLvovpEKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZEZpeGVkUmFuZG9tU3ViJyAmJiB0aGlzLnBhcmVudFJ1bGUpIHsKICAgICAgICAvLyDpopjlupPop4TliJnnmoTlrZDop4TliJnlj6/ku6XmmK/popjlnovop4TliJkKICAgICAgICBpZiAodGhpcy5wYXJlbnRSdWxlLnR5cGUgPT09IDMpIHsKICAgICAgICAgIHJldHVybiBydWxlVHlwZSA9PT0gMQogICAgICAgIH0KICAgICAgICAvLyDlhbbku5bmg4XlhrXkuI3og73pgInmi6nniLbop4TliJnnmoTnsbvlnosKICAgICAgICByZXR1cm4gcnVsZVR5cGUgIT09IHRoaXMucGFyZW50UnVsZS50eXBlCiAgICAgIH0KCiAgICAgIC8vIOWFtuS7luaDheWGteaYvuekuuaJgOacieexu+WeiwogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKCiAgICAvKiog5piv5ZCm5pi+56S654m55a6a6aKY5Z6L6YCJ6aG5ICovCiAgICBzaG91bGRTaG93UXVlc3Rpb25UeXBlKHF1ZXN0aW9uVHlwZSkgewogICAgICAvLyDlpoLmnpzkuI3mmK/mt7vliqDlrZDop4TliJnvvIzmmL7npLrmiYDmnInpopjlnosKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiAhPT0gJ2FkZFN1YicgJiYgdGhpcy5jdXJyZW50T3BlcmF0aW9uICE9PSAnYWRkRml4ZWRSYW5kb21TdWInKSB7CiAgICAgICAgcmV0dXJuIHRydWUKICAgICAgfQoKICAgICAgaWYgKCF0aGlzLnBhcmVudFJ1bGUpIHsKICAgICAgICByZXR1cm4gdHJ1ZQogICAgICB9CgogICAgICAvLyDojrflj5bniLbop4TliJnkuIvlt7LmnInnmoTpopjlnovlrZDop4TliJnkuK3pgInmi6nnmoTpopjlnosKICAgICAgY29uc3QgZXhpc3RpbmdRdWVzdGlvblR5cGVzID0gW10KICAgICAgaWYgKHRoaXMucGFyZW50UnVsZS5jaGlsZHJlbikgewogICAgICAgIHRoaXMucGFyZW50UnVsZS5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHsKICAgICAgICAgIGlmIChjaGlsZC50eXBlID09PSAxICYmIGNoaWxkLnNlbGVjdGVkUXVlc3Rpb25UeXBlcykgewogICAgICAgICAgICBleGlzdGluZ1F1ZXN0aW9uVHlwZXMucHVzaCguLi5jaGlsZC5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQoKICAgICAgLy8g5aaC5p6c6K+l6aKY5Z6L5bey57uP6KKr6YCJ5oup6L+H77yM5YiZ6ZqQ6JePCiAgICAgIHJldHVybiAhZXhpc3RpbmdRdWVzdGlvblR5cGVzLmluY2x1ZGVzKHF1ZXN0aW9uVHlwZSkKICAgIH0sCgogICAgLyoqIOWIpOaWreihqOagvOihjOaYr+WQpuWPr+mAieaLqSAqLwogICAgaXNSb3dTZWxlY3RhYmxlKHJvdykgewogICAgICAvLyDlpoLmnpzmmK/nvJbovpHmk43kvZzvvIzlhYHorrjpgInmi6kKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2VkaXQnKSB7CiAgICAgICAgcmV0dXJuIHRydWUKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5piv5re75Yqg5a2Q6KeE5YiZ77yM5YWB6K646YCJ5oup77yI5a2Q6KeE5YiZ5LiN5raJ5Y+K6aKY5bqT6YCJ5oup77yJCiAgICAgIGlmICh0aGlzLmN1cnJlbnRPcGVyYXRpb24gPT09ICdhZGRTdWInIHx8IHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZEZpeGVkUmFuZG9tU3ViJykgewogICAgICAgIHJldHVybiB0cnVlCiAgICAgIH0KCiAgICAgIC8vIOWmguaenOaYr+WbuuWumuivleWNt+maj+acuuaKvemimO+8jOajgOafpeivpemimOW6k+aYr+WQpuW3sue7j+iiq+S9v+eUqAogICAgICBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkRml4ZWRSYW5kb20nKSB7CiAgICAgICAgcmV0dXJuICF0aGlzLnVzZWRGaXhlZFJhbmRvbUJhbmtJZHMuaW5jbHVkZXMocm93LmJhbmtJZCkKICAgICAgfQoKICAgICAgLy8g5aaC5p6c5piv5re75Yqg5LiA57qn6KeE5YiZ77yM5qOA5p+l6aKY5bqT5piv5ZCm5bey6KKr5L2/55SoCiAgICAgIHJldHVybiAhdGhpcy51c2VkQmFua0lkcy5pbmNsdWRlcyhyb3cuYmFua0lkKQogICAgfSwKCiAgICAvKiog6I635Y+W6KGo5qC86KGM55qE5qC35byP57G75ZCNICovCiAgICBnZXRSb3dDbGFzc05hbWUoeyByb3cgfSkgewogICAgICAvLyDlpoLmnpzpopjlupPlt7Looqvkvb/nlKjkuJTkuI3mmK/nvJbovpHmk43kvZzvvIzmt7vliqDnpoHnlKjmoLflvI8KICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiAhPT0gJ2VkaXQnICYmIHRoaXMuY3VycmVudE9wZXJhdGlvbiAhPT0gJ2FkZFN1YicgJiYgdGhpcy51c2VkQmFua0lkcy5pbmNsdWRlcyhyb3cuYmFua0lkKSkgewogICAgICAgIHJldHVybiAnZGlzYWJsZWQtcm93JwogICAgICB9CiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvKiog5pu05paw6aKY5Z6L6aKY55uu5pWw6YePICovCiAgICBhc3luYyB1cGRhdGVRdWVzdGlvblR5cGVDb3VudCgpIHsKICAgICAgaWYgKHRoaXMucnVsZUZvcm0ucnVsZVR5cGUgIT09IDEgfHwgIXRoaXMucnVsZUZvcm0uc2VsZWN0ZWRRdWVzdGlvblR5cGVzLmxlbmd0aCkgewogICAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZUNvdW50ID0gMAogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDojrflj5bpopjlupPkv6Hmga8KICAgICAgbGV0IHNlbGVjdGVkQmFua3MgPSBbXQogICAgICBpZiAoKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZFN1YicgfHwgdGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnYWRkRml4ZWRSYW5kb21TdWInKSAmJiB0aGlzLnBhcmVudFJ1bGUgJiYgdGhpcy5wYXJlbnRSdWxlLnR5cGUgPT09IDMpIHsKICAgICAgICAvLyDmt7vliqDlrZDop4TliJnml7bvvIzkvb/nlKjniLbop4TliJnnmoTpopjlupMKICAgICAgICBzZWxlY3RlZEJhbmtzID0gdGhpcy5wYXJlbnRSdWxlLnNlbGVjdGVkQmFua3MgfHwgW10KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhbbku5bmg4XlhrXkvb/nlKjlvZPliY3pgInmi6nnmoTpopjlupMKICAgICAgICBzZWxlY3RlZEJhbmtzID0gdGhpcy5zZWxlY3RlZEJhbmtzCiAgICAgIH0KCiAgICAgIGlmICghc2VsZWN0ZWRCYW5rcy5sZW5ndGgpIHsKICAgICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvblR5cGVDb3VudCA9IDAKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqemimOW6kycpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgY291bnQgPSBhd2FpdCB0aGlzLmdldFF1ZXN0aW9uQ291bnRCeVR5cGUoc2VsZWN0ZWRCYW5rcywgdGhpcy5ydWxlRm9ybS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMpCiAgICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlQ291bnQgPSBjb3VudAoKICAgICAgICBpZiAoY291bnQgPT09IDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5omA6YCJ6aKY5Z6L5Zyo5b2T5YmN6aKY5bqT5Lit5rKh5pyJ5Y+v55So6aKY55uuJykKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5p+l6K+i6aKY55uu5pWw6YeP5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZUNvdW50ID0gMAogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+afpeivoumimOebruaVsOmHj+Wksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOagueaNrumimOW6k+WSjOmimOWei+afpeivoumimOebruaVsOmHjyAqLwogICAgYXN5bmMgZ2V0UXVlc3Rpb25Db3VudEJ5VHlwZShiYW5rcywgcXVlc3Rpb25UeXBlcykgewogICAgICBpZiAoIWJhbmtzLmxlbmd0aCB8fCAhcXVlc3Rpb25UeXBlcy5sZW5ndGgpIHsKICAgICAgICByZXR1cm4gMAogICAgICB9CgogICAgICBsZXQgdG90YWxDb3VudCA9IDAKCiAgICAgIC8vIOmBjeWOhuavj+S4qumimOW6k++8jOafpeivoumimOebrue7n+iuoQogICAgICBmb3IgKGNvbnN0IGJhbmsgb2YgYmFua3MpIHsKICAgICAgICB0cnkgewogICAgICAgICAgLy8g5L2/55So5bey5a+85YWl55qEQVBJ5pa55rOVCiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFF1ZXN0aW9uU3RhdGlzdGljcyhiYW5rLmJhbmtJZCkKICAgICAgICAgIGNvbnNvbGUubG9nKGDpopjlupMke2JhbmsuYmFua0lkfee7n+iuoeaVsOaNrjpgLCByZXNwb25zZSkKCiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgY29uc3Qgc3RhdGlzdGljcyA9IHJlc3BvbnNlLmRhdGEKCiAgICAgICAgICAgIC8vIOagueaNrumAieaLqeeahOmimOWei+e0r+WKoOaVsOmHjwogICAgICAgICAgICBxdWVzdGlvblR5cGVzLmZvckVhY2godHlwZSA9PiB7CiAgICAgICAgICAgICAgc3dpdGNoICh0eXBlKSB7CiAgICAgICAgICAgICAgICBjYXNlICcxJzogLy8g5Y2V6YCJ6aKYCiAgICAgICAgICAgICAgICAgIHRvdGFsQ291bnQgKz0gc3RhdGlzdGljcy5zaW5nbGVDaG9pY2UgfHwgMAogICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgICAgY2FzZSAnMic6IC8vIOWkmumAiemimAogICAgICAgICAgICAgICAgICB0b3RhbENvdW50ICs9IHN0YXRpc3RpY3MubXVsdGlwbGVDaG9pY2UgfHwgMAogICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgICAgY2FzZSAnMyc6IC8vIOWIpOaWremimAogICAgICAgICAgICAgICAgICB0b3RhbENvdW50ICs9IHN0YXRpc3RpY3MuanVkZ21lbnQgfHwgMAogICAgICAgICAgICAgICAgICBicmVhawogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgY29uc29sZS5lcnJvcihg5p+l6K+i6aKY5bqTJHtiYW5rLmJhbmtJZH3nu5/orqHkv6Hmga/lpLHotKU6YCwgZXJyb3IpCiAgICAgICAgfQogICAgICB9CgogICAgICBjb25zb2xlLmxvZyhg5oC76aKY55uu5pWw6YePOiAke3RvdGFsQ291bnR9YCkKICAgICAgcmV0dXJuIHRvdGFsQ291bnQKICAgIH0sCgogICAgLyoqIOaYr+WQpuemgeeUqOinhOWImeexu+WeiyAqLwogICAgc2hvdWxkRGlzYWJsZVJ1bGVUeXBlKHJ1bGVUeXBlKSB7CiAgICAgIC8vIOe8lui+keaXtuS4jeiDveabtOaUueexu+WeiwogICAgICBpZiAodGhpcy5jdXJyZW50T3BlcmF0aW9uID09PSAnZWRpdCcpIHsKICAgICAgICByZXR1cm4gdGhpcy5lZGl0aW5nUnVsZS50eXBlICE9PSBydWxlVHlwZQogICAgICB9CgogICAgICAvLyDmt7vliqDkuIDnuqfop4TliJnml7bvvIzlj6rog73pgInmi6npopjlupPvvIznpoHnlKjlhbbku5bnsbvlnosKICAgICAgaWYgKHRoaXMuY3VycmVudE9wZXJhdGlvbiA9PT0gJ2FkZCcpIHsKICAgICAgICByZXR1cm4gcnVsZVR5cGUgIT09IDMKICAgICAgfQoKICAgICAgcmV0dXJuIGZhbHNlCiAgICB9LAogICAgCiAgICAvKiog5YWo6YCJL+WPlua2iOWFqOmAiSAqLwogICAgaGFuZGxlU2VsZWN0QWxsKCkgewogICAgICB0aGlzLmZpeGVkUXVlc3Rpb25zLmZvckVhY2gocXVlc3Rpb24gPT4gewogICAgICAgIHF1ZXN0aW9uLnNlbGVjdGVkID0gdGhpcy5zZWxlY3RBbGwKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOWIoOmZpOmAieS4remimOebriAqLwogICAgaGFuZGxlRGVsZXRlU2VsZWN0ZWQoKSB7CiAgICAgIGNvbnN0IHNlbGVjdGVkUXVlc3Rpb25zID0gdGhpcy5maXhlZFF1ZXN0aW9ucy5maWx0ZXIocSA9PiBxLnNlbGVjdGVkKQogICAgICBpZiAoc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliKDpmaTnmoTpopjnm64nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKGDnoa7lrprliKDpmaTpgInkuK3nmoQgJHtzZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebruWQl++8n2AsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmZpeGVkUXVlc3Rpb25zID0gdGhpcy5maXhlZFF1ZXN0aW9ucy5maWx0ZXIocSA9PiAhcS5zZWxlY3RlZCkKICAgICAgICB0aGlzLnNlbGVjdEFsbCA9IGZhbHNlCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDmiYvliqjpgInpopggKi8KICAgIGhhbmRsZU1hbnVhbFNlbGVjdCgpIHsKICAgICAgdGhpcy5zaG93TWFudWFsU2VsZWN0RGlhbG9nID0gdHJ1ZQogICAgICB0aGlzLmluaXRNYW51YWxTZWxlY3REYXRhKCkKICAgIH0sCgogICAgLyoqIOWIneWni+WMluaJi+WKqOmAiemimOaVsOaNriAqLwogICAgaW5pdE1hbnVhbFNlbGVjdERhdGEoKSB7CiAgICAgIC8vIOmHjee9ruaVsOaNrgogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5zZWxlY3RlZENhdGVnb3J5ID0gJycKICAgICAgdGhpcy5tYW51YWxTZWxlY3QuYmFua1NlYXJjaEtleXdvcmQgPSAnJwogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvblR5cGUgPSAnJwogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5kaWZmaWN1bHR5ID0gJycKICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25TZWFyY2hLZXl3b3JkID0gJycKICAgICAgdGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRRdWVzdGlvbnMgPSBbXQogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5zZWxlY3RlZFN0YXRzID0ge30KCiAgICAgIC8vIOWKoOi9veWIhuexu+aVsOaNrgogICAgICB0aGlzLmxvYWRDYXRlZ29yeVRyZWUoKQoKICAgICAgLy8g5Yqg6L296aKY5bqT5YiX6KGoCiAgICAgIHRoaXMubG9hZE1hbnVhbFNlbGVjdFF1ZXN0aW9uQmFua3MoKQogICAgfSwKCiAgICAvKiog5Yqg6L295YiG57G75qCR5pWw5o2uICovCiAgICBsb2FkQ2F0ZWdvcnlUcmVlKCkgewogICAgICBsaXN0Q2F0ZWdvcnkoeyBwYWdlU2l6ZTogMTAwMCB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBjb25zdCBjYXRlZ29yaWVzID0gcmVzcG9uc2Uucm93cyB8fCBbXQogICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gdGhpcy5idWlsZENhdGVnb3J5VHJlZShjYXRlZ29yaWVzKQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295YiG57G75pWw5o2u5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gW10KICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaehOW7uuWIhuexu+agkSAqLwogICAgYnVpbGRDYXRlZ29yeVRyZWUoY2F0ZWdvcmllcykgewogICAgICBjb25zdCBtYXAgPSB7fQoKICAgICAgLy8g5YWI5bCG5omA5pyJ5YiG57G75pS+5YWlbWFw5LitCiAgICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7CiAgICAgICAgbWFwW2NhdGVnb3J5LmlkXSA9IHsgLi4uY2F0ZWdvcnksIGNoaWxkcmVuOiBbXSB9CiAgICAgIH0pCgogICAgICAvLyDmnoTlu7rlrozmlbTnmoTmoJHlvaLnu5PmnoQKICAgICAgY29uc3QgcmVzdWx0ID0gW10KICAgICAgY2F0ZWdvcmllcy5mb3JFYWNoKGNhdGVnb3J5ID0+IHsKICAgICAgICBpZiAoY2F0ZWdvcnkucGFyZW50SWQgPT09IDApIHsKICAgICAgICAgIC8vIOmhtue6p+WIhuexuwogICAgICAgICAgcmVzdWx0LnB1c2gobWFwW2NhdGVnb3J5LmlkXSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5a2Q5YiG57G7CiAgICAgICAgICBpZiAobWFwW2NhdGVnb3J5LnBhcmVudElkXSkgewogICAgICAgICAgICBtYXBbY2F0ZWdvcnkucGFyZW50SWRdLmNoaWxkcmVuLnB1c2gobWFwW2NhdGVnb3J5LmlkXSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pCgogICAgICAvLyDmuIXnkIbnqbrnmoRjaGlsZHJlbuaVsOe7hAogICAgICBjb25zdCBjbGVhbkVtcHR5Q2hpbGRyZW4gPSAobm9kZSkgPT4gewogICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbgogICAgICAgIH0gZWxzZSBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgIG5vZGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiBjbGVhbkVtcHR5Q2hpbGRyZW4oY2hpbGQpKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5riF55CG5omA5pyJ6IqC54K555qE56m6Y2hpbGRyZW4KICAgICAgcmVzdWx0LmZvckVhY2gobm9kZSA9PiBjbGVhbkVtcHR5Q2hpbGRyZW4obm9kZSkpCgogICAgICByZXR1cm4gcmVzdWx0CiAgICB9LAoKICAgIC8qKiDojrflj5bmiYDmnInlrZDliIbnsbtJRCAqLwogICAgZ2V0QWxsQ2hpbGRDYXRlZ29yeUlkcyhjYXRlZ29yeUlkLCBjYXRlZ29yaWVzKSB7CiAgICAgIGNvbnN0IHJlc3VsdCA9IFtjYXRlZ29yeUlkXQoKICAgICAgY29uc3QgZmluZENoaWxkcmVuID0gKHBhcmVudElkKSA9PiB7CiAgICAgICAgY2F0ZWdvcmllcy5mb3JFYWNoKGNhdGVnb3J5ID0+IHsKICAgICAgICAgIGlmIChjYXRlZ29yeS5wYXJlbnRJZCA9PT0gcGFyZW50SWQpIHsKICAgICAgICAgICAgcmVzdWx0LnB1c2goY2F0ZWdvcnkuaWQpCiAgICAgICAgICAgIGZpbmRDaGlsZHJlbihjYXRlZ29yeS5pZCkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CgogICAgICBmaW5kQ2hpbGRyZW4oY2F0ZWdvcnlJZCkKICAgICAgcmV0dXJuIHJlc3VsdAogICAgfSwKCiAgICAvKiog5Yqg6L295omL5Yqo6YCJ6aKY55qE6aKY5bqT5YiX6KGoICovCiAgICBsb2FkTWFudWFsU2VsZWN0UXVlc3Rpb25CYW5rcygpIHsKICAgICAgbGV0IGNhdGVnb3J5SWRzID0gW10KCiAgICAgIC8vIOWmguaenOmAieaLqeS6huWIhuexu++8jOiOt+WPluivpeWIhuexu+WPiuWFtuaJgOacieWtkOWIhuexu+eahElECiAgICAgIGlmICh0aGlzLm1hbnVhbFNlbGVjdC5zZWxlY3RlZENhdGVnb3J5KSB7CiAgICAgICAgLy8g6I635Y+W5Y6f5aeL5YiG57G75pWw5o2u77yI5YyF5ZCr5omA5pyJ5bGC57qn77yJCiAgICAgICAgbGlzdENhdGVnb3J5KHsgcGFnZVNpemU6IDEwMDAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zdCBhbGxDYXRlZ29yaWVzID0gcmVzcG9uc2Uucm93cyB8fCBbXQogICAgICAgICAgY2F0ZWdvcnlJZHMgPSB0aGlzLmdldEFsbENoaWxkQ2F0ZWdvcnlJZHModGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRDYXRlZ29yeSwgYWxsQ2F0ZWdvcmllcykKCiAgICAgICAgICAvLyDmiafooYzlpJrmrKHmn6Xor6LvvIzlm6DkuLrlkI7nq6/kuI3mlK/mjIFJTuafpeivogogICAgICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rc0J5Q2F0ZWdvcmllcyhjYXRlZ29yeUlkcykKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3liIbnsbvmlbDmja7lpLHotKU6JywgZXJyb3IpCiAgICAgICAgICB0aGlzLmxvYWRRdWVzdGlvbkJhbmtzQnlDYXRlZ29yaWVzKFtdKQogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5rKh5pyJ6YCJ5oup5YiG57G777yM5Yqg6L295omA5pyJ6aKY5bqTCiAgICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rc0J5Q2F0ZWdvcmllcyhbXSkKICAgICAgfQogICAgfSwKCiAgICAvKiog5qC55o2u5YiG57G7SUTliJfooajliqDovb3popjlupMgKi8KICAgIGxvYWRRdWVzdGlvbkJhbmtzQnlDYXRlZ29yaWVzKGNhdGVnb3J5SWRzKSB7CiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IHRoaXMubWFudWFsU2VsZWN0LmJhbmtQYWdpbmF0aW9uLnBhZ2VOdW0sCiAgICAgICAgcGFnZVNpemU6IHRoaXMubWFudWFsU2VsZWN0LmJhbmtQYWdpbmF0aW9uLnBhZ2VTaXplLAogICAgICAgIGJhbmtOYW1lOiB0aGlzLm1hbnVhbFNlbGVjdC5iYW5rU2VhcmNoS2V5d29yZCB8fCB1bmRlZmluZWQKICAgICAgfQoKICAgICAgaWYgKGNhdGVnb3J5SWRzLmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDlpoLmnpzmnInliIbnsbvnrZvpgInvvIzpnIDopoHlkIjlubblpJrkuKrliIbnsbvnmoTnu5PmnpwKICAgICAgICBjb25zdCBwcm9taXNlcyA9IGNhdGVnb3J5SWRzLm1hcChjYXRlZ29yeUlkID0+IHsKICAgICAgICAgIHJldHVybiBsaXN0UXVlc3Rpb25CYW5rKHsgLi4ucXVlcnlQYXJhbXMsIGNhdGVnb3J5SWQgfSkKICAgICAgICB9KQoKICAgICAgICBQcm9taXNlLmFsbChwcm9taXNlcykudGhlbihyZXNwb25zZXMgPT4gewogICAgICAgICAgY29uc3QgYWxsQmFua3MgPSBbXQogICAgICAgICAgbGV0IHRvdGFsQ291bnQgPSAwCgogICAgICAgICAgcmVzcG9uc2VzLmZvckVhY2gocmVzcG9uc2UgPT4gewogICAgICAgICAgICBpZiAocmVzcG9uc2Uucm93cykgewogICAgICAgICAgICAgIGFsbEJhbmtzLnB1c2goLi4ucmVzcG9uc2Uucm93cykKICAgICAgICAgICAgICB0b3RhbENvdW50ICs9IHJlc3BvbnNlLnRvdGFsIHx8IDAKICAgICAgICAgICAgfQogICAgICAgICAgfSkKCiAgICAgICAgICAvLyDljrvph43vvIjmoLnmja5iYW5rSWTvvIkKICAgICAgICAgIGNvbnN0IHVuaXF1ZUJhbmtzID0gYWxsQmFua3MuZmlsdGVyKChiYW5rLCBpbmRleCwgc2VsZikgPT4KICAgICAgICAgICAgaW5kZXggPT09IHNlbGYuZmluZEluZGV4KGIgPT4gYi5iYW5rSWQgPT09IGJhbmsuYmFua0lkKQogICAgICAgICAgKQoKICAgICAgICAgIHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uQmFua3MgPSB1bmlxdWVCYW5rcwogICAgICAgICAgdGhpcy5tYW51YWxTZWxlY3QuYmFua1BhZ2luYXRpb24udG90YWwgPSB1bmlxdWVCYW5rcy5sZW5ndGgKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3popjlupPliJfooajlpLHotKU6JywgZXJyb3IpCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3popjlupPliJfooajlpLHotKUnKQogICAgICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25CYW5rcyA9IFtdCiAgICAgICAgICB0aGlzLm1hbnVhbFNlbGVjdC5iYW5rUGFnaW5hdGlvbi50b3RhbCA9IDAKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOayoeacieWIhuexu+etm+mAie+8jOebtOaOpeafpeivogogICAgICAgIGxpc3RRdWVzdGlvbkJhbmsocXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25CYW5rcyA9IHJlc3BvbnNlLnJvd3MgfHwgW10KICAgICAgICAgIHRoaXMubWFudWFsU2VsZWN0LmJhbmtQYWdpbmF0aW9uLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMAogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vemimOW6k+WIl+ihqOWksei0pTonLCBlcnJvcikKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vemimOW6k+WIl+ihqOWksei0pScpCiAgICAgICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvbkJhbmtzID0gW10KICAgICAgICAgIHRoaXMubWFudWFsU2VsZWN0LmJhbmtQYWdpbmF0aW9uLnRvdGFsID0gMAogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOaQnOe0oumimOW6kyAqLwogICAgc2VhcmNoUXVlc3Rpb25CYW5rcygpIHsKICAgICAgdGhpcy5tYW51YWxTZWxlY3QuYmFua1BhZ2luYXRpb24ucGFnZU51bSA9IDEKICAgICAgdGhpcy5sb2FkTWFudWFsU2VsZWN0UXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDpgInmi6npopjlupMgKi8KICAgIHNlbGVjdFF1ZXN0aW9uQmFuayhyb3cpIHsKICAgICAgdGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRCYW5rSWQgPSByb3cuYmFua0lkCiAgICAgIHRoaXMubG9hZFF1ZXN0aW9ucygpCiAgICB9LAoKICAgIC8qKiDliqDovb3popjnm67liJfooaggKi8KICAgIGxvYWRRdWVzdGlvbnMoKSB7CiAgICAgIGlmICghdGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRCYW5rSWQpIHsKICAgICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvbnMgPSBbXQogICAgICAgIHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uUGFnaW5hdGlvbi50b3RhbCA9IDAKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB7CiAgICAgICAgcGFnZU51bTogdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25QYWdpbmF0aW9uLnBhZ2VOdW0sCiAgICAgICAgcGFnZVNpemU6IHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uUGFnaW5hdGlvbi5wYWdlU2l6ZSwKICAgICAgICBiYW5rSWQ6IHRoaXMubWFudWFsU2VsZWN0LnNlbGVjdGVkQmFua0lkLAogICAgICAgIHF1ZXN0aW9uVHlwZTogdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25UeXBlIHx8IHVuZGVmaW5lZCwKICAgICAgICBkaWZmaWN1bHR5OiB0aGlzLm1hbnVhbFNlbGVjdC5kaWZmaWN1bHR5IHx8IHVuZGVmaW5lZCwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uU2VhcmNoS2V5d29yZCB8fCB1bmRlZmluZWQKICAgICAgfQoKICAgICAgbGlzdFF1ZXN0aW9uKHF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvbnMgPSByZXNwb25zZS5yb3dzIHx8IFtdCiAgICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25QYWdpbmF0aW9uLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMAogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296aKY55uu5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vemimOebruWIl+ihqOWksei0pScpCiAgICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvblBhZ2luYXRpb24udG90YWwgPSAwCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDmkJzntKLpopjnm64gKi8KICAgIHNlYXJjaFF1ZXN0aW9ucygpIHsKICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25QYWdpbmF0aW9uLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMubG9hZFF1ZXN0aW9ucygpCiAgICB9LAoKICAgIC8qKiDph43nva7popjnm67mkJzntKIgKi8KICAgIHJlc2V0UXVlc3Rpb25TZWFyY2goKSB7CiAgICAgIHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uVHlwZSA9ICcnCiAgICAgIHRoaXMubWFudWFsU2VsZWN0LmRpZmZpY3VsdHkgPSAnJwogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5xdWVzdGlvblNlYXJjaEtleXdvcmQgPSAnJwogICAgICB0aGlzLnNlYXJjaFF1ZXN0aW9ucygpCiAgICB9LAoKICAgIC8qKiDpmo/mnLrmir3popggKi8KICAgIGhhbmRsZVJhbmRvbVNlbGVjdCgpIHsKICAgICAgLy8g5Li65Zu65a6a6K+V5Y235omT5byA6ZqP5py66KeE5YiZ6K6+572u5a+56K+d5qGGCiAgICAgIHRoaXMuc2hvd0ZpeGVkUmFuZG9tRGlhbG9nID0gdHJ1ZQogICAgICB0aGlzLnJlc2V0Rml4ZWRSYW5kb21Gb3JtKCkKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDph43nva7lm7rlrpror5Xljbfpmo/mnLrmir3popjooajljZUgKi8KICAgIHJlc2V0Rml4ZWRSYW5kb21Gb3JtKCkgewogICAgICB0aGlzLmZpeGVkUmFuZG9tUnVsZXMgPSBbXQogICAgICB0aGlzLmZpeGVkUmFuZG9tRm9ybSA9IHsKICAgICAgICBydWxlVHlwZTogMywKICAgICAgICBnZW5lcmF0ZVR5cGU6ICdkaXZpZGVkJywKICAgICAgICBzZWxlY3RlZEl0ZW1zOiBbXSwKICAgICAgICBzZWxlY3RlZFF1ZXN0aW9uVHlwZXM6IFtdCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOa3u+WKoOWbuuWumuivleWNt+maj+acuuaKvemimOinhOWImSAqLwogICAgaGFuZGxlQWRkRml4ZWRSYW5kb21SdWxlKCkgewogICAgICB0aGlzLmN1cnJlbnRPcGVyYXRpb24gPSAnYWRkRml4ZWRSYW5kb20nCiAgICAgIHRoaXMuZWRpdGluZ1J1bGUgPSBudWxsCiAgICAgIHRoaXMucnVsZUZvcm0ucnVsZVR5cGUgPSAzIC8vIOWbuuWumuS4uumimOW6k+exu+WeiwogICAgICB0aGlzLnNob3dBZGRSdWxlRGlhbG9nID0gdHJ1ZQogICAgICAvLyDph43nva7mn6Xor6Llj4LmlbAKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gJycKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDmt7vliqDlm7rlrpror5Xljbfpmo/mnLrmir3popjlrZDop4TliJkgKi8KICAgIGhhbmRsZUFkZEZpeGVkUmFuZG9tU3ViUnVsZShwYXJlbnRSdWxlKSB7CiAgICAgIHRoaXMuY3VycmVudE9wZXJhdGlvbiA9ICdhZGRGaXhlZFJhbmRvbVN1YicKICAgICAgdGhpcy5lZGl0aW5nUnVsZSA9IG51bGwKICAgICAgdGhpcy5wYXJlbnRSdWxlID0gcGFyZW50UnVsZQoKICAgICAgLy8g5qC55o2u54i26KeE5YiZ57G75Z6L56Gu5a6a5a2Q6KeE5YiZ57G75Z6LCiAgICAgIGxldCBkZWZhdWx0UnVsZVR5cGUKICAgICAgaWYgKHBhcmVudFJ1bGUudHlwZSA9PT0gMykgewogICAgICAgIC8vIOmimOW6k+inhOWImeeahOWtkOinhOWImeWPquiDveaYr+mimOWei+inhOWImQogICAgICAgIGRlZmF1bHRSdWxlVHlwZSA9IDEKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlhbbku5bmg4XlhrXnmoTpu5jorqTlpITnkIYKICAgICAgICBkZWZhdWx0UnVsZVR5cGUgPSAxCiAgICAgIH0KCiAgICAgIHRoaXMucnVsZUZvcm0ucnVsZVR5cGUgPSBkZWZhdWx0UnVsZVR5cGUKICAgICAgdGhpcy5ydWxlRm9ybS5nZW5lcmF0ZVR5cGUgPSAnZGl2aWRlZCcKICAgICAgdGhpcy5ydWxlRm9ybS5zZWxlY3RlZEl0ZW1zID0gW10KICAgICAgdGhpcy5ydWxlRm9ybS5zZWxlY3RlZFF1ZXN0aW9uVHlwZXMgPSBbXQoKICAgICAgdGhpcy5zaG93QWRkUnVsZURpYWxvZyA9IHRydWUKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rTmFtZTogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgdGhpcy5zZWFyY2hLZXl3b3JkID0gJycKICAgICAgdGhpcy5sb2FkUXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDmm7TmlrDlm7rlrpror5Xljbfpmo/mnLrmir3popjop4TliJnliIbmlbAgKi8KICAgIHVwZGF0ZUZpeGVkUmFuZG9tUnVsZVNjb3JlKHJ1bGUpIHsKICAgICAgcnVsZS50b3RhbFNjb3JlID0gcnVsZS5zZWxlY3RlZENvdW50ICogcnVsZS5zY29yZVBlclF1ZXN0aW9uCiAgICB9LAoKICAgIC8qKiDliKDpmaTlm7rlrpror5Xljbfpmo/mnLrmir3popjop4TliJkgKi8KICAgIGRlbGV0ZUZpeGVkUmFuZG9tUnVsZShydWxlKSB7CiAgICAgIC8vIOWmguaenOaYr+eItuinhOWIme+8jOmcgOimgeWQjOaXtuWIoOmZpOaJgOacieWtkOinhOWImQogICAgICBpZiAocnVsZS5jaGlsZHJlbiAmJiBydWxlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDliKDpmaTmiYDmnInlrZDop4TliJkKICAgICAgICBydWxlLmNoaWxkcmVuLmZvckVhY2goY2hpbGRSdWxlID0+IHsKICAgICAgICAgIGNvbnN0IGNoaWxkSW5kZXggPSB0aGlzLmZpeGVkUmFuZG9tUnVsZXMuZmluZEluZGV4KHIgPT4gci5pZCA9PT0gY2hpbGRSdWxlLmlkKQogICAgICAgICAgaWYgKGNoaWxkSW5kZXggPiAtMSkgewogICAgICAgICAgICB0aGlzLmZpeGVkUmFuZG9tUnVsZXMuc3BsaWNlKGNoaWxkSW5kZXgsIDEpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQoKICAgICAgLy8g5Yig6Zmk54i26KeE5YiZCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5maXhlZFJhbmRvbVJ1bGVzLmZpbmRJbmRleChyID0+IHIuaWQgPT09IHJ1bGUuaWQpCiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgdGhpcy5maXhlZFJhbmRvbVJ1bGVzLnNwbGljZShpbmRleCwgMSkKICAgICAgfQogICAgfSwKCiAgICAvKiog5Yig6Zmk5Zu65a6a6K+V5Y236ZqP5py65oq96aKY5a2Q6KeE5YiZICovCiAgICBkZWxldGVGaXhlZFJhbmRvbUNoaWxkUnVsZShwYXJlbnRSdWxlLCBjaGlsZFJ1bGUpIHsKICAgICAgLy8g5LuO54i26KeE5YiZ55qEY2hpbGRyZW7kuK3liKDpmaQKICAgICAgaWYgKHBhcmVudFJ1bGUuY2hpbGRyZW4pIHsKICAgICAgICBjb25zdCBjaGlsZEluZGV4ID0gcGFyZW50UnVsZS5jaGlsZHJlbi5maW5kSW5kZXgociA9PiByLmlkID09PSBjaGlsZFJ1bGUuaWQpCiAgICAgICAgaWYgKGNoaWxkSW5kZXggPiAtMSkgewogICAgICAgICAgcGFyZW50UnVsZS5jaGlsZHJlbi5zcGxpY2UoY2hpbGRJbmRleCwgMSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOS7juS4u+inhOWImeWIl+ihqOS4reWIoOmZpAogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZml4ZWRSYW5kb21SdWxlcy5maW5kSW5kZXgociA9PiByLmlkID09PSBjaGlsZFJ1bGUuaWQpCiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgdGhpcy5maXhlZFJhbmRvbVJ1bGVzLnNwbGljZShpbmRleCwgMSkKICAgICAgfQogICAgfSwKCiAgICAvKiog56Gu6K6k5Zu65a6a6K+V5Y236ZqP5py65oq96aKYICovCiAgICBhc3luYyBoYW5kbGVDb25maXJtRml4ZWRSYW5kb21TZWxlY3QoKSB7CiAgICAgIGlmICh0aGlzLmZpeGVkUmFuZG9tUnVsZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjmt7vliqDmir3popjop4TliJknKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0cnkgewogICAgICAgIC8vIOagueaNruinhOWImemaj+acuuaKveWPlumimOebrgogICAgICAgIGNvbnN0IHNlbGVjdGVkUXVlc3Rpb25zID0gYXdhaXQgdGhpcy5leHRyYWN0UXVlc3Rpb25zRnJvbVJ1bGVzKHRoaXMuZml4ZWRSYW5kb21SdWxlcykKCiAgICAgICAgaWYgKHNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmoLnmja7lvZPliY3op4TliJnmnKrog73mir3lj5bliLDpopjnm64nKQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQoKICAgICAgICAvLyDlsIbmir3lj5bnmoTpopjnm67mt7vliqDliLDlm7rlrpror5XljbfkuK0KICAgICAgICBzZWxlY3RlZFF1ZXN0aW9ucy5mb3JFYWNoKChxdWVzdGlvbiwgaW5kZXgpID0+IHsKICAgICAgICAgIHRoaXMuZml4ZWRRdWVzdGlvbnMucHVzaCh7CiAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgaW5kZXgsCiAgICAgICAgICAgIHF1ZXN0aW9uSWQ6IHF1ZXN0aW9uLnF1ZXN0aW9uSWQsCiAgICAgICAgICAgIGNvbnRlbnQ6IHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwKICAgICAgICAgICAgdHlwZTogcXVlc3Rpb24ucXVlc3Rpb25UeXBlLAogICAgICAgICAgICBkaWZmaWN1bHR5OiBxdWVzdGlvbi5kaWZmaWN1bHR5LAogICAgICAgICAgICBvcHRpb25zOiBxdWVzdGlvbi5vcHRpb25zLAogICAgICAgICAgICBzY29yZTogcXVlc3Rpb24uc2NvcmUgfHwgMSwgLy8g5L2/55So6KeE5YiZ5Lit6K6+572u55qE5YiG5pWwCiAgICAgICAgICAgIHNlbGVjdGVkOiBmYWxzZSwKICAgICAgICAgICAgZXhwYW5kZWQ6IGZhbHNlCiAgICAgICAgICB9KQogICAgICAgIH0pCgogICAgICAgIHRoaXMuc2hvd0ZpeGVkUmFuZG9tRGlhbG9nID0gZmFsc2UKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+maj+acuuaKveWPluW5tua3u+WKoCAke3NlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfpmo/mnLrmir3popjlpLHotKU6JywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6ZqP5py65oq96aKY5aSx6LSl77yM6K+36YeN6K+VJykKICAgICAgfQogICAgfSwKCiAgICAvKiog5qC55o2u6KeE5YiZ5oq95Y+W6aKY55uuICovCiAgICBhc3luYyBleHRyYWN0UXVlc3Rpb25zRnJvbVJ1bGVzKHJ1bGVzKSB7CiAgICAgIGNvbnN0IGFsbFF1ZXN0aW9ucyA9IFtdCgogICAgICAvLyDlpITnkIbniLbop4TliJnvvIjpopjlupPop4TliJnvvIkKICAgICAgY29uc3QgcGFyZW50UnVsZXMgPSBydWxlcy5maWx0ZXIocnVsZSA9PiAhcnVsZS5wYXJlbnRJZCkKCiAgICAgIGZvciAoY29uc3QgcGFyZW50UnVsZSBvZiBwYXJlbnRSdWxlcykgewogICAgICAgIHRyeSB7CiAgICAgICAgICAvLyDojrflj5blrZDop4TliJkKICAgICAgICAgIGNvbnN0IGNoaWxkUnVsZXMgPSBydWxlcy5maWx0ZXIocnVsZSA9PiBydWxlLnBhcmVudElkID09PSBwYXJlbnRSdWxlLmlkKQoKICAgICAgICAgIGlmIChjaGlsZFJ1bGVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgLy8g5pyJ5a2Q6KeE5YiZ77ya5oyJ5a2Q6KeE5YiZ55qE6aKY5Z6L5LuO54i26KeE5YiZ55qE6aKY5bqT5Lit5oq95Y+WCiAgICAgICAgICAgIGZvciAoY29uc3QgY2hpbGRSdWxlIG9mIGNoaWxkUnVsZXMpIHsKICAgICAgICAgICAgICBsZXQgcXVlc3Rpb25zID0gW10KCiAgICAgICAgICAgICAgLy8g5LuO54i26KeE5YiZ55qE6aKY5bqT5Lit5oyJ5a2Q6KeE5YiZ55qE6aKY5Z6L5oq95Y+W6aKY55uuCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiYW5rIG9mIHBhcmVudFJ1bGUuc2VsZWN0ZWRCYW5rcykgewogICAgICAgICAgICAgICAgZm9yIChjb25zdCBxdWVzdGlvblR5cGUgb2YgY2hpbGRSdWxlLnNlbGVjdGVkUXVlc3Rpb25UeXBlcykgewogICAgICAgICAgICAgICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHsKICAgICAgICAgICAgICAgICAgICBiYW5rSWQ6IGJhbmsuYmFua0lkLAogICAgICAgICAgICAgICAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgICAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBsaXN0UXVlc3Rpb24ocXVlcnlQYXJhbXMpCiAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5yb3dzICYmIHJlc3BvbnNlLnJvd3MubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgIHF1ZXN0aW9ucyA9IHF1ZXN0aW9ucy5jb25jYXQocmVzcG9uc2Uucm93cykKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgLy8g6ZqP5py66YCJ5oup5oyH5a6a5pWw6YeP55qE6aKY55uuCiAgICAgICAgICAgICAgaWYgKHF1ZXN0aW9ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICBjb25zdCBzaHVmZmxlZCA9IHRoaXMuc2h1ZmZsZUFycmF5KFsuLi5xdWVzdGlvbnNdKQogICAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRDb3VudCA9IE1hdGgubWluKGNoaWxkUnVsZS5zZWxlY3RlZENvdW50LCBzaHVmZmxlZC5sZW5ndGgpCiAgICAgICAgICAgICAgICBjb25zdCBzZWxlY3RlZFF1ZXN0aW9ucyA9IHNodWZmbGVkLnNsaWNlKDAsIHNlbGVjdGVkQ291bnQpCgogICAgICAgICAgICAgICAgLy8g5Li65q+P5Liq6aKY55uu6K6+572u5YiG5pWwCiAgICAgICAgICAgICAgICBzZWxlY3RlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICAgICAgICAgICAgcXVlc3Rpb24uc2NvcmUgPSBjaGlsZFJ1bGUuc2NvcmVQZXJRdWVzdGlvbgogICAgICAgICAgICAgICAgfSkKCiAgICAgICAgICAgICAgICBhbGxRdWVzdGlvbnMucHVzaCguLi5zZWxlY3RlZFF1ZXN0aW9ucykKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOayoeacieWtkOinhOWIme+8muebtOaOpeS7jueItuinhOWImeeahOmimOW6k+S4reaKveWPlumimOebrgogICAgICAgICAgICBsZXQgcXVlc3Rpb25zID0gW10KCiAgICAgICAgICAgIGZvciAoY29uc3QgYmFuayBvZiBwYXJlbnRSdWxlLnNlbGVjdGVkQmFua3MpIHsKICAgICAgICAgICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHsKICAgICAgICAgICAgICAgIGJhbmtJZDogYmFuay5iYW5rSWQsCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbGlzdFF1ZXN0aW9uKHF1ZXJ5UGFyYW1zKQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5yb3dzICYmIHJlc3BvbnNlLnJvd3MubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgcXVlc3Rpb25zID0gcXVlc3Rpb25zLmNvbmNhdChyZXNwb25zZS5yb3dzKQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g6ZqP5py66YCJ5oup5oyH5a6a5pWw6YeP55qE6aKY55uuCiAgICAgICAgICAgIGlmIChxdWVzdGlvbnMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIGNvbnN0IHNodWZmbGVkID0gdGhpcy5zaHVmZmxlQXJyYXkoWy4uLnF1ZXN0aW9uc10pCiAgICAgICAgICAgICAgY29uc3Qgc2VsZWN0ZWRDb3VudCA9IE1hdGgubWluKHBhcmVudFJ1bGUuc2VsZWN0ZWRDb3VudCwgc2h1ZmZsZWQubGVuZ3RoKQogICAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkUXVlc3Rpb25zID0gc2h1ZmZsZWQuc2xpY2UoMCwgc2VsZWN0ZWRDb3VudCkKCiAgICAgICAgICAgICAgLy8g5Li65q+P5Liq6aKY55uu6K6+572u5YiG5pWwCiAgICAgICAgICAgICAgc2VsZWN0ZWRRdWVzdGlvbnMuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgICAgICAgICBxdWVzdGlvbi5zY29yZSA9IHBhcmVudFJ1bGUuc2NvcmVQZXJRdWVzdGlvbgogICAgICAgICAgICAgIH0pCgogICAgICAgICAgICAgIGFsbFF1ZXN0aW9ucy5wdXNoKC4uLnNlbGVjdGVkUXVlc3Rpb25zKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aKveWPlumimOebruWksei0pTonLCBlcnJvcikKICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBhbGxRdWVzdGlvbnMKICAgIH0sCgogICAgLyoqIOaVsOe7hOmaj+acuuaOkuW6jyAqLwogICAgc2h1ZmZsZUFycmF5KGFycmF5KSB7CiAgICAgIGZvciAobGV0IGkgPSBhcnJheS5sZW5ndGggLSAxOyBpID4gMDsgaS0tKSB7CiAgICAgICAgY29uc3QgaiA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIChpICsgMSkpOwogICAgICAgIFthcnJheVtpXSwgYXJyYXlbal1dID0gW2FycmF5W2pdLCBhcnJheVtpXV0KICAgICAgfQogICAgICByZXR1cm4gYXJyYXkKICAgIH0sCgogICAgLyoqIOaOkuW6jyAqLwogICAgaGFuZGxlU29ydCgpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmjpLluo/lip/og73lvIDlj5HkuK0uLi4nKQogICAgfSwKCiAgICAvKiog5om56YeP6K6+572u5YiG5pWwICovCiAgICBoYW5kbGVCYXRjaFNldFNjb3JlKCkgewogICAgICBpZiAodGhpcy5maXhlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOmimOebru+8jOaXoOazleiuvue9ruWIhuaVsCcpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruihqOWNlQogICAgICB0aGlzLmJhdGNoU2NvcmVGb3JtID0gewogICAgICAgIHNjb3JlVHlwZTogJ2FsbCcsCiAgICAgICAgc2NvcmU6IDEsCiAgICAgICAgcXVlc3Rpb25UeXBlOiAnJwogICAgICB9CgogICAgICB0aGlzLnNob3dCYXRjaFNjb3JlRGlhbG9nID0gdHJ1ZQogICAgfSwKCiAgICAvKiog6I635Y+W5om56YeP6K6+572u5YiG5pWw5b2x5ZON55qE6aKY55uu5pWw6YePICovCiAgICBnZXRCYXRjaFNjb3JlQWZmZWN0ZWRDb3VudCgpIHsKICAgICAgaWYgKHRoaXMuZml4ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgcmV0dXJuIDAKICAgICAgfQoKICAgICAgc3dpdGNoICh0aGlzLmJhdGNoU2NvcmVGb3JtLnNjb3JlVHlwZSkgewogICAgICAgIGNhc2UgJ2FsbCc6CiAgICAgICAgICByZXR1cm4gdGhpcy5maXhlZFF1ZXN0aW9ucy5sZW5ndGgKICAgICAgICBjYXNlICdzZWxlY3RlZCc6CiAgICAgICAgICByZXR1cm4gdGhpcy5maXhlZFF1ZXN0aW9ucy5maWx0ZXIocSA9PiBxLnNlbGVjdGVkKS5sZW5ndGgKICAgICAgICBjYXNlICdieVR5cGUnOgogICAgICAgICAgaWYgKCF0aGlzLmJhdGNoU2NvcmVGb3JtLnF1ZXN0aW9uVHlwZSkgewogICAgICAgICAgICByZXR1cm4gMAogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuIHRoaXMuZml4ZWRRdWVzdGlvbnMuZmlsdGVyKHEgPT4gcS50eXBlID09IHRoaXMuYmF0Y2hTY29yZUZvcm0ucXVlc3Rpb25UeXBlKS5sZW5ndGgKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuIDAKICAgICAgfQogICAgfSwKCiAgICAvKiog56Gu6K6k5om56YeP6K6+572u5YiG5pWwICovCiAgICBoYW5kbGVDb25maXJtQmF0Y2hTZXRTY29yZSgpIHsKICAgICAgY29uc3QgYWZmZWN0ZWRDb3VudCA9IHRoaXMuZ2V0QmF0Y2hTY29yZUFmZmVjdGVkQ291bnQoKQoKICAgICAgaWYgKGFmZmVjdGVkQ291bnQgPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieespuWQiOadoeS7tueahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGxldCB0YXJnZXRRdWVzdGlvbnMgPSBbXQoKICAgICAgc3dpdGNoICh0aGlzLmJhdGNoU2NvcmVGb3JtLnNjb3JlVHlwZSkgewogICAgICAgIGNhc2UgJ2FsbCc6CiAgICAgICAgICB0YXJnZXRRdWVzdGlvbnMgPSB0aGlzLmZpeGVkUXVlc3Rpb25zCiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgJ3NlbGVjdGVkJzoKICAgICAgICAgIHRhcmdldFF1ZXN0aW9ucyA9IHRoaXMuZml4ZWRRdWVzdGlvbnMuZmlsdGVyKHEgPT4gcS5zZWxlY3RlZCkKICAgICAgICAgIGlmICh0YXJnZXRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB6K6+572u5YiG5pWw55qE6aKY55uuJykKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgJ2J5VHlwZSc6CiAgICAgICAgICBpZiAoIXRoaXMuYmF0Y2hTY29yZUZvcm0ucXVlc3Rpb25UeXBlKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6aKY5Z6LJykKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CiAgICAgICAgICB0YXJnZXRRdWVzdGlvbnMgPSB0aGlzLmZpeGVkUXVlc3Rpb25zLmZpbHRlcihxID0+IHEudHlwZSA9PSB0aGlzLmJhdGNoU2NvcmVGb3JtLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGJyZWFrCiAgICAgIH0KCiAgICAgIC8vIOaJuemHj+iuvue9ruWIhuaVsAogICAgICB0YXJnZXRRdWVzdGlvbnMuZm9yRWFjaChxdWVzdGlvbiA9PiB7CiAgICAgICAgcXVlc3Rpb24uc2NvcmUgPSB0aGlzLmJhdGNoU2NvcmVGb3JtLnNjb3JlCiAgICAgIH0pCgogICAgICB0aGlzLnNob3dCYXRjaFNjb3JlRGlhbG9nID0gZmFsc2UKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/kuLogJHthZmZlY3RlZENvdW50fSDpgZPpopjnm67orr7nva7liIbmlbDkuLogJHt0aGlzLmJhdGNoU2NvcmVGb3JtLnNjb3JlfSDliIZgKQogICAgfSwKCiAgICAvKiog5a+85Ye6ICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5a+85Ye65Yqf6IO95byA5Y+R5LitLi4uJykKICAgIH0sCgogICAgLyoqIOWxleW8gC/mlLbotbcgKi8KICAgIGhhbmRsZVRvZ2dsZUV4cGFuZCgpIHsKICAgICAgdGhpcy5pc0V4cGFuZGVkID0gIXRoaXMuaXNFeHBhbmRlZAogICAgICAvLyDmibnph4/orr7nva7miYDmnInpopjnm67nmoTlsZXlvIDnirbmgIEKICAgICAgdGhpcy5maXhlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICBxdWVzdGlvbi5leHBhbmRlZCA9IHRoaXMuaXNFeHBhbmRlZAogICAgICB9KQogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oYOW3siR7dGhpcy5pc0V4cGFuZGVkID8gJ+WxleW8gCcgOiAn5pS26LW3J33miYDmnInpopjnm65gKQogICAgfSwKCiAgICAvKiog6aKY55uu6YCJ5oup5Y+Y5YyWICovCiAgICBoYW5kbGVRdWVzdGlvblNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRRdWVzdGlvbnMgPSBzZWxlY3Rpb24KICAgICAgdGhpcy51cGRhdGVTZWxlY3RlZFN0YXRzKCkKICAgIH0sCgogICAgLyoqIOabtOaWsOmAieS4remimOebrue7n+iuoSAqLwogICAgdXBkYXRlU2VsZWN0ZWRTdGF0cygpIHsKICAgICAgY29uc3Qgc3RhdHMgPSB7fQogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5zZWxlY3RlZFF1ZXN0aW9ucy5mb3JFYWNoKHF1ZXN0aW9uID0+IHsKICAgICAgICBjb25zdCB0eXBlID0gcXVlc3Rpb24ucXVlc3Rpb25UeXBlCiAgICAgICAgc3RhdHNbdHlwZV0gPSAoc3RhdHNbdHlwZV0gfHwgMCkgKyAxCiAgICAgIH0pCiAgICAgIHRoaXMubWFudWFsU2VsZWN0LnNlbGVjdGVkU3RhdHMgPSBzdGF0cwogICAgfSwKCiAgICAvKiog56Gu6K6k5omL5Yqo6YCJ6aKYICovCiAgICBjb25maXJtTWFudWFsU2VsZWN0KCkgewogICAgICBpZiAodGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6npopjnm64nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDlsIbpgInkuK3nmoTpopjnm67mt7vliqDliLDlm7rlrpror5XljbfkuK0KICAgICAgdGhpcy5tYW51YWxTZWxlY3Quc2VsZWN0ZWRRdWVzdGlvbnMuZm9yRWFjaCgocXVlc3Rpb24sIGluZGV4KSA9PiB7CiAgICAgICAgdGhpcy5maXhlZFF1ZXN0aW9ucy5wdXNoKHsKICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgaW5kZXgsCiAgICAgICAgICBxdWVzdGlvbklkOiBxdWVzdGlvbi5xdWVzdGlvbklkLAogICAgICAgICAgY29udGVudDogcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50LAogICAgICAgICAgdHlwZTogcXVlc3Rpb24ucXVlc3Rpb25UeXBlLAogICAgICAgICAgZGlmZmljdWx0eTogcXVlc3Rpb24uZGlmZmljdWx0eSwKICAgICAgICAgIG9wdGlvbnM6IHF1ZXN0aW9uLm9wdGlvbnMsIC8vIOS/neWtmOmAiemhueS/oeaBrwogICAgICAgICAgc2NvcmU6IDEsIC8vIOm7mOiupDHliIYKICAgICAgICAgIHNlbGVjdGVkOiBmYWxzZSwKICAgICAgICAgIGV4cGFuZGVkOiBmYWxzZSAvLyDpu5jorqTmlLbotbfnirbmgIEKICAgICAgICB9KQogICAgICB9KQoKICAgICAgdGhpcy5zaG93TWFudWFsU2VsZWN0RGlhbG9nID0gZmFsc2UKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/mt7vliqAgJHt0aGlzLm1hbnVhbFNlbGVjdC5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebrmApCiAgICB9LAoKICAgIC8qKiDpopjlupPliIbpobXlpKflsI/lj5jljJYgKi8KICAgIGhhbmRsZUJhbmtTaXplQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5iYW5rUGFnaW5hdGlvbi5wYWdlU2l6ZSA9IHZhbAogICAgICB0aGlzLm1hbnVhbFNlbGVjdC5iYW5rUGFnaW5hdGlvbi5wYWdlTnVtID0gMQogICAgICB0aGlzLmxvYWRNYW51YWxTZWxlY3RRdWVzdGlvbkJhbmtzKCkKICAgIH0sCgogICAgLyoqIOmimOW6k+W9k+WJjemhteWPmOWMliAqLwogICAgaGFuZGxlQmFua0N1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMubWFudWFsU2VsZWN0LmJhbmtQYWdpbmF0aW9uLnBhZ2VOdW0gPSB2YWwKICAgICAgdGhpcy5sb2FkTWFudWFsU2VsZWN0UXVlc3Rpb25CYW5rcygpCiAgICB9LAoKICAgIC8qKiDpopjnm67liIbpobXlpKflsI/lj5jljJYgKi8KICAgIGhhbmRsZVF1ZXN0aW9uU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5tYW51YWxTZWxlY3QucXVlc3Rpb25QYWdpbmF0aW9uLnBhZ2VTaXplID0gdmFsCiAgICAgIHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uUGFnaW5hdGlvbi5wYWdlTnVtID0gMQogICAgICB0aGlzLmxvYWRRdWVzdGlvbnMoKQogICAgfSwKCiAgICAvKiog6aKY55uu5b2T5YmN6aG15Y+Y5YyWICovCiAgICBoYW5kbGVRdWVzdGlvbkN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMubWFudWFsU2VsZWN0LnF1ZXN0aW9uUGFnaW5hdGlvbi5wYWdlTnVtID0gdmFsCiAgICAgIHRoaXMubG9hZFF1ZXN0aW9ucygpCiAgICB9LAoKICAgIC8qKiDojrflj5bpmr7luqbmlofmnKwgKi8KICAgIGdldERpZmZpY3VsdHlUZXh0KGRpZmZpY3VsdHkpIHsKICAgICAgY29uc3QgZGlmZmljdWx0eU1hcCA9IHsgJzEnOiAn566A5Y2VJywgJzInOiAn5Lit562JJywgJzMnOiAn5Zuw6Zq+JywgMTogJ+eugOWNlScsIDI6ICfkuK3nrYknLCAzOiAn5Zuw6Zq+JyB9CiAgICAgIHJldHVybiBkaWZmaWN1bHR5TWFwW2RpZmZpY3VsdHldIHx8ICfmnKrnn6UnCiAgICB9LAoKICAgIC8qKiDmm7TmlrDpopjnm67liIbmlbAgKi8KICAgIHVwZGF0ZVF1ZXN0aW9uU2NvcmUocXVlc3Rpb24pIHsKICAgICAgLy8g5YiG5pWw5pu05paw5ZCO5Y+v5Lul6Kem5Y+R5oC75YiG6YeN5paw6K6h566XCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgIH0sCgogICAgLyoqIOWIh+aNoumimOebruWxleW8gC/mlLbotbfnirbmgIEgKi8KICAgIHRvZ2dsZVF1ZXN0aW9uRXhwYW5kKHF1ZXN0aW9uKSB7CiAgICAgIHF1ZXN0aW9uLmV4cGFuZGVkID0gIXF1ZXN0aW9uLmV4cGFuZGVkCiAgICB9LAoKICAgIC8qKiDliKDpmaTljZXkuKrpopjnm64gKi8KICAgIGRlbGV0ZVF1ZXN0aW9uKHF1ZXN0aW9uLCBpbmRleCkgewogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTov5npgZPpopjnm67lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5maXhlZFF1ZXN0aW9ucy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgLy8g55So5oi35Y+W5raI5Yig6ZmkCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDop6PmnpDpgInpoblKU09O5a2X56ym5LiyICovCiAgICBwYXJzZU9wdGlvbnMob3B0aW9uc1N0cikgewogICAgICB0cnkgewogICAgICAgIGlmICh0eXBlb2Ygb3B0aW9uc1N0ciA9PT0gJ3N0cmluZycpIHsKICAgICAgICAgIHJldHVybiBKU09OLnBhcnNlKG9wdGlvbnNTdHIpCiAgICAgICAgfQogICAgICAgIHJldHVybiBvcHRpb25zU3RyIHx8IFtdCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6YCJ6aG55aSx6LSlOicsIGVycm9yKQogICAgICAgIHJldHVybiBbXQogICAgICB9CiAgICB9LAoKICAgIC8qKiDliKTmlq3popjnm67mmK/lkKblj6/pgInmi6kgKi8KICAgIGlzUXVlc3Rpb25TZWxlY3RhYmxlKHJvdykgewogICAgICAvLyDmo4Dmn6Xpopjnm67mmK/lkKblt7Lnu4/mt7vliqDliLDor5XljbfkuK0KICAgICAgcmV0dXJuICF0aGlzLmZpeGVkUXVlc3Rpb25zLnNvbWUocXVlc3Rpb24gPT4gcXVlc3Rpb24ucXVlc3Rpb25JZCA9PT0gcm93LnF1ZXN0aW9uSWQpCiAgICB9LAoKICAgIC8qKiDojrflj5bpopjnm67ooYznmoTmoLflvI/nsbvlkI0gKi8KICAgIGdldFF1ZXN0aW9uUm93Q2xhc3NOYW1lKHsgcm93IH0pIHsKICAgICAgLy8g5aaC5p6c6aKY55uu5bey6KKr5re75Yqg77yM5re75Yqg56aB55So5qC35byPCiAgICAgIGlmICh0aGlzLmZpeGVkUXVlc3Rpb25zLnNvbWUocXVlc3Rpb24gPT4gcXVlc3Rpb24ucXVlc3Rpb25JZCA9PT0gcm93LnF1ZXN0aW9uSWQpKSB7CiAgICAgICAgcmV0dXJuICdkaXNhYmxlZC1xdWVzdGlvbi1yb3cnCiAgICAgIH0KICAgICAgcmV0dXJuICcnCiAgICB9LAogICAgCiAgICAvKiog5LiK5Lyg5YmN6aqM6K+BICovCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBpc0ltYWdlID0gZmlsZS50eXBlLmluZGV4T2YoJ2ltYWdlLycpID09PSAwCiAgICAgIGNvbnN0IGlzTHQyTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMgogICAgICAKICAgICAgaWYgKCFpc0ltYWdlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5Zu+54mH5paH5Lu2IScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgaWYgKCFpc0x0Mk0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDlm77niYflpKflsI/kuI3og73otoXov4cgMk1CIScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KICAgICAgcmV0dXJuIHRydWUKICAgIH0sCiAgICAKICAgIC8qKiDmoLzlvI/ljJbml6XmnJ8gKi8KICAgIGZvcm1hdERhdGUoZGF0ZSkgewogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJykKICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpCiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKQogICAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpCiAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGF0ZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJykKICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gCiAgICB9LAogICAgCiAgICAvKiog5Yqg6L296K+V5Y235pWw5o2uICovCiAgICBsb2FkUGFwZXJEYXRhKCkgewogICAgICBpZiAodGhpcy5wYXBlcklkKSB7CiAgICAgICAgLy8gVE9ETzog6LCD55SoQVBJ5Yqg6L296K+V5Y235pWw5o2uCiAgICAgICAgY29uc29sZS5sb2coJ+WKoOi9veivleWNt+aVsOaNrjonLCB0aGlzLnBhcGVySWQpCiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["create.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg3DA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "create.vue", "sourceRoot": "src/views/biz/paper", "sourcesContent": ["<template>\n  <div class=\"exam-editor\">\n    <!-- 左侧主要内容区域 -->\n    <div class=\"exam-editor-left\" :class=\"{ collapsed: rightPanelCollapsed }\">\n      <!-- 顶部操作栏 -->\n      <div class=\"editPaper_main_top\">\n        <div class=\"el-button-group\">\n          <el-button type=\"primary\" @click=\"handleBack\">\n            <i class=\"el-icon-back\"></i>\n            <span>返回试卷</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button @click=\"handleExamEntry\">\n            <i class=\"el-icon-share\"></i>\n            <span>考试入口</span>\n          </el-button>\n          <el-button @click=\"handlePageSetting\">\n            <i class=\"el-icon-picture-outline\"></i>\n            <span>设置考试页面</span>\n          </el-button>\n          <el-button type=\"warning\" @click=\"handlePublish\">\n            <i class=\"el-icon-upload\"></i>\n            <span>发布</span>\n          </el-button>\n        </div>\n        \n        <div class=\"el-button-group\">\n          <el-button type=\"success\" @click=\"handleInviteStudents\">\n            邀请考生\n          </el-button>\n          <el-button type=\"warning\" @click=\"handleInviteList\">\n            已邀请列表\n          </el-button>\n        </div>\n        \n        <div class=\"clear_both\"></div>\n      </div>\n\n      <!-- 试卷信息卡片 -->\n      <div class=\"subject_main-wrapper\">\n        <div class=\"subject_main\">\n          <el-card class=\"is-hover-shadow\" style=\"width: 100%;\">\n            <div class=\"subtitle-title\" style=\"padding: 10px;\">\n              <div>\n                <div style=\"float: left;\">\n                  <strong class=\"slgfont\">{{ paperForm.paperName || '新建试卷' }}</strong>\n                </div>\n                <div style=\"float: right; color: #ec661a; width: 130px; text-align: right;\">\n                  <span style=\"font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;\">{{ totalRuleScore }}</span>分\n                </div>\n                <div style=\"clear: both;\"></div>\n                <div class=\"paper-count\" style=\"font-size: 13px; color: #aaa;\">\n                  <span v-if=\"paperForm.createTime\">创建时间：{{ paperForm.createTime }}</span>\n                  <el-tag type=\"warning\" size=\"medium\" effect=\"light\">共 {{ totalQuestions }} 题</el-tag>\n                  <el-tag type=\"success\" size=\"medium\" effect=\"light\">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>\n                  <el-tag size=\"medium\" effect=\"light\">自动出分</el-tag>\n                </div>\n                <div class=\"rich-text\" style=\"display: none;\"></div>\n              </div>\n              <div style=\"clear: both;\"></div>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 题目设置区域 -->\n        <div>\n          <div class=\"mb10 mt10\">\n            <div class=\"el-button-group\"></div>\n          </div>\n          \n          <div v-if=\"paperForm.paperType === 1\">\n            <!-- 随机试卷 -->\n            <div class=\"random-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 有规则时显示规则内容 -->\n                <div v-if=\"rules.length > 0\" style=\"padding: 10px;\">\n                  <div class=\"tac pd5\">\n                    <div>\n                      <el-button type=\"primary\" @click=\"handleEditRules\">\n                        <i class=\"el-icon-edit\"></i>\n                        <span>编辑规则</span>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 规则显示区域 -->\n                  <div v-for=\"rule in parentRules\" :key=\"rule.id\" class=\"rule-display-container\">\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule-display\">\n                      <span class=\"rule-label\">{{ getRuleTypeLabel(rule) }}</span>\n                      <span class=\"rule-content\">{{ getRuleContent(rule) }}</span>\n                    </div>\n\n                    <!-- 子规则显示 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules-display\">\n                      <div v-for=\"childRule in rule.children\" :key=\"childRule.id\" class=\"child-rule-display\">\n                        <div class=\"child-rule-left\">\n                          <span class=\"rule-label\">{{ getRuleTypeLabel(childRule) }}</span>\n                          <span class=\"rule-content\">{{ getRuleContent(childRule) }}</span>\n                        </div>\n                        <div class=\"child-rule-right\">\n                          <span class=\"rule-stats\">\n                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，\n                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，\n                            总分 <strong>{{ childRule.totalScore }}</strong> 分\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 没有规则时显示添加按钮 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 10px;\">\n                  <div>\n                    <div class=\"mb10\">点击添加规则设置本试卷的抽题规则</div>\n                    <el-button type=\"primary\" @click=\"handleAddRule\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>添加规则</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n          \n          <div v-else>\n            <!-- 固定试卷 -->\n            <div class=\"fixed-paper\">\n              <el-card class=\"is-hover-shadow\">\n                <!-- 操作栏 -->\n                <div class=\"mb10 mt10\">\n                  <div class=\"el-button-group\">\n                    <label class=\"el-checkbox fl el-checkbox--medium is-bordered\" style=\"padding: 7.5px 20px;\">\n                      <span class=\"el-checkbox__input\">\n                        <span class=\"el-checkbox__inner\"></span>\n                        <input type=\"checkbox\" aria-hidden=\"false\" class=\"el-checkbox__original\" value=\"\" v-model=\"selectAll\" @change=\"handleSelectAll\">\n                      </span>\n                      <span class=\"el-checkbox__label\">全选</span>\n                    </label>\n                    <el-tooltip content=\"删除选中题目\" placement=\"top\">\n                      <el-button type=\"danger\" size=\"medium\" @click=\"handleDeleteSelected\">\n                        <i class=\"el-icon-delete\"></i>\n                      </el-button>\n                    </el-tooltip>\n                  </div>\n\n                  <div class=\"el-button-group\">\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>手动选题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleRandomSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>随机抽题</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleSort\">\n                      <i class=\"el-icon-sort\"></i>\n                      <span>排序</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleBatchSetScore\">\n                      <i class=\"icon_size iconfont icon-batch-add\"></i>\n                      <span>批量设置分数</span>\n                    </el-button>\n                    <el-button type=\"default\" size=\"medium\" @click=\"handleExport\">\n                      <i class=\"icon_size iconfont icon-daochu\"></i>\n                      <span>导出</span>\n                    </el-button>\n                  </div>\n\n                  <el-button type=\"default\" size=\"medium\" style=\"vertical-align: middle;\" @click=\"handleToggleExpand\">\n                    <span>\n                      <i :class=\"isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\" style=\"margin-right: 5px;\"></i>\n                      <span>{{ isExpanded ? '收起' : '展开' }}</span>\n                    </span>\n                  </el-button>\n                </div>\n\n                <!-- 题目列表区域 -->\n                <div v-if=\"fixedQuestions.length > 0\" class=\"question-list\">\n                  <!-- 这里将显示已添加的题目列表 -->\n                  <div class=\"question-item\" v-for=\"(question, index) in fixedQuestions\" :key=\"question.id\">\n                    <!-- 题目头部 -->\n                    <div class=\"question-header\">\n                      <div class=\"question-header-left\">\n                        <el-checkbox v-model=\"question.selected\"></el-checkbox>\n                        <span class=\"question-number\">{{ index + 1 }}.</span>\n                        <i\n                          :class=\"question.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\"\n                          class=\"question-expand-icon\"\n                          @click=\"toggleQuestionExpand(question)\"\n                        ></i>\n                        <span class=\"question-content\" @click=\"toggleQuestionExpand(question)\">{{ question.content }}</span>\n                      </div>\n                      <div class=\"question-header-right\">\n                        <!-- 分数设置 -->\n                        <el-input-number\n                          v-model=\"question.score\"\n                          :min=\"0.5\"\n                          :step=\"0.5\"\n                          size=\"mini\"\n                          style=\"width: 100px;\"\n                          @change=\"updateQuestionScore(question)\"\n                        ></el-input-number>\n                        <span style=\"margin-left: 5px; margin-right: 10px;\">分</span>\n\n                        <!-- 展开/收起按钮 -->\n                        <el-tooltip :content=\"question.expanded ? '收起' : '展开'\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"toggleQuestionExpand(question)\"\n                            style=\"margin-right: 5px;\"\n                          >\n                            <i :class=\"question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                          </el-button>\n                        </el-tooltip>\n\n                        <!-- 删除按钮 -->\n                        <el-tooltip content=\"删除\" placement=\"top\">\n                          <el-button\n                            type=\"text\"\n                            size=\"mini\"\n                            @click=\"deleteQuestion(question, index)\"\n                            style=\"color: #f56c6c;\"\n                          >\n                            <i class=\"el-icon-delete\"></i>\n                          </el-button>\n                        </el-tooltip>\n                      </div>\n                    </div>\n\n                    <!-- 题目详情（展开时显示） -->\n                    <div v-if=\"question.expanded\" class=\"question-details\">\n                      <div class=\"question-info\">\n                        <span class=\"info-item\">题型：{{ getQuestionTypeText(question.type) }}</span>\n                        <span class=\"info-item\">难度：{{ getDifficultyText(question.difficulty) }}</span>\n                      </div>\n\n                      <!-- 选项显示 -->\n                      <div v-if=\"question.options\" class=\"question-options\">\n                        <div class=\"options-title\">选项：</div>\n                        <div\n                          v-for=\"(option, optIndex) in parseOptions(question.options)\"\n                          :key=\"optIndex\"\n                          class=\"option-item\"\n                          :class=\"{ 'correct-option': option.isCorrect }\"\n                        >\n                          <span class=\"option-key\">{{ option.key }}.</span>\n                          <span class=\"option-content\">{{ option.content }}</span>\n                          <span v-if=\"option.isCorrect\" class=\"correct-mark\">✓</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-else class=\"tac pd5\" style=\"padding: 40px 10px;\">\n                  <div>\n                    <div class=\"mb10\">暂无题目，点击上方按钮添加题目到试卷中</div>\n                    <el-button type=\"primary\" @click=\"handleManualSelect\">\n                      <i class=\"el-icon-plus\"></i>\n                      <span>开始添加题目</span>\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 右侧设置面板 -->\n    <div class=\"exam-editor-right\" :style=\"{ width: rightPanelWidth + 'px' }\">\n      <i \n        :class=\"rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'\" \n        class=\"collapse-button\" \n        title=\"收起/展开\"\n        @click=\"toggleRightPanel\"\n      ></i>\n      \n      <div v-show=\"!rightPanelCollapsed\">\n        <div class=\"editor-header editor-header--big\">\n          <span>考试设置</span>\n          <i class=\"el-icon-close close-button\" @click=\"handleBack\" title=\"关闭\"></i>\n        </div>\n        \n        <div class=\"main\">\n          <el-form class=\"h100p\">\n            <el-collapse v-model=\"activeCollapse\" class=\"main-collapse h100p\" accordion>\n              <!-- 基础设置 -->\n              <el-collapse-item title=\"基础设置\" name=\"basic\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-setting\" style=\"color: #67c23a;\"></i>基础设置\n                  </div>\n                </template>\n\n                <div class=\"main-module\">\n                  <!-- 封面图 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>封面图</b>\n                      <div class=\"input-area\">\n                        <div style=\"margin-top: 20px;\">\n                          <span class=\"dpib\" style=\"width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;\">\n                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2\n                          </span>\n                          <div class=\"g-component-cover-uploader dpib\">\n                            <div class=\"avatar-uploader\" style=\"width: 120px; height: 80px;\">\n                              <el-upload\n                                class=\"avatar-uploader\"\n                                action=\"#\"\n                                :show-file-list=\"false\"\n                                :before-upload=\"beforeUpload\"\n                                accept=\".png, .jpeg, .jpg\"\n                              >\n                                <div class=\"image_area\">\n                                  <img v-if=\"paperForm.coverImg\" :src=\"paperForm.coverImg\" class=\"avatar\">\n                                  <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n                                </div>\n                              </el-upload>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷名称 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷名称</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperName\"\n                          placeholder=\"请输入试卷名称\"\n                          maxlength=\"100\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 试卷描述 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>试卷描述</b>\n                      <div class=\"input-area\">\n                        <el-input\n                          v-model=\"paperForm.paperDesc\"\n                          type=\"textarea\"\n                          :rows=\"3\"\n                          placeholder=\"请输入试卷描述\"\n                          maxlength=\"500\"\n                          show-word-limit\n                        ></el-input>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 出题方式 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>出题方式</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"paper-type-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">出题方式</div>\n                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>\n                            <br>\n                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-radio-group v-model=\"paperForm.paperType\" size=\"mini\">\n                          <el-radio-button :label=\"1\">随机试卷</el-radio-button>\n                          <el-radio-button :label=\"0\">固定试卷</el-radio-button>\n                        </el-radio-group>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n                  <!-- 时长 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>时长(按卷限时)</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">时长</div>\n                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>\n                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.duration\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                        <el-input-number v-model=\"paperForm.durationSeconds\" :min=\"0\" :max=\"59\" size=\"mini\" style=\"width: 85px;\"></el-input-number> 秒\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 及格分 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>及格分</b>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.passScore\" :min=\"0\" :max=\"paperForm.totalScore || 100\" size=\"mini\" class=\"input--number\"></el-input-number> 分\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>考试时间</b>\n                      <el-popover placement=\"top\" width=\"240\" trigger=\"hover\">\n                        <div slot=\"content\">\n                          <div class=\"el-popover__title\">考试时间</div>\n                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试\n                        </div>\n                      </el-popover>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.enableTimeLimit\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 考试时间子项容器 -->\n                  <div class=\"block-expand\" v-if=\"paperForm.enableTimeLimit\">\n                    <!-- 考试开始时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试开始时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.startTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择开始时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 考试结束时间 -->\n                    <div class=\"setting-block sub-setting\">\n                      <div class=\"line block-header\">\n                        <b>考试结束时间</b>\n                        <div class=\"input-area\">\n                          <el-date-picker\n                            v-model=\"paperForm.endTime\"\n                            type=\"datetime\"\n                            placeholder=\"选择结束时间\"\n                            format=\"yyyy-MM-dd HH:mm:ss\"\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\n                            size=\"mini\"\n                          ></el-date-picker>\n                        </div>\n                      </div>\n                    </div>\n\n\n                  </div>\n\n                  <!-- 提前交卷 -->\n                  <div class=\"setting-block\" v-if=\"paperForm.enableTimeLimit\">\n                    <div class=\"line block-header\">\n                      <b>提前交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch\n                          v-model=\"paperForm.allowEarlySubmit\"\n                          :active-value=\"1\"\n                          :inactive-value=\"0\"\n                        ></el-switch>\n                        <span v-if=\"paperForm.allowEarlySubmit\" style=\"margin-left: 10px;\">\n                          <el-input-number\n                            v-model=\"paperForm.earlySubmitTime\"\n                            :min=\"1\"\n                            :max=\"60\"\n                            size=\"mini\"\n                            style=\"width: 100px;\"\n                          ></el-input-number> 分钟\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示分值 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示分值</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showScore\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 是否显示题型 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示题型</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showType\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 迟到限制 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <div class=\"setting-title\">\n                        <b>迟到限制</b>\n                        <el-tooltip\n                          placement=\"top\"\n                          effect=\"light\"\n                          popper-class=\"late-limit-tooltip\"\n                        >\n                          <div slot=\"content\">\n                            <div style=\"font-weight: bold; margin-bottom: 8px;\">迟到限制</div>\n                            <div>设置迟到多少分钟后，禁止再进入考试。</div>\n                          </div>\n                          <i class=\"el-icon-question paper-type-question\"></i>\n                        </el-tooltip>\n                      </div>\n                      <div class=\"input-area\">\n                        <el-input-number v-model=\"paperForm.lateLimit\" :min=\"0\" size=\"mini\" class=\"input--number\"></el-input-number> 分钟\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试中设置 -->\n              <el-collapse-item title=\"考试中\" name=\"during\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-edit-outline\" style=\"color: #409eff;\"></i>考试中\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n\n\n                  <!-- 全部答完才能交卷 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>全部答完才能交卷</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.requireAllAnswered\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n\n\n\n                </div>\n              </el-collapse-item>\n\n              <!-- 考试后设置 -->\n              <el-collapse-item title=\"考试后\" name=\"after\" class=\"editor-collapse-item\">\n                <template slot=\"title\">\n                  <div class=\"bold collapse-title\">\n                    <i class=\"el-icon-finished\" style=\"color: #e6a23c;\"></i>考试后\n                  </div>\n                </template>\n                \n                <div class=\"main-module\">\n                  <!-- 显示成绩 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示成绩</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showResult\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示对错 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示对错</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showCorrect\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示答案 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示答案</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnswer\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 显示解析 -->\n                  <div class=\"setting-block\">\n                    <div class=\"line block-header\">\n                      <b>显示解析</b>\n                      <div class=\"input-area\">\n                        <el-switch v-model=\"paperForm.showAnalysis\" :active-value=\"1\" :inactive-value=\"0\"></el-switch>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </el-form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 随机规则设置对话框 -->\n    <el-dialog\n      title=\"随机规则设置\"\n      :visible.sync=\"showRuleDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"新版规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFirstRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加一级规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ totalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ totalRuleScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"确定一级规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      按题库、按题型、按难度、按知识点抽题四选一\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"添加子规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      在每个上级规则基础上继续添加子规则\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"保存抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      各级规则设置完成后即可保存规则开始抽题\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"rules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in parentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}\n                        </div>\n                        <div class=\"rule-content\">\n                          <span v-if=\"rule.type === 3\">\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                          <span v-else-if=\"rule.type === 1\">\n                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"addSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}\n                          </div>\n                          <div class=\"rule-content\" v-if=\"childRule.type === 3\">\n                            <span>\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"rules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加一级规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showRuleDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleSaveRules\">保存规则</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 手动选题对话框 -->\n    <el-dialog\n      title=\"选择题目\"\n      :visible.sync=\"showManualSelectDialog\"\n      width=\"1200px\"\n      :close-on-click-modal=\"false\"\n      class=\"checked-question\"\n      top=\"15vh\"\n      :append-to-body=\"true\"\n      :z-index=\"3200\"\n    >\n      <div style=\"display: flex; height: 550px;\">\n        <!-- 左侧：题库列表 -->\n        <div style=\"width: 300px; padding-right: 10px;\">\n          <!-- 搜索区域 -->\n          <div style=\"padding: 5px; height: 42px; display: flex; gap: 10px;\">\n            <el-cascader\n              v-model=\"manualSelect.selectedCategory\"\n              :options=\"categoryOptions\"\n              :props=\"cascaderProps\"\n              placeholder=\"选择题库分类\"\n              size=\"small\"\n              style=\"width: 135px;\"\n              clearable\n              @change=\"searchQuestionBanks\"\n            ></el-cascader>\n\n            <el-input\n              v-model=\"manualSelect.bankSearchKeyword\"\n              placeholder=\"题库名称\"\n              size=\"small\"\n              style=\"width: 150px;\"\n            >\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchQuestionBanks\"></el-button>\n            </el-input>\n          </div>\n\n          <!-- 题库表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questionBanks\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @row-click=\"selectQuestionBank\"\n              highlight-current-row\n            >\n              <el-table-column type=\"index\" width=\"38\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"left\" header-align=\"center\"></el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding-top: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleBankSizeChange\"\n              @current-change=\"handleBankCurrentChange\"\n              :current-page=\"manualSelect.bankPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 50]\"\n              :page-size=\"manualSelect.bankPagination.pageSize\"\n              :total=\"manualSelect.bankPagination.total\"\n              layout=\"prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 中间：题目列表 -->\n        <div style=\"width: 700px; padding: 0px 5px;\">\n          <!-- 筛选区域 -->\n          <div style=\"padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;\">\n            <el-select\n              v-model=\"manualSelect.questionType\"\n              placeholder=\"题型\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部题型\" value=\"\"></el-option>\n              <el-option label=\"单选题\" value=\"1\"></el-option>\n              <el-option label=\"多选题\" value=\"2\"></el-option>\n              <el-option label=\"判断题\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-select\n              v-model=\"manualSelect.difficulty\"\n              placeholder=\"难度\"\n              size=\"small\"\n              style=\"width: 110px;\"\n              @change=\"searchQuestions\"\n            >\n              <el-option label=\"全部难度\" value=\"\"></el-option>\n              <el-option label=\"低\" value=\"1\"></el-option>\n              <el-option label=\"中\" value=\"2\"></el-option>\n              <el-option label=\"高\" value=\"3\"></el-option>\n            </el-select>\n\n            <el-input\n              v-model=\"manualSelect.questionSearchKeyword\"\n              placeholder=\"搜索题目\"\n              size=\"small\"\n              style=\"width: 250px;\"\n            >\n              <template slot=\"append\">\n                <el-button @click=\"searchQuestions\" style=\"border-left: 1px solid #dcdfe6;\">搜索</el-button>\n                <el-button @click=\"resetQuestionSearch\" style=\"border-left: 1px solid #dcdfe6;\">重置</el-button>\n              </template>\n            </el-input>\n          </div>\n\n          <!-- 题目表格 -->\n          <div style=\"padding: 0px 5px; height: 485px; overflow: auto;\">\n            <el-table\n              :data=\"manualSelect.questions\"\n              border\n              size=\"medium\"\n              height=\"485\"\n              @selection-change=\"handleQuestionSelectionChange\"\n              :row-class-name=\"getQuestionRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"40\" :selectable=\"isQuestionSelectable\"></el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题干\" min-width=\"400\" header-align=\"center\" class-name=\"question-content-column\">\n                <template slot-scope=\"scope\">\n                  <div class=\"question-content-text\">\n                    {{ scope.row.questionContent }}\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionType\" label=\"题目类型\" width=\"78\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getQuestionTypeText(scope.row.questionType) }}\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"50\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getDifficultyText(scope.row.difficulty) }}\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n\n          <!-- 分页 -->\n          <div style=\"padding: 10px; text-align: right;\">\n            <el-pagination\n              @size-change=\"handleQuestionSizeChange\"\n              @current-change=\"handleQuestionCurrentChange\"\n              :current-page=\"manualSelect.questionPagination.pageNum\"\n              :page-sizes=\"[10, 20, 30, 40, 50, 100]\"\n              :page-size=\"manualSelect.questionPagination.pageSize\"\n              :total=\"manualSelect.questionPagination.total\"\n              layout=\"sizes, prev, pager, next\"\n              background\n              small\n            ></el-pagination>\n          </div>\n        </div>\n\n        <!-- 右侧：统计信息 -->\n        <div style=\"width: 150px; padding: 0px 5px;\">\n          <div style=\"padding: 7px 0px; height: 42px; font-size: 16px;\">试卷题型统计</div>\n          <div style=\"border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;\">\n            <!-- 显示已添加到试卷的题目统计 -->\n            <div v-for=\"(count, type) in fixedQuestionStats\" :key=\"type\" style=\"margin-bottom: 10px;\">\n              <div style=\"font-size: 14px; color: #606266;\">\n                {{ getQuestionTypeText(type) }}：{{ count }} 题\n              </div>\n            </div>\n\n            <!-- 显示当前选择的题目统计 -->\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;\">\n              <div style=\"font-size: 13px; color: #909399; margin-bottom: 8px;\">本次选择：</div>\n              <div v-for=\"(count, type) in manualSelect.selectedStats\" :key=\"'selected-' + type\" style=\"margin-bottom: 8px;\">\n                <div style=\"font-size: 13px; color: #409eff;\">\n                  {{ getQuestionTypeText(type) }}：{{ count }} 题\n                </div>\n              </div>\n            </div>\n          </div>\n          <div style=\"width: 140px; text-align: right; padding-right: 5px;\">\n            <div style=\"font-size: 14px; font-weight: bold;\">总题数：{{ fixedQuestions.length }} 题</div>\n            <div v-if=\"manualSelect.selectedQuestions.length > 0\" style=\"font-size: 12px; color: #409eff;\">\n              +{{ manualSelect.selectedQuestions.length }} 题\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showManualSelectDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"confirmManualSelect\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加规则对话框 -->\n    <el-dialog\n      :title=\"getDialogTitle()\"\n      :visible.sync=\"showAddRuleDialog\"\n      width=\"900px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3100\"\n    >\n      <div style=\"margin-bottom: -30px;\">\n        <el-form :model=\"ruleForm\" label-width=\"140px\">\n          <!-- 选择规则类型 -->\n          <el-form-item v-if=\"shouldShowRuleTypeSelection()\" label=\"选择规则类型\">\n            <el-radio-group v-model=\"ruleForm.ruleType\">\n              <el-radio\n                v-if=\"shouldShowRuleType(3)\"\n                :label=\"3\"\n                :disabled=\"shouldDisableRuleType(3)\"\n              >题库</el-radio>\n              <el-radio\n                v-if=\"shouldShowRuleType(1)\"\n                :label=\"1\"\n                :disabled=\"shouldDisableRuleType(1)\"\n              >题型</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 规则生成方式 -->\n          <el-form-item v-if=\"currentOperation !== 'addSub'\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                规则生成方式\n              </div>\n            </template>\n            <el-radio-group v-model=\"ruleForm.generateType\">\n              <el-radio label=\"divided\">分开设置</el-radio>\n              <el-radio label=\"one\">整体设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n\n          <!-- 已选择的题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\">\n            <template slot=\"label\">\n              <div>\n                <el-tooltip content=\"点击选项可取消选择。\" placement=\"top\">\n                  <i class=\"el-icon-info\" style=\"cursor: pointer;\"></i>\n                </el-tooltip>\n                已选择的题库\n              </div>\n            </template>\n            <span v-if=\"ruleForm.selectedItems.length === 0\" class=\"ml20\">\n              暂无选择，请至少选择一项。\n            </span>\n            <div v-else>\n              <el-tag\n                v-for=\"item in selectedBanks\"\n                :key=\"item.bankId\"\n                closable\n                @close=\"removeSelectedBank(item.bankId)\"\n                type=\"info\"\n                size=\"medium\"\n                class=\"selected-bank-tag\"\n              >\n                {{ item.bankName }}\n              </el-tag>\n            </div>\n          </el-form-item>\n\n          <!-- 选择题型 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 1\" label=\"选择题型\">\n            <el-checkbox-group v-model=\"ruleForm.selectedQuestionTypes\">\n              <el-checkbox v-if=\"shouldShowQuestionType('1')\" label=\"1\">单选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('2')\" label=\"2\">多选题</el-checkbox>\n              <el-checkbox v-if=\"shouldShowQuestionType('3')\" label=\"3\">判断题</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n\n\n\n          <!-- 选择题库 -->\n          <el-form-item v-if=\"ruleForm.ruleType === 3\" label=\"选择题库\">\n            <!-- 搜索区域 -->\n            <div class=\"clearfix\" style=\"margin-bottom: 6px;\">\n              <div style=\"float: right; display: flex; align-items: center;\">\n                <el-input\n                  v-model=\"searchKeyword\"\n                  placeholder=\"题库名称\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 8px;\"\n                  @keyup.enter.native=\"handleSearch\"\n                  clearable\n                >\n                  <el-button slot=\"append\" @click=\"handleSearch\">搜索</el-button>\n                </el-input>\n                <el-button size=\"small\" @click=\"handleReset\">重置</el-button>\n              </div>\n            </div>\n\n            <!-- 题库表格 -->\n            <el-table\n              ref=\"questionBankTable\"\n              :data=\"questionBanks\"\n              border\n              size=\"small\"\n              max-height=\"300\"\n              v-loading=\"questionBankLoading\"\n              @selection-change=\"handleSelectionChange\"\n              :row-class-name=\"getRowClassName\"\n            >\n              <el-table-column type=\"index\" width=\"40\" label=\"#\" align=\"center\"></el-table-column>\n              <el-table-column type=\"selection\" width=\"50\" align=\"center\" :selectable=\"isRowSelectable\"></el-table-column>\n              <el-table-column prop=\"bankName\" label=\"题库名称\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"questionCount\" label=\"题目数量\" width=\"80\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.questionCount || 0 }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"分类\" width=\"150\" align=\"center\">\n                <template slot-scope=\"scope\">\n                  {{ getCategoryName(scope.row.categoryId) }}\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <!-- 分页 -->\n            <div style=\"text-align: right; margin-top: 10px;\">\n              <el-pagination\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"queryParams.pageNum\"\n                :page-size=\"queryParams.pageSize\"\n                layout=\"total, prev, pager, next\"\n                :total=\"total\"\n                small\n                background\n              >\n              </el-pagination>\n            </div>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showAddRuleDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleSaveRule\">保 存</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 固定试卷随机抽题对话框 -->\n    <el-dialog\n      title=\"随机抽题规则设置\"\n      :visible.sync=\"showFixedRandomDialog\"\n      width=\"1100px\"\n      top=\"15vh\"\n      :close-on-click-modal=\"false\"\n      :modal-append-to-body=\"false\"\n      :append-to-body=\"true\"\n      :z-index=\"3000\"\n    >\n      <div class=\"pd10\">\n        <el-tabs value=\"newRule\" type=\"border-card\">\n          <el-tab-pane label=\"随机抽题规则\" name=\"newRule\">\n            <div class=\"cascade-random-rules\">\n              <!-- 顶部操作栏 -->\n              <div class=\"topbar clearfix\">\n                <div class=\"fl\">\n                  <el-button type=\"success\" @click=\"handleAddFixedRandomRule\">\n                    <i class=\"el-icon-plus\"></i>\n                    添加抽题规则\n                  </el-button>\n                  <span class=\"summary\">\n                    共选择 <span class=\"total_score\">{{ fixedRandomTotalQuestions }}</span> 道题目，\n                    总分 <span class=\"total_score\">{{ fixedRandomTotalScore }}</span> 分\n                  </span>\n                </div>\n              </div>\n\n              <!-- 步骤指引 -->\n              <el-steps :active=\"0\" class=\"guide-steps-list\" align-center>\n                <el-step title=\"设置抽题规则\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      选择题库和题型，设置抽题数量和分数\n                    </div>\n                  </template>\n                </el-step>\n                <el-step title=\"确认抽题\">\n                  <template slot=\"description\">\n                    <div class=\"step-content\">\n                      根据规则随机抽取题目并添加到试卷\n                    </div>\n                  </template>\n                </el-step>\n              </el-steps>\n\n              <!-- 规则列表区域 -->\n              <div class=\"rules-content\">\n                <!-- 已添加的规则列表 -->\n                <div v-if=\"fixedRandomParentRules.length > 0\" class=\"rules-list\">\n                  <div\n                    v-for=\"rule in fixedRandomParentRules\"\n                    :key=\"rule.id\"\n                    class=\"rule-node-cascade\"\n                  >\n                    <!-- 父规则显示 -->\n                    <div class=\"parent-rule\">\n                      <!-- 左侧：规则信息 -->\n                      <div class=\"rule-left\">\n                        <div class=\"rule-type-label\">\n                          题库：\n                        </div>\n                        <div class=\"rule-content\">\n                          <span>\n                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                          </span>\n                        </div>\n                      </div>\n\n                      <!-- 右侧：操作区域 -->\n                      <div class=\"rule-right\">\n                        <!-- 父规则没有子规则时显示完整控件 -->\n                        <div v-if=\"!rule.children || rule.children.length === 0\" class=\"rule-controls\">\n                          <span class=\"control-item\">\n                            选取\n                            <el-input-number\n                              v-model=\"rule.selectedCount\"\n                              :min=\"1\"\n                              :max=\"rule.maxQuestions\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                          </span>\n                          <span class=\"control-divider\">/</span>\n                          <span class=\"control-item\">{{ rule.maxQuestions }} 题</span>\n                          <span class=\"control-item\">\n                            每题\n                            <el-input-number\n                              v-model=\"rule.scorePerQuestion\"\n                              :min=\"0.5\"\n                              :step=\"0.5\"\n                              size=\"mini\"\n                              class=\"input-number-mini\"\n                              @change=\"updateFixedRandomRuleScore(rule)\"\n                            ></el-input-number>\n                            分\n                          </span>\n                          <span class=\"control-item total-score\">总分 {{ rule.totalScore }} 分</span>\n                        </div>\n\n                        <!-- 操作按钮（始终显示） -->\n                        <div class=\"rule-actions\">\n                          <el-tooltip content=\"添加子规则\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"handleAddFixedRandomSubRule(rule)\">\n                              <i class=\"el-icon-plus\"></i>\n                            </el-button>\n                          </el-tooltip>\n                          <el-tooltip content=\"删除\" placement=\"top\">\n                            <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomRule(rule)\">\n                              <i class=\"el-icon-delete\"></i>\n                            </el-button>\n                          </el-tooltip>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- 子规则列表 -->\n                    <div v-if=\"rule.children && rule.children.length > 0\" class=\"children-rules\">\n                      <div\n                        v-for=\"childRule in rule.children\"\n                        :key=\"childRule.id\"\n                        class=\"rule-node-cascade children_component\"\n                      >\n                        <!-- 左侧：子规则信息 -->\n                        <div class=\"rule-left\">\n                          <div class=\"rule-type-label\">\n                            {{ getChildRuleLabel(childRule) }}：\n                          </div>\n                          <div class=\"rule-content\">\n                            <span v-if=\"childRule.type === 3\">\n                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}\n                            </span>\n                          </div>\n                        </div>\n\n                        <!-- 右侧：子规则操作区域 -->\n                        <div class=\"rule-right\">\n                          <div class=\"rule-controls\">\n                            <span class=\"control-item\">\n                              选取\n                              <el-input-number\n                                v-model=\"childRule.selectedCount\"\n                                :min=\"1\"\n                                :max=\"childRule.maxQuestions\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                            </span>\n                            <span class=\"control-divider\">/</span>\n                            <span class=\"control-item\">{{ childRule.maxQuestions }} 题</span>\n                            <span class=\"control-item\">\n                              每题\n                              <el-input-number\n                                v-model=\"childRule.scorePerQuestion\"\n                                :min=\"0.5\"\n                                :step=\"0.5\"\n                                size=\"mini\"\n                                class=\"input-number-mini\"\n                                @change=\"updateFixedRandomRuleScore(childRule)\"\n                              ></el-input-number>\n                              分\n                            </span>\n                            <span class=\"control-item total-score\">总分 {{ childRule.totalScore }} 分</span>\n                          </div>\n\n                          <div class=\"rule-actions\">\n                            <el-tooltip content=\"删除\" placement=\"top\">\n                              <el-button type=\"text\" size=\"mini\" class=\"action-btn\" @click=\"deleteFixedRandomChildRule(rule, childRule)\">\n                                <i class=\"el-icon-delete\"></i>\n                              </el-button>\n                            </el-tooltip>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 空状态 -->\n                <div v-if=\"fixedRandomRules.length === 0\" class=\"empty-rules\">\n                  <p>暂无规则，请点击左上角\"添加抽题规则\"开始设置</p>\n                </div>\n              </div>\n\n              <!-- 底部按钮 -->\n              <div class=\"bottom-panel\">\n                <el-button @click=\"showFixedRandomDialog = false\">取 消</el-button>\n                <el-button type=\"primary\" @click=\"handleConfirmFixedRandomSelect\">确认抽题</el-button>\n              </div>\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n    </el-dialog>\n\n    <!-- 批量设置分数对话框 -->\n    <el-dialog\n      title=\"批量设置分数\"\n      :visible.sync=\"showBatchScoreDialog\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :append-to-body=\"true\"\n    >\n      <el-form :model=\"batchScoreForm\" label-width=\"120px\">\n        <el-form-item label=\"设置范围\">\n          <el-radio-group v-model=\"batchScoreForm.scoreType\">\n            <el-radio label=\"all\">全部题目</el-radio>\n            <el-radio label=\"selected\">选中题目</el-radio>\n            <el-radio label=\"byType\">按题型</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <el-form-item v-if=\"batchScoreForm.scoreType === 'byType'\" label=\"选择题型\">\n          <el-select v-model=\"batchScoreForm.questionType\" placeholder=\"请选择题型\">\n            <el-option label=\"单选题\" value=\"1\"></el-option>\n            <el-option label=\"多选题\" value=\"2\"></el-option>\n            <el-option label=\"判断题\" value=\"3\"></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"分数\">\n          <el-input-number\n            v-model=\"batchScoreForm.score\"\n            :min=\"0.5\"\n            :step=\"0.5\"\n            :precision=\"1\"\n            style=\"width: 150px;\"\n          ></el-input-number>\n          <span style=\"margin-left: 10px;\">分</span>\n        </el-form-item>\n\n        <el-form-item label=\"影响题目\">\n          <div style=\"color: #909399; font-size: 13px;\">\n            {{ getBatchScoreAffectedCount() }} 道题目将被设置为 {{ batchScoreForm.score }} 分\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"showBatchScoreDialog = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"handleConfirmBatchSetScore\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n/* 表格垂直对齐优化 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {\n  vertical-align: middle;\n}\n\n/* 多选框垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n\n/* 序号列垂直居中 */\n::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 32px;\n}\n\n/* 规则节点样式 */\n.rule-node-cascade {\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  padding: 16px;\n  margin-bottom: 12px;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.rule-node-cascade:hover {\n  border: 1px dashed #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n}\n\n/* 左侧题库信息 */\n.rule-left {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.rule-type-label {\n  font-weight: 600;\n  color: #333;\n  font-size: 14px;\n  min-width: 60px;\n}\n\n.rule-content {\n  font-size: 14px;\n  color: #666;\n}\n\n/* 右侧操作区域 */\n.rule-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rule-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  color: #666;\n}\n\n.control-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  white-space: nowrap;\n}\n\n.control-divider {\n  color: #999;\n  margin: 0 4px;\n}\n\n.total-score {\n  font-weight: 600;\n  color: #333;\n}\n\n.input-number-mini {\n  width: 110px !important;\n}\n\n.rule-actions {\n  display: flex;\n  gap: 2px;\n}\n\n.action-btn {\n  color: #409eff !important;\n  padding: 4px 6px !important;\n  margin-left: 0 !important;\n}\n\n.action-btn:hover {\n  background-color: #ecf5ff !important;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 40px 0;\n  color: #999;\n}\n\n/* 子规则样式 */\n.children-rules {\n  margin-top: 12px;\n  margin-left: 20px;\n  border-left: 2px dashed #e8e8e8;\n  padding-left: 20px;\n}\n\n.children_component {\n  margin-bottom: 8px !important;\n  border: 1px dashed #d9d9d9 !important;\n  background-color: #f9f9f9 !important;\n}\n\n.children_component:hover {\n  border: 1px dashed #409eff !important;\n  background-color: #ecf5ff !important;\n}\n\n.parent-rule {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n/* 禁用的表格行样式 */\n::v-deep .disabled-row {\n  background-color: #f5f5f5 !important;\n  color: #c0c4cc !important;\n}\n\n::v-deep .disabled-row:hover {\n  background-color: #f5f5f5 !important;\n}\n\n::v-deep .disabled-row td {\n  color: #c0c4cc !important;\n}\n\n/* random-paper 规则显示样式 */\n.rule-display-container {\n  border: 1px dashed #d0d0d0;\n  border-radius: 6px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background-color: #fafbfc;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n/* 题库容器悬停效果 */\n.rule-display-container:hover {\n  border-color: #409eff;\n  background-color: #f8fbff;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.rule-display-container:hover .parent-rule-display {\n  background-color: #e3f2fd;\n  border-left-color: #1976d2;\n}\n\n.rule-display-container:hover .parent-rule-display .rule-label {\n  color: #1976d2;\n}\n\n.parent-rule-display {\n  font-size: 14px;\n  color: #303133;\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n  border-left: 4px solid #409eff;\n}\n\n.children-rules-display {\n  margin-left: 20px;\n}\n\n.child-rule-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 12px;\n  margin-bottom: 8px;\n  background-color: #ffffff;\n  border: 1px dashed #d0d0d0;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.child-rule-display:last-child {\n  margin-bottom: 0;\n}\n\n/* 子规则悬停效果 */\n.child-rule-display:hover {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.child-rule-display:hover .rule-label {\n  color: #1976d2;\n}\n\n.child-rule-display:hover .rule-stats {\n  background-color: #e3f2fd;\n  border-color: #90caf9;\n}\n\n.child-rule-left {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.child-rule-right {\n  flex-shrink: 0;\n  margin-left: 20px;\n}\n\n.rule-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.rule-content {\n  color: #303133;\n}\n\n.rule-stats {\n  font-size: 13px;\n  color: #606266;\n  background-color: #f0f9ff;\n  padding: 4px 8px;\n  border-radius: 5px;\n  border: 1px solid #b3d8ff;\n  white-space: nowrap;\n}\n\n.rule-stats strong {\n  color: #409eff;\n  font-weight: 600;\n}\n\n/* 固定试卷样式 */\n.question-list {\n  margin-top: 20px;\n}\n\n.question-item {\n  margin-bottom: 12px;\n  background-color: #fafafa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.question-item:hover {\n  background-color: #f0f9ff;\n  border-color: #409eff;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 15px;\n}\n\n.question-header-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.question-header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.question-number {\n  font-weight: bold;\n  color: #409eff;\n  margin-left: 10px;\n  margin-right: 10px;\n  min-width: 30px;\n}\n\n.question-content {\n  flex: 1;\n  color: #303133;\n  font-size: 14px;\n  line-height: 1.5;\n  margin-right: 15px;\n  cursor: pointer;\n}\n\n.question-expand-icon {\n  margin-right: 8px;\n  margin-left: 8px;\n  color: #909399;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n  font-size: 12px;\n}\n\n.question-expand-icon:hover {\n  color: #409eff;\n}\n\n.question-details {\n  border-top: 1px solid #e4e7ed;\n  padding: 15px;\n  background-color: #ffffff;\n}\n\n.question-info {\n  margin-bottom: 15px;\n}\n\n.info-item {\n  display: inline-block;\n  margin-right: 20px;\n  font-size: 13px;\n  color: #606266;\n}\n\n.question-options {\n  margin-top: 10px;\n}\n\n.options-title {\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 6px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.option-item.correct-option {\n  color: #67c23a;\n  font-weight: 500;\n}\n\n.option-key {\n  font-weight: bold;\n  margin-right: 8px;\n  min-width: 20px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  margin-left: 8px;\n}\n\n.el-button-group {\n  display: inline-block;\n  margin-right: 10px;\n}\n\n.icon_size {\n  font-size: 14px;\n}\n\n/* 禁用题目行样式 */\n.disabled-question-row {\n  background-color: #f5f7fa !important;\n  color: #c0c4cc !important;\n}\n\n.disabled-question-row:hover {\n  background-color: #f5f7fa !important;\n}\n\n.disabled-question-row td {\n  color: #c0c4cc !important;\n}\n\n/* 题干内容左对齐样式 */\n.question-content-text {\n  max-height: 60px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: left !important;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n/* 强制题干列内容左对齐 */\n.el-table .question-content-column {\n  text-align: left !important;\n}\n\n.el-table .question-content-column .cell {\n  text-align: left !important;\n  padding-left: 10px !important;\n  padding-right: 10px !important;\n}\n\n/* 已选择题库标签样式 */\n.selected-bank-tag {\n  margin-right: 8px !important;\n  margin-bottom: 4px !important;\n  font-size: 13px !important;\n}\n\n/* 修复标签关闭按钮样式 */\n::v-deep .selected-bank-tag .el-tag__close {\n  color: #909399 !important;\n  font-size: 12px !important;\n  margin-left: 6px !important;\n  cursor: pointer !important;\n  border-radius: 50% !important;\n  width: 16px !important;\n  height: 16px !important;\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  line-height: 1 !important;\n  vertical-align: middle !important;\n}\n\n::v-deep .selected-bank-tag .el-tag__close:hover {\n  background-color: #909399 !important;\n  color: #fff !important;\n}\n</style>\n\n<script>\nimport { listQuestionBank } from \"@/api/biz/questionBank\"\nimport { listCategory } from \"@/api/biz/category\"\nimport { getQuestionStatistics, listQuestion } from \"@/api/biz/question\"\n\nexport default {\n  name: \"PaperCreate\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    paperId: {\n      type: [String, Number],\n      default: null\n    }\n  },\n  data() {\n    return {\n      // 右侧面板状态\n      rightPanelCollapsed: false,\n      rightPanelWidth: 410,\n      \n      // 折叠面板激活项（accordion模式下为字符串）\n      activeCollapse: 'basic',\n      \n      // 试卷表单数据\n      paperForm: {\n        paperId: null,\n        paperName: '',\n        paperDesc: '',\n        paperType: 1, // 0: 固定试卷, 1: 随机试卷\n        coverImg: '',\n        totalScore: 100,\n        passScore: 60,\n        startTime: null,\n        endTime: null,\n        duration: 90, // 考试时长，分钟\n        lateLimit: 0, // 迟到限制，分钟\n        allowEarlySubmit: 0, // 是否允许提前交卷\n        earlySubmitTime: 40, // 提前交卷时间，分钟\n        showScore: 0, // 是否显示分值\n        showType: 0, // 是否显示题型\n        requireAllAnswered: 0, // 是否要求全部答完才能交卷\n        showResult: 0, // 是否显示成绩\n        showCorrect: 0, // 是否显示对错\n        showAnswer: 0, // 是否显示答案\n        showAnalysis: 0, // 是否显示解析\n        status: 0, // 状态：0未发布 1已发布\n        enableTimeLimit: 0, // 是否启用考试时间限制\n        durationSeconds: 0, // 时长秒数\n        createTime: null\n      },\n      \n      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）\n\n      // 固定试卷相关数据\n      fixedQuestions: [], // 固定试卷的题目列表\n      selectAll: false, // 全选状态\n      isExpanded: false, // 展开状态\n\n      // 手动选题对话框\n      showManualSelectDialog: false,\n      categoryOptions: [], // 分类选项数据\n      cascaderProps: {\n        value: 'id',\n        label: 'name',\n        children: 'children',\n        checkStrictly: false,\n        emitPath: false\n      },\n      manualSelect: {\n        selectedCategory: '', // 选择的题库目录\n        bankSearchKeyword: '', // 题库搜索关键词\n        questionBanks: [], // 题库列表\n        bankPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedBankId: null, // 选择的题库ID\n        questionType: '', // 题型筛选\n        difficulty: '', // 难度筛选\n        questionSearchKeyword: '', // 题目搜索关键词\n        questions: [], // 题目列表\n        questionPagination: {\n          pageNum: 1,\n          pageSize: 10,\n          total: 0\n        },\n        selectedQuestions: [], // 选中的题目\n        selectedStats: {} // 选中题目的统计信息\n      },\n\n      // 固定试卷随机抽题对话框\n      showFixedRandomDialog: false,\n\n      // 固定试卷随机抽题相关数据\n      fixedRandomRules: [], // 临时规则列表，用于固定试卷随机抽题\n      fixedRandomForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n\n      // 批量设置分数对话框\n      showBatchScoreDialog: false,\n      batchScoreForm: {\n        scoreType: 'all', // all: 全部题目, selected: 选中题目, byType: 按题型\n        score: 1, // 分数值\n        questionType: '' // 题型（当scoreType为byType时使用）\n      },\n\n      // 随机规则对话框\n      showRuleDialog: false,\n      // 规则列表\n      rules: [],\n\n      // 添加规则对话框\n      showAddRuleDialog: false,\n      ruleForm: {\n        ruleType: 3, // 1:题型 3:题库\n        generateType: 'divided', // one:整体设置 divided:分开设置\n        selectedItems: [],\n        selectedQuestionTypes: [] // 选择的题型\n      },\n      // 当前操作状态\n      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则\n      editingRule: null, // 正在编辑的规则\n      parentRule: null, // 父规则（添加子规则时使用）\n      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量\n      questionBanks: [],\n      questionBankLoading: false,\n      categoryOptions: [],\n      searchKeyword: '',\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n    }\n  },\n  computed: {\n    // 已选择的题库\n    selectedBanks() {\n      return this.questionBanks.filter(bank =>\n        this.ruleForm.selectedItems.includes(bank.bankId)\n      )\n    },\n\n    // 计算总题目数量\n    totalQuestions() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的题目数\n          rule.children.forEach(child => {\n            total += child.selectedCount || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的题目数\n          total += rule.selectedCount || 0\n        }\n      })\n      return total\n    },\n\n    // 计算总分数\n    totalRuleScore() {\n      let total = 0\n      this.rules.forEach(rule => {\n        if (rule.children && rule.children.length > 0) {\n          // 如果有子规则，计算子规则的总分\n          rule.children.forEach(child => {\n            total += child.totalScore || 0\n          })\n        } else {\n          // 如果没有子规则，计算父规则的总分\n          total += rule.totalScore || 0\n        }\n      })\n      return total\n    },\n\n    // 获取父规则列表（只包含没有parentId的规则）\n    parentRules() {\n      return this.rules.filter(rule => !rule.parentId)\n    },\n\n    // 获取已使用的题库ID列表\n    usedBankIds() {\n      const usedIds = new Set()\n      this.rules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    },\n\n    // 计算已添加到试卷的题目统计\n    fixedQuestionStats() {\n      const stats = {}\n      this.fixedQuestions.forEach(question => {\n        const type = question.type\n        stats[type] = (stats[type] || 0) + 1\n      })\n      return stats\n    },\n\n    // 固定试卷随机抽题总题数\n    fixedRandomTotalQuestions() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.selectedCount || 0\n      })\n      return total\n    },\n\n    // 固定试卷随机抽题总分数\n    fixedRandomTotalScore() {\n      let total = 0\n      this.fixedRandomRules.forEach(rule => {\n        total += rule.totalScore || 0\n      })\n      return total\n    },\n\n    // 获取固定试卷随机抽题父规则列表（只包含没有parentId的规则）\n    fixedRandomParentRules() {\n      return this.fixedRandomRules.filter(rule => !rule.parentId)\n    },\n\n    // 获取固定试卷随机抽题已使用的题库ID列表\n    usedFixedRandomBankIds() {\n      const usedIds = new Set()\n      this.fixedRandomRules.forEach(rule => {\n        if (rule.type === 3 && rule.selectedItems) {\n          rule.selectedItems.forEach(bankId => {\n            usedIds.add(bankId)\n          })\n        }\n      })\n      return Array.from(usedIds)\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val && this.paperId) {\n        this.loadPaperData()\n      }\n    },\n\n    // 监听题型选择变化\n    'ruleForm.selectedQuestionTypes': {\n      async handler(newVal) {\n        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {\n          await this.updateQuestionTypeCount()\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    /** 返回试卷列表 */\n    handleBack() {\n      this.$emit('close')\n    },\n    \n    /** 切换右侧面板 */\n    toggleRightPanel() {\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\n      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400\n    },\n    \n    /** 考试入口 */\n    handleExamEntry() {\n      this.$message.info('考试入口功能开发中...')\n    },\n    \n    /** 设置考试页面 */\n    handlePageSetting() {\n      this.$message.info('设置考试页面功能开发中...')\n    },\n    \n    /** 发布试卷 */\n    handlePublish() {\n      this.$message.info('发布试卷功能开发中...')\n    },\n    \n    /** 邀请考生 */\n    handleInviteStudents() {\n      this.$message.info('邀请考生功能开发中...')\n    },\n    \n    /** 已邀请列表 */\n    handleInviteList() {\n      this.$message.info('已邀请列表功能开发中...')\n    },\n    \n\n    \n    /** 添加规则（随机试卷） */\n    handleAddRule() {\n      this.showRuleDialog = true\n    },\n\n    /** 编辑规则（打开规则编辑界面） */\n    handleEditRules() {\n      this.showRuleDialog = true\n    },\n\n    /** 添加一级规则 */\n    handleAddFirstRule() {\n      this.currentOperation = 'add'\n      this.editingRule = null\n\n      // 如果已有规则，限制只能选择相同类型\n      if (this.rules.length > 0) {\n        this.ruleForm.ruleType = this.rules[0].type\n      } else {\n        this.ruleForm.ruleType = 3 // 默认题库\n      }\n\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 加载题库列表 */\n    loadQuestionBanks() {\n      this.questionBankLoading = true\n      // 同时加载题库和分类数据\n      Promise.all([\n        listQuestionBank(this.queryParams),\n        listCategory({ pageSize: 1000 })\n      ]).then(([bankResponse, categoryResponse]) => {\n        const questionBanks = bankResponse.rows || []\n        this.total = bankResponse.total || 0\n        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []\n\n        // 为每个题库获取题目统计\n        const statisticsPromises = questionBanks.map(bank =>\n          getQuestionStatistics(bank.bankId).then(stats => {\n            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0\n            return bank\n          }).catch(() => {\n            bank.questionCount = 0\n            return bank\n          })\n        )\n\n        Promise.all(statisticsPromises).then(banksWithStats => {\n          this.questionBanks = banksWithStats\n          this.questionBankLoading = false\n        })\n      }).catch(() => {\n        this.questionBankLoading = false\n      })\n    },\n\n    /** 保存规则 */\n    handleSaveRules() {\n      this.$message.info('保存规则功能开发中...')\n      this.showRuleDialog = false\n    },\n\n    /** 保存单个规则 */\n    async handleSaveRule() {\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {\n        this.$message.warning('请至少选择一个题库')\n        return\n      }\n      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {\n        this.$message.warning('请至少选择一个题型')\n        return\n      }\n\n      // 题型规则验证：检查是否有可用题目\n      if (this.ruleForm.ruleType === 1) {\n        if (this.currentQuestionTypeCount === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')\n          return\n        }\n      }\n\n      if (this.currentOperation === 'edit') {\n        // 编辑现有规则\n        this.updateExistingRule()\n      } else if (this.currentOperation === 'addFixedRandom') {\n        // 添加固定试卷随机抽题规则\n        this.addFixedRandomRule()\n      } else if (this.currentOperation === 'addFixedRandomSub') {\n        // 添加固定试卷随机抽题子规则\n        this.addFixedRandomSubRule()\n      } else {\n        // 添加新规则（包括添加子规则）\n        this.addNewRule()\n      }\n\n      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')\n      this.showAddRuleDialog = false\n\n      // 重置表单\n      this.resetRuleForm()\n    },\n\n    /** 更新现有规则 */\n    updateExistingRule() {\n      const rule = this.editingRule\n      rule.type = this.ruleForm.ruleType\n      rule.generateType = this.ruleForm.generateType\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n\n        // 重新计算总分，确保选取数量不超过最大题目数\n        if (rule.selectedCount > rule.maxQuestions) {\n          rule.selectedCount = rule.maxQuestions\n        }\n        rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n    },\n\n    /** 添加新规则 */\n    addNewRule() {\n      // 如果是添加子规则，按原逻辑处理\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        this.addSingleRule()\n        return\n      }\n\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index, // 确保每个规则有唯一ID\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 0.5,\n            totalScore: 0.5,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateRuleScore(rule)\n          this.rules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleRule()\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    addFixedRandomRule() {\n      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则\n      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {\n        this.selectedBanks.forEach((bank, index) => {\n          const rule = {\n            id: Date.now() + index,\n            type: this.ruleForm.ruleType,\n            generateType: this.ruleForm.generateType,\n            selectedCount: 1,\n            scorePerQuestion: 1,\n            totalScore: 1,\n            selectedItems: [bank.bankId],\n            selectedBanks: [{\n              bankId: bank.bankId,\n              bankName: bank.bankName,\n              questionCount: bank.questionCount\n            }],\n            maxQuestions: bank.questionCount || 0\n          }\n          this.updateFixedRandomRuleScore(rule)\n          this.fixedRandomRules.push(rule)\n        })\n      } else {\n        // 整体设置或其他情况，创建单个规则\n        this.addSingleFixedRandomRule()\n      }\n    },\n\n    /** 添加单个固定试卷随机抽题规则 */\n    addSingleFixedRandomRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      }\n\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    addFixedRandomSubRule() {\n      const rule = {\n        id: Date.now(),\n        type: this.ruleForm.ruleType,\n        parentId: this.parentRule.id,\n        selectedCount: 1,\n        scorePerQuestion: 1,\n        totalScore: 1,\n        maxQuestions: 0\n      }\n\n      if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount\n      } else if (this.ruleForm.ruleType === 3) {\n        // 题库规则（虽然在固定试卷随机抽题中子规则主要是题型，但保持兼容性）\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      }\n\n      // 确保父规则有children数组\n      if (!this.parentRule.children) {\n        this.$set(this.parentRule, 'children', [])\n      }\n\n      // 添加到父规则的children中\n      this.parentRule.children.push(rule)\n\n      // 同时添加到主规则列表中（用于统计和抽题）\n      this.fixedRandomRules.push(rule)\n      this.updateFixedRandomRuleScore(rule)\n    },\n\n    /** 添加单个规则 */\n    addSingleRule() {\n      const rule = {\n        id: Date.now(), // 临时ID\n        type: this.ruleForm.ruleType,\n        generateType: this.ruleForm.generateType,\n        selectedCount: 1, // 默认选取1题\n        scorePerQuestion: 0.5, // 默认每题0.5分\n        totalScore: 0.5, // 默认总分0.5分\n        maxQuestions: 0 // 最大题目数\n      }\n\n      // 如果是添加子规则\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        rule.parentId = this.parentRule.id\n\n        // 确保父规则有children数组\n        if (!this.parentRule.children) {\n          this.$set(this.parentRule, 'children', [])\n        }\n\n        // 添加到父规则的children中\n        this.parentRule.children.push(rule)\n      }\n\n      if (this.ruleForm.ruleType === 3) {\n        // 题库规则\n        rule.selectedItems = [...this.ruleForm.selectedItems]\n        rule.selectedBanks = this.selectedBanks.map(bank => ({\n          bankId: bank.bankId,\n          bankName: bank.bankName,\n          questionCount: bank.questionCount\n        }))\n        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)\n      } else if (this.ruleForm.ruleType === 1) {\n        // 题型规则\n        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]\n        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量\n      }\n\n      // 只有父规则才添加到主规则列表\n      if (this.currentOperation !== 'addSub') {\n        this.rules.push(rule)\n      }\n\n      // 更新规则分数\n      this.updateRuleScore(rule)\n    },\n\n    /** 重置规则表单 */\n    resetRuleForm() {\n      this.ruleForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n      // 只有不是固定试卷随机抽题时才重置操作状态\n      if (this.currentOperation !== 'addFixedRandom' && this.currentOperation !== 'addFixedRandomSub') {\n        this.currentOperation = 'add'\n        this.editingRule = null\n        this.parentRule = null\n      }\n      this.currentQuestionTypeCount = 0\n    },\n\n    /** 搜索题库 */\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = this.searchKeyword || undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 重置搜索 */\n    handleReset() {\n      this.searchKeyword = ''\n      this.queryParams.pageNum = 1\n      this.queryParams.bankName = undefined\n      this.loadQuestionBanks()\n    },\n\n    /** 表格选择变化 */\n    handleSelectionChange(selection) {\n      this.ruleForm.selectedItems = selection.map(item => item.bankId)\n    },\n\n    /** 移除已选择的题库 */\n    removeSelectedBank(bankId) {\n      const index = this.ruleForm.selectedItems.indexOf(bankId)\n      if (index > -1) {\n        this.ruleForm.selectedItems.splice(index, 1)\n      }\n\n      // 同步取消表格中的勾选状态\n      this.$nextTick(() => {\n        const table = this.$refs.questionBankTable\n        if (table) {\n          // 找到对应的行数据\n          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)\n          if (rowToDeselect) {\n            table.toggleRowSelection(rowToDeselect, false)\n          }\n        }\n      })\n    },\n\n    /** 分页变化 */\n    handleCurrentChange(page) {\n      this.queryParams.pageNum = page\n      this.loadQuestionBanks()\n    },\n\n    /** 根据分类ID获取分类名称 */\n    getCategoryName(categoryId) {\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\n      return category ? category.name : '未分类'\n    },\n\n    /** 在分类数据中查找分类（支持扁平和树形结构） */\n    findCategoryById(categories, id) {\n      // 创建扁平化的分类列表\n      const flatCategories = this.flattenCategories(categories)\n\n      // 在扁平化列表中查找\n      const category = flatCategories.find(cat => cat.id === id)\n      return category || null\n    },\n\n    /** 将树形分类数据扁平化 */\n    flattenCategories(categories) {\n      let result = []\n\n      function flatten(cats) {\n        for (const cat of cats) {\n          result.push(cat)\n          if (cat.children && cat.children.length > 0) {\n            flatten(cat.children)\n          }\n        }\n      }\n\n      flatten(categories)\n      return result\n    },\n\n    /** 更新规则分数 */\n    updateRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 获取题型文本 */\n    getQuestionTypeText(questionTypes) {\n      const questionTypeMap = {\n        '1': '单选题', '2': '多选题', '3': '判断题',\n        1: '单选题', 2: '多选题', 3: '判断题'\n      }\n\n      // 如果是数组，处理多个题型\n      if (Array.isArray(questionTypes)) {\n        return questionTypes.map(t => questionTypeMap[t]).join('、')\n      }\n\n      // 如果是单个值，直接返回对应文本\n      return questionTypeMap[questionTypes] || '未知'\n    },\n\n\n\n    /** 获取子规则标签 */\n    getChildRuleLabel(childRule) {\n      if (childRule.type === 3) {\n        return '题库：'\n      } else if (childRule.type === 1) {\n        // 题型子规则只显示具体的题型名称，不显示\"题型：\"前缀\n        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return questionTypeMap[childRule.selectedQuestionTypes[0]]\n        } else {\n          return '题型'\n        }\n      }\n      return '规则：'\n    },\n\n    /** 获取规则类型标签（用于random-paper显示） */\n    getRuleTypeLabel(rule) {\n      if (rule.type === 3) {\n        return '题库：'\n      } else if (rule.type === 1) {\n        return '题型：'\n      }\n      return '规则：'\n    },\n\n    /** 获取规则内容（用于random-paper显示） */\n    getRuleContent(rule) {\n      if (rule.type === 3) {\n        // 题库规则显示题库名称\n        if (rule.selectedBanks && rule.selectedBanks.length > 0) {\n          return rule.selectedBanks.map(bank => bank.bankName).join('、')\n        }\n        return '未选择题库'\n      } else if (rule.type === 1) {\n        // 题型规则显示题型名称\n        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {\n          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }\n          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')\n        }\n        return '未选择题型'\n      }\n      return '未配置'\n    },\n\n    /** 添加子规则 */\n    addSubRule(rule) {\n      this.currentOperation = 'addSub'\n      this.parentRule = rule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n\n      if (rule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 编辑规则 */\n    editRule(rule) {\n      this.currentOperation = 'edit'\n      this.editingRule = rule\n      this.ruleForm.ruleType = rule.type\n      this.ruleForm.generateType = rule.generateType\n\n      if (rule.type === 3) {\n        // 题库规则\n        this.ruleForm.selectedItems = [...rule.selectedItems]\n      } else if (rule.type === 1) {\n        // 题型规则\n        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]\n        // 编辑题型规则时，更新题目数量\n        this.$nextTick(() => {\n          this.updateQuestionTypeCount()\n        })\n      }\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 删除规则 */\n    deleteRule(rule) {\n      this.$confirm('确定要删除这条规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = this.rules.findIndex(r => r.id === rule.id)\n        if (index > -1) {\n          this.rules.splice(index, 1)\n          this.$message.success('规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 删除子规则 */\n    deleteChildRule(parentRule, childRule) {\n      this.$confirm('确定要删除这条子规则吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const index = parentRule.children.findIndex(child => child.id === childRule.id)\n        if (index > -1) {\n          parentRule.children.splice(index, 1)\n          this.$message.success('子规则删除成功')\n        }\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 获取对话框标题 */\n    getDialogTitle() {\n      switch (this.currentOperation) {\n        case 'add':\n          return '添加规则'\n        case 'edit':\n          return '编辑规则'\n        case 'addSub':\n          return '添加子规则'\n        case 'addFixedRandom':\n          return '添加抽题规则'\n        case 'addFixedRandomSub':\n          return '添加题型规则'\n        default:\n          return '添加规则'\n      }\n    },\n\n    /** 是否显示规则类型选择 */\n    shouldShowRuleTypeSelection() {\n      // 固定试卷随机抽题的一级规则只能选择题库，但仍显示选择界面\n      if (this.currentOperation === 'addFixedRandom') {\n        return true\n      }\n      // 固定试卷随机抽题的子规则可以选择规则类型\n      if (this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n      // 其他操作都显示规则类型选择\n      return true\n    },\n\n    /** 是否显示规则类型选项 */\n    shouldShowRuleType(ruleType) {\n      // 编辑时只显示当前规则的类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type === ruleType\n      }\n\n      // 添加子规则时的逻辑\n      if (this.currentOperation === 'addSub' && this.parentRule) {\n        // 题型规则的子规则只能是题库规则\n        if (this.parentRule.type === 1) {\n          return ruleType === 3\n        }\n        // 题库规则的子规则只能是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 添加一级规则时的逻辑\n      if (this.currentOperation === 'add') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题一级规则时的逻辑\n      if (this.currentOperation === 'addFixedRandom') {\n        // 一级规则只能选择题库\n        return ruleType === 3\n      }\n\n      // 固定试卷随机抽题子规则时的逻辑\n      if (this.currentOperation === 'addFixedRandomSub' && this.parentRule) {\n        // 题库规则的子规则可以是题型规则\n        if (this.parentRule.type === 3) {\n          return ruleType === 1\n        }\n        // 其他情况不能选择父规则的类型\n        return ruleType !== this.parentRule.type\n      }\n\n      // 其他情况显示所有类型\n      return true\n    },\n\n    /** 是否显示特定题型选项 */\n    shouldShowQuestionType(questionType) {\n      // 如果不是添加子规则，显示所有题型\n      if (this.currentOperation !== 'addSub' && this.currentOperation !== 'addFixedRandomSub') {\n        return true\n      }\n\n      if (!this.parentRule) {\n        return true\n      }\n\n      // 获取父规则下已有的题型子规则中选择的题型\n      const existingQuestionTypes = []\n      if (this.parentRule.children) {\n        this.parentRule.children.forEach(child => {\n          if (child.type === 1 && child.selectedQuestionTypes) {\n            existingQuestionTypes.push(...child.selectedQuestionTypes)\n          }\n        })\n      }\n\n      // 如果该题型已经被选择过，则隐藏\n      return !existingQuestionTypes.includes(questionType)\n    },\n\n    /** 判断表格行是否可选择 */\n    isRowSelectable(row) {\n      // 如果是编辑操作，允许选择\n      if (this.currentOperation === 'edit') {\n        return true\n      }\n\n      // 如果是添加子规则，允许选择（子规则不涉及题库选择）\n      if (this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') {\n        return true\n      }\n\n      // 如果是固定试卷随机抽题，检查该题库是否已经被使用\n      if (this.currentOperation === 'addFixedRandom') {\n        return !this.usedFixedRandomBankIds.includes(row.bankId)\n      }\n\n      // 如果是添加一级规则，检查题库是否已被使用\n      return !this.usedBankIds.includes(row.bankId)\n    },\n\n    /** 获取表格行的样式类名 */\n    getRowClassName({ row }) {\n      // 如果题库已被使用且不是编辑操作，添加禁用样式\n      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {\n        return 'disabled-row'\n      }\n      return ''\n    },\n\n    /** 更新题型题目数量 */\n    async updateQuestionTypeCount() {\n      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {\n        this.currentQuestionTypeCount = 0\n        return\n      }\n\n      // 获取题库信息\n      let selectedBanks = []\n      if ((this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') && this.parentRule && this.parentRule.type === 3) {\n        // 添加子规则时，使用父规则的题库\n        selectedBanks = this.parentRule.selectedBanks || []\n      } else {\n        // 其他情况使用当前选择的题库\n        selectedBanks = this.selectedBanks\n      }\n\n      if (!selectedBanks.length) {\n        this.currentQuestionTypeCount = 0\n        this.$message.warning('请先选择题库')\n        return\n      }\n\n      try {\n        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)\n        this.currentQuestionTypeCount = count\n\n        if (count === 0) {\n          this.$message.warning('所选题型在当前题库中没有可用题目')\n        }\n      } catch (error) {\n        console.error('查询题目数量失败:', error)\n        this.currentQuestionTypeCount = 0\n        this.$message.error('查询题目数量失败')\n      }\n    },\n\n    /** 根据题库和题型查询题目数量 */\n    async getQuestionCountByType(banks, questionTypes) {\n      if (!banks.length || !questionTypes.length) {\n        return 0\n      }\n\n      let totalCount = 0\n\n      // 遍历每个题库，查询题目统计\n      for (const bank of banks) {\n        try {\n          // 使用已导入的API方法\n          const response = await getQuestionStatistics(bank.bankId)\n          console.log(`题库${bank.bankId}统计数据:`, response)\n\n          if (response.code === 200 && response.data) {\n            const statistics = response.data\n\n            // 根据选择的题型累加数量\n            questionTypes.forEach(type => {\n              switch (type) {\n                case '1': // 单选题\n                  totalCount += statistics.singleChoice || 0\n                  break\n                case '2': // 多选题\n                  totalCount += statistics.multipleChoice || 0\n                  break\n                case '3': // 判断题\n                  totalCount += statistics.judgment || 0\n                  break\n              }\n            })\n          }\n        } catch (error) {\n          console.error(`查询题库${bank.bankId}统计信息失败:`, error)\n        }\n      }\n\n      console.log(`总题目数量: ${totalCount}`)\n      return totalCount\n    },\n\n    /** 是否禁用规则类型 */\n    shouldDisableRuleType(ruleType) {\n      // 编辑时不能更改类型\n      if (this.currentOperation === 'edit') {\n        return this.editingRule.type !== ruleType\n      }\n\n      // 添加一级规则时，只能选择题库，禁用其他类型\n      if (this.currentOperation === 'add') {\n        return ruleType !== 3\n      }\n\n      return false\n    },\n    \n    /** 全选/取消全选 */\n    handleSelectAll() {\n      this.fixedQuestions.forEach(question => {\n        question.selected = this.selectAll\n      })\n    },\n\n    /** 删除选中题目 */\n    handleDeleteSelected() {\n      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)\n      if (selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)\n        this.selectAll = false\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 手动选题 */\n    handleManualSelect() {\n      this.showManualSelectDialog = true\n      this.initManualSelectData()\n    },\n\n    /** 初始化手动选题数据 */\n    initManualSelectData() {\n      // 重置数据\n      this.manualSelect.selectedCategory = ''\n      this.manualSelect.bankSearchKeyword = ''\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.manualSelect.selectedQuestions = []\n      this.manualSelect.selectedStats = {}\n\n      // 加载分类数据\n      this.loadCategoryTree()\n\n      // 加载题库列表\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 加载分类树数据 */\n    loadCategoryTree() {\n      listCategory({ pageSize: 1000 }).then(response => {\n        const categories = response.rows || []\n        this.categoryOptions = this.buildCategoryTree(categories)\n      }).catch(error => {\n        console.error('加载分类数据失败:', error)\n        this.categoryOptions = []\n      })\n    },\n\n    /** 构建分类树 */\n    buildCategoryTree(categories) {\n      const map = {}\n\n      // 先将所有分类放入map中\n      categories.forEach(category => {\n        map[category.id] = { ...category, children: [] }\n      })\n\n      // 构建完整的树形结构\n      const result = []\n      categories.forEach(category => {\n        if (category.parentId === 0) {\n          // 顶级分类\n          result.push(map[category.id])\n        } else {\n          // 子分类\n          if (map[category.parentId]) {\n            map[category.parentId].children.push(map[category.id])\n          }\n        }\n      })\n\n      // 清理空的children数组\n      const cleanEmptyChildren = (node) => {\n        if (node.children && node.children.length === 0) {\n          delete node.children\n        } else if (node.children && node.children.length > 0) {\n          node.children.forEach(child => cleanEmptyChildren(child))\n        }\n      }\n\n      // 清理所有节点的空children\n      result.forEach(node => cleanEmptyChildren(node))\n\n      return result\n    },\n\n    /** 获取所有子分类ID */\n    getAllChildCategoryIds(categoryId, categories) {\n      const result = [categoryId]\n\n      const findChildren = (parentId) => {\n        categories.forEach(category => {\n          if (category.parentId === parentId) {\n            result.push(category.id)\n            findChildren(category.id)\n          }\n        })\n      }\n\n      findChildren(categoryId)\n      return result\n    },\n\n    /** 加载手动选题的题库列表 */\n    loadManualSelectQuestionBanks() {\n      let categoryIds = []\n\n      // 如果选择了分类，获取该分类及其所有子分类的ID\n      if (this.manualSelect.selectedCategory) {\n        // 获取原始分类数据（包含所有层级）\n        listCategory({ pageSize: 1000 }).then(response => {\n          const allCategories = response.rows || []\n          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)\n\n          // 执行多次查询，因为后端不支持IN查询\n          this.loadQuestionBanksByCategories(categoryIds)\n        }).catch(error => {\n          console.error('加载分类数据失败:', error)\n          this.loadQuestionBanksByCategories([])\n        })\n      } else {\n        // 没有选择分类，加载所有题库\n        this.loadQuestionBanksByCategories([])\n      }\n    },\n\n    /** 根据分类ID列表加载题库 */\n    loadQuestionBanksByCategories(categoryIds) {\n      const queryParams = {\n        pageNum: this.manualSelect.bankPagination.pageNum,\n        pageSize: this.manualSelect.bankPagination.pageSize,\n        bankName: this.manualSelect.bankSearchKeyword || undefined\n      }\n\n      if (categoryIds.length > 0) {\n        // 如果有分类筛选，需要合并多个分类的结果\n        const promises = categoryIds.map(categoryId => {\n          return listQuestionBank({ ...queryParams, categoryId })\n        })\n\n        Promise.all(promises).then(responses => {\n          const allBanks = []\n          let totalCount = 0\n\n          responses.forEach(response => {\n            if (response.rows) {\n              allBanks.push(...response.rows)\n              totalCount += response.total || 0\n            }\n          })\n\n          // 去重（根据bankId）\n          const uniqueBanks = allBanks.filter((bank, index, self) =>\n            index === self.findIndex(b => b.bankId === bank.bankId)\n          )\n\n          this.manualSelect.questionBanks = uniqueBanks\n          this.manualSelect.bankPagination.total = uniqueBanks.length\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      } else {\n        // 没有分类筛选，直接查询\n        listQuestionBank(queryParams).then(response => {\n          this.manualSelect.questionBanks = response.rows || []\n          this.manualSelect.bankPagination.total = response.total || 0\n        }).catch(error => {\n          console.error('加载题库列表失败:', error)\n          this.$message.error('加载题库列表失败')\n          this.manualSelect.questionBanks = []\n          this.manualSelect.bankPagination.total = 0\n        })\n      }\n    },\n\n    /** 搜索题库 */\n    searchQuestionBanks() {\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 选择题库 */\n    selectQuestionBank(row) {\n      this.manualSelect.selectedBankId = row.bankId\n      this.loadQuestions()\n    },\n\n    /** 加载题目列表 */\n    loadQuestions() {\n      if (!this.manualSelect.selectedBankId) {\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n        return\n      }\n\n      const queryParams = {\n        pageNum: this.manualSelect.questionPagination.pageNum,\n        pageSize: this.manualSelect.questionPagination.pageSize,\n        bankId: this.manualSelect.selectedBankId,\n        questionType: this.manualSelect.questionType || undefined,\n        difficulty: this.manualSelect.difficulty || undefined,\n        questionContent: this.manualSelect.questionSearchKeyword || undefined\n      }\n\n      listQuestion(queryParams).then(response => {\n        this.manualSelect.questions = response.rows || []\n        this.manualSelect.questionPagination.total = response.total || 0\n      }).catch(error => {\n        console.error('加载题目列表失败:', error)\n        this.$message.error('加载题目列表失败')\n        this.manualSelect.questions = []\n        this.manualSelect.questionPagination.total = 0\n      })\n    },\n\n    /** 搜索题目 */\n    searchQuestions() {\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 重置题目搜索 */\n    resetQuestionSearch() {\n      this.manualSelect.questionType = ''\n      this.manualSelect.difficulty = ''\n      this.manualSelect.questionSearchKeyword = ''\n      this.searchQuestions()\n    },\n\n    /** 随机抽题 */\n    handleRandomSelect() {\n      // 为固定试卷打开随机规则设置对话框\n      this.showFixedRandomDialog = true\n      this.resetFixedRandomForm()\n      this.loadQuestionBanks()\n    },\n\n    /** 重置固定试卷随机抽题表单 */\n    resetFixedRandomForm() {\n      this.fixedRandomRules = []\n      this.fixedRandomForm = {\n        ruleType: 3,\n        generateType: 'divided',\n        selectedItems: [],\n        selectedQuestionTypes: []\n      }\n    },\n\n    /** 添加固定试卷随机抽题规则 */\n    handleAddFixedRandomRule() {\n      this.currentOperation = 'addFixedRandom'\n      this.editingRule = null\n      this.ruleForm.ruleType = 3 // 固定为题库类型\n      this.showAddRuleDialog = true\n      // 重置查询参数\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 添加固定试卷随机抽题子规则 */\n    handleAddFixedRandomSubRule(parentRule) {\n      this.currentOperation = 'addFixedRandomSub'\n      this.editingRule = null\n      this.parentRule = parentRule\n\n      // 根据父规则类型确定子规则类型\n      let defaultRuleType\n      if (parentRule.type === 3) {\n        // 题库规则的子规则只能是题型规则\n        defaultRuleType = 1\n      } else {\n        // 其他情况的默认处理\n        defaultRuleType = 1\n      }\n\n      this.ruleForm.ruleType = defaultRuleType\n      this.ruleForm.generateType = 'divided'\n      this.ruleForm.selectedItems = []\n      this.ruleForm.selectedQuestionTypes = []\n\n      this.showAddRuleDialog = true\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        bankName: undefined\n      }\n      this.searchKeyword = ''\n      this.loadQuestionBanks()\n    },\n\n    /** 更新固定试卷随机抽题规则分数 */\n    updateFixedRandomRuleScore(rule) {\n      rule.totalScore = rule.selectedCount * rule.scorePerQuestion\n    },\n\n    /** 删除固定试卷随机抽题规则 */\n    deleteFixedRandomRule(rule) {\n      // 如果是父规则，需要同时删除所有子规则\n      if (rule.children && rule.children.length > 0) {\n        // 删除所有子规则\n        rule.children.forEach(childRule => {\n          const childIndex = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n          if (childIndex > -1) {\n            this.fixedRandomRules.splice(childIndex, 1)\n          }\n        })\n      }\n\n      // 删除父规则\n      const index = this.fixedRandomRules.findIndex(r => r.id === rule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 删除固定试卷随机抽题子规则 */\n    deleteFixedRandomChildRule(parentRule, childRule) {\n      // 从父规则的children中删除\n      if (parentRule.children) {\n        const childIndex = parentRule.children.findIndex(r => r.id === childRule.id)\n        if (childIndex > -1) {\n          parentRule.children.splice(childIndex, 1)\n        }\n      }\n\n      // 从主规则列表中删除\n      const index = this.fixedRandomRules.findIndex(r => r.id === childRule.id)\n      if (index > -1) {\n        this.fixedRandomRules.splice(index, 1)\n      }\n    },\n\n    /** 确认固定试卷随机抽题 */\n    async handleConfirmFixedRandomSelect() {\n      if (this.fixedRandomRules.length === 0) {\n        this.$message.warning('请先添加抽题规则')\n        return\n      }\n\n      try {\n        // 根据规则随机抽取题目\n        const selectedQuestions = await this.extractQuestionsFromRules(this.fixedRandomRules)\n\n        if (selectedQuestions.length === 0) {\n          this.$message.warning('根据当前规则未能抽取到题目')\n          return\n        }\n\n        // 将抽取的题目添加到固定试卷中\n        selectedQuestions.forEach((question, index) => {\n          this.fixedQuestions.push({\n            id: Date.now() + index,\n            questionId: question.questionId,\n            content: question.questionContent,\n            type: question.questionType,\n            difficulty: question.difficulty,\n            options: question.options,\n            score: question.score || 1, // 使用规则中设置的分数\n            selected: false,\n            expanded: false\n          })\n        })\n\n        this.showFixedRandomDialog = false\n        this.$message.success(`成功随机抽取并添加 ${selectedQuestions.length} 道题目`)\n      } catch (error) {\n        console.error('随机抽题失败:', error)\n        this.$message.error('随机抽题失败，请重试')\n      }\n    },\n\n    /** 根据规则抽取题目 */\n    async extractQuestionsFromRules(rules) {\n      const allQuestions = []\n\n      // 处理父规则（题库规则）\n      const parentRules = rules.filter(rule => !rule.parentId)\n\n      for (const parentRule of parentRules) {\n        try {\n          // 获取子规则\n          const childRules = rules.filter(rule => rule.parentId === parentRule.id)\n\n          if (childRules.length > 0) {\n            // 有子规则：按子规则的题型从父规则的题库中抽取\n            for (const childRule of childRules) {\n              let questions = []\n\n              // 从父规则的题库中按子规则的题型抽取题目\n              for (const bank of parentRule.selectedBanks) {\n                for (const questionType of childRule.selectedQuestionTypes) {\n                  const queryParams = {\n                    bankId: bank.bankId,\n                    questionType: questionType,\n                    pageNum: 1,\n                    pageSize: 1000\n                  }\n\n                  const response = await listQuestion(queryParams)\n                  if (response.rows && response.rows.length > 0) {\n                    questions = questions.concat(response.rows)\n                  }\n                }\n              }\n\n              // 随机选择指定数量的题目\n              if (questions.length > 0) {\n                const shuffled = this.shuffleArray([...questions])\n                const selectedCount = Math.min(childRule.selectedCount, shuffled.length)\n                const selectedQuestions = shuffled.slice(0, selectedCount)\n\n                // 为每个题目设置分数\n                selectedQuestions.forEach(question => {\n                  question.score = childRule.scorePerQuestion\n                })\n\n                allQuestions.push(...selectedQuestions)\n              }\n            }\n          } else {\n            // 没有子规则：直接从父规则的题库中抽取题目\n            let questions = []\n\n            for (const bank of parentRule.selectedBanks) {\n              const queryParams = {\n                bankId: bank.bankId,\n                pageNum: 1,\n                pageSize: 1000\n              }\n\n              const response = await listQuestion(queryParams)\n              if (response.rows && response.rows.length > 0) {\n                questions = questions.concat(response.rows)\n              }\n            }\n\n            // 随机选择指定数量的题目\n            if (questions.length > 0) {\n              const shuffled = this.shuffleArray([...questions])\n              const selectedCount = Math.min(parentRule.selectedCount, shuffled.length)\n              const selectedQuestions = shuffled.slice(0, selectedCount)\n\n              // 为每个题目设置分数\n              selectedQuestions.forEach(question => {\n                question.score = parentRule.scorePerQuestion\n              })\n\n              allQuestions.push(...selectedQuestions)\n            }\n          }\n        } catch (error) {\n          console.error('抽取题目失败:', error)\n        }\n      }\n\n      return allQuestions\n    },\n\n    /** 数组随机排序 */\n    shuffleArray(array) {\n      for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [array[j], array[i]]\n      }\n      return array\n    },\n\n    /** 排序 */\n    handleSort() {\n      this.$message.info('排序功能开发中...')\n    },\n\n    /** 批量设置分数 */\n    handleBatchSetScore() {\n      if (this.fixedQuestions.length === 0) {\n        this.$message.warning('暂无题目，无法设置分数')\n        return\n      }\n\n      // 重置表单\n      this.batchScoreForm = {\n        scoreType: 'all',\n        score: 1,\n        questionType: ''\n      }\n\n      this.showBatchScoreDialog = true\n    },\n\n    /** 获取批量设置分数影响的题目数量 */\n    getBatchScoreAffectedCount() {\n      if (this.fixedQuestions.length === 0) {\n        return 0\n      }\n\n      switch (this.batchScoreForm.scoreType) {\n        case 'all':\n          return this.fixedQuestions.length\n        case 'selected':\n          return this.fixedQuestions.filter(q => q.selected).length\n        case 'byType':\n          if (!this.batchScoreForm.questionType) {\n            return 0\n          }\n          return this.fixedQuestions.filter(q => q.type == this.batchScoreForm.questionType).length\n        default:\n          return 0\n      }\n    },\n\n    /** 确认批量设置分数 */\n    handleConfirmBatchSetScore() {\n      const affectedCount = this.getBatchScoreAffectedCount()\n\n      if (affectedCount === 0) {\n        this.$message.warning('没有符合条件的题目')\n        return\n      }\n\n      let targetQuestions = []\n\n      switch (this.batchScoreForm.scoreType) {\n        case 'all':\n          targetQuestions = this.fixedQuestions\n          break\n        case 'selected':\n          targetQuestions = this.fixedQuestions.filter(q => q.selected)\n          if (targetQuestions.length === 0) {\n            this.$message.warning('请先选择要设置分数的题目')\n            return\n          }\n          break\n        case 'byType':\n          if (!this.batchScoreForm.questionType) {\n            this.$message.warning('请选择题型')\n            return\n          }\n          targetQuestions = this.fixedQuestions.filter(q => q.type == this.batchScoreForm.questionType)\n          break\n      }\n\n      // 批量设置分数\n      targetQuestions.forEach(question => {\n        question.score = this.batchScoreForm.score\n      })\n\n      this.showBatchScoreDialog = false\n      this.$message.success(`成功为 ${affectedCount} 道题目设置分数为 ${this.batchScoreForm.score} 分`)\n    },\n\n    /** 导出 */\n    handleExport() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    /** 展开/收起 */\n    handleToggleExpand() {\n      this.isExpanded = !this.isExpanded\n      // 批量设置所有题目的展开状态\n      this.fixedQuestions.forEach(question => {\n        question.expanded = this.isExpanded\n      })\n      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)\n    },\n\n    /** 题目选择变化 */\n    handleQuestionSelectionChange(selection) {\n      this.manualSelect.selectedQuestions = selection\n      this.updateSelectedStats()\n    },\n\n    /** 更新选中题目统计 */\n    updateSelectedStats() {\n      const stats = {}\n      this.manualSelect.selectedQuestions.forEach(question => {\n        const type = question.questionType\n        stats[type] = (stats[type] || 0) + 1\n      })\n      this.manualSelect.selectedStats = stats\n    },\n\n    /** 确认手动选题 */\n    confirmManualSelect() {\n      if (this.manualSelect.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择题目')\n        return\n      }\n\n      // 将选中的题目添加到固定试卷中\n      this.manualSelect.selectedQuestions.forEach((question, index) => {\n        this.fixedQuestions.push({\n          id: Date.now() + index,\n          questionId: question.questionId,\n          content: question.questionContent,\n          type: question.questionType,\n          difficulty: question.difficulty,\n          options: question.options, // 保存选项信息\n          score: 1, // 默认1分\n          selected: false,\n          expanded: false // 默认收起状态\n        })\n      })\n\n      this.showManualSelectDialog = false\n      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)\n    },\n\n    /** 题库分页大小变化 */\n    handleBankSizeChange(val) {\n      this.manualSelect.bankPagination.pageSize = val\n      this.manualSelect.bankPagination.pageNum = 1\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题库当前页变化 */\n    handleBankCurrentChange(val) {\n      this.manualSelect.bankPagination.pageNum = val\n      this.loadManualSelectQuestionBanks()\n    },\n\n    /** 题目分页大小变化 */\n    handleQuestionSizeChange(val) {\n      this.manualSelect.questionPagination.pageSize = val\n      this.manualSelect.questionPagination.pageNum = 1\n      this.loadQuestions()\n    },\n\n    /** 题目当前页变化 */\n    handleQuestionCurrentChange(val) {\n      this.manualSelect.questionPagination.pageNum = val\n      this.loadQuestions()\n    },\n\n    /** 获取难度文本 */\n    getDifficultyText(difficulty) {\n      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }\n      return difficultyMap[difficulty] || '未知'\n    },\n\n    /** 更新题目分数 */\n    updateQuestionScore(question) {\n      // 分数更新后可以触发总分重新计算\n      this.$forceUpdate()\n    },\n\n    /** 切换题目展开/收起状态 */\n    toggleQuestionExpand(question) {\n      question.expanded = !question.expanded\n    },\n\n    /** 删除单个题目 */\n    deleteQuestion(question, index) {\n      this.$confirm('确定删除这道题目吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.fixedQuestions.splice(index, 1)\n        this.$message.success('删除成功')\n      }).catch(() => {\n        // 用户取消删除\n      })\n    },\n\n    /** 解析选项JSON字符串 */\n    parseOptions(optionsStr) {\n      try {\n        if (typeof optionsStr === 'string') {\n          return JSON.parse(optionsStr)\n        }\n        return optionsStr || []\n      } catch (error) {\n        console.error('解析选项失败:', error)\n        return []\n      }\n    },\n\n    /** 判断题目是否可选择 */\n    isQuestionSelectable(row) {\n      // 检查题目是否已经添加到试卷中\n      return !this.fixedQuestions.some(question => question.questionId === row.questionId)\n    },\n\n    /** 获取题目行的样式类名 */\n    getQuestionRowClassName({ row }) {\n      // 如果题目已被添加，添加禁用样式\n      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {\n        return 'disabled-question-row'\n      }\n      return ''\n    },\n    \n    /** 上传前验证 */\n    beforeUpload(file) {\n      const isImage = file.type.indexOf('image/') === 0\n      const isLt2M = file.size / 1024 / 1024 < 2\n      \n      if (!isImage) {\n        this.$message.error('只能上传图片文件!')\n        return false\n      }\n      if (!isLt2M) {\n        this.$message.error('上传图片大小不能超过 2MB!')\n        return false\n      }\n      return true\n    },\n    \n    /** 格式化日期 */\n    formatDate(date) {\n      const year = date.getFullYear()\n      const month = String(date.getMonth() + 1).padStart(2, '0')\n      const day = String(date.getDate()).padStart(2, '0')\n      const hours = String(date.getHours()).padStart(2, '0')\n      const minutes = String(date.getMinutes()).padStart(2, '0')\n      const seconds = String(date.getSeconds()).padStart(2, '0')\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\n    },\n    \n    /** 加载试卷数据 */\n    loadPaperData() {\n      if (this.paperId) {\n        // TODO: 调用API加载试卷数据\n        console.log('加载试卷数据:', this.paperId)\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.exam-editor {\n  display: flex;\n  height: 100vh;\n  background: #f5f5f5;\n}\n\n.exam-editor-left {\n  flex: 1;\n  padding: 20px;\n  padding-right: 420px; /* 为右侧固定面板留出空间 */\n  overflow-y: auto;\n  transition: padding-right 0.3s ease;\n  background-color: #FFF;\n}\n\n.exam-editor-left.collapsed {\n  padding-right: 70px; /* 右侧面板收起时的空间 */\n}\n\n.exam-editor-right {\n  background: #fff;\n  border-left: 1px solid #e4e7ed;\n  position: fixed;\n  right: 0;\n  top: 0;\n  height: 100vh;\n  transition: width 0.3s ease;\n  z-index: 100;\n}\n\n.collapse-button {\n  position: absolute;\n  left: -15px;\n  top: 20px;\n  width: 30px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e4e7ed;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  font-size: 16px;\n  color: #606266;\n}\n\n.collapse-button:hover {\n  background: #f5f7fa;\n  color: #409eff;\n}\n\n.editPaper_main_top {\n  background: #fff;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.editPaper_main_top .el-button-group {\n  margin-right: 15px;\n  display: inline-block;\n}\n\n.clear_both {\n  clear: both;\n}\n\n.subject_main-wrapper {\n  max-width: 100%;\n}\n\n/* 左侧卡片模块悬停效果 */\n.subject_main-wrapper .el-card {\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n}\n\n.subject_main-wrapper .el-card:hover {\n  border: 2px dashed #409eff;\n  background-color: #fafbff;\n}\n\n.subtitle-title {\n  position: relative;\n}\n\n.slgfont {\n  font-size: 24px;\n  color: #303133;\n}\n\n.paper-count {\n  margin-top: 10px;\n}\n\n.paper-count .el-tag {\n  margin-left: 8px;\n}\n\n\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.tac {\n  text-align: center;\n}\n\n.pd5 {\n  padding: 5px;\n}\n\n.editor-header {\n  padding: 20px;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.editor-header--big {\n  font-size: 20px;\n}\n\n.close-button {\n  font-size: 20px;\n  color: #909399;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  color: #f56c6c;\n  background-color: #fef0f0;\n}\n\n.main {\n  height: calc(100vh - 80px);\n  overflow-y: auto;\n  padding: 0;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.main-collapse {\n  border: none;\n}\n\n.main-collapse .el-collapse-item__header {\n  padding: 0 20px;\n  height: 60px;\n  line-height: 60px;\n  background: #f8f8f8;\n  border-bottom: 1px solid #e4e7ed;\n  font-size: 16px;\n  font-weight: bold;\n}\n\n/* 使用深度选择器覆盖Element UI默认样式 */\n.main-collapse >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n.editor-collapse-item >>> .el-collapse-item__content {\n  padding: 0 !important;\n}\n\n/* Vue 3 深度选择器语法 */\n.main-collapse :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.editor-collapse-item :deep(.el-collapse-item__content) {\n  padding: 0 !important;\n}\n\n.main-module {\n  padding: 20px;\n}\n\n.setting-block {\n  margin-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.setting-block:last-child {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n\n/* 二级设置块容器 */\n.block-expand {\n  margin-top: 10px;\n  padding-left: 20px;\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 二级设置块样式 */\n.sub-setting {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #e8e8e8;\n}\n\n.sub-setting:last-child {\n  border-bottom: none;\n  margin-bottom: 0;\n}\n\n.line {\n  display: flex;\n  align-items: flex-start;\n}\n\n.block-header {\n  justify-content: space-between;\n}\n\n.block-header b {\n  font-size: 14px;\n  color: #303133;\n  min-width: 93px;\n  line-height: 32px;\n}\n\n.input-area {\n  flex: 1;\n  margin-left: 20px;\n}\n\n.input--number {\n  width: 120px;\n}\n\n.dpib {\n  display: inline-block;\n}\n\n.avatar-uploader {\n  border: none !important;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 120px;\n  height: 80px;\n  box-sizing: border-box;\n}\n\n\n\n/* 使用伪元素创建统一的虚线边框 */\n.avatar-uploader::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.avatar-uploader:hover::before {\n  border-color: #409eff;\n}\n\n/* 确保Element UI组件没有边框 */\n.avatar-uploader .el-upload {\n  border: none !important;\n  width: 100%;\n  height: 100%;\n  background: transparent !important;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 120px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n}\n\n.avatar {\n  width: 120px;\n  height: 80px;\n  display: block;\n  object-fit: cover;\n}\n\n.image_area {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bold {\n  font-weight: bold;\n}\n\n.mr10 {\n  margin-right: 10px;\n}\n\n/* 折叠面板标题样式 */\n.collapse-title {\n  margin-left: 15px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.collapse-title i {\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.el-popover__reference {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n}\n\n.el-popover__title {\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n/* 设置标题容器样式 */\n.setting-title {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n}\n\n.setting-title b {\n  font-size: 14px;\n  color: #303133;\n}\n\n/* 出题方式问号图标样式 */\n.paper-type-question {\n  margin-left: 5px;\n  color: #909399;\n  cursor: help;\n  position: relative;\n  z-index: 10;\n  font-size: 14px;\n  line-height: 1;\n}\n\n.paper-type-question:hover {\n  color: #409eff;\n}\n\n/* 出题方式提示框样式 */\n.paper-type-tooltip {\n  max-width: 320px !important;\n  z-index: 2000 !important;\n}\n\n/* 迟到限制提示框样式 */\n.late-limit-tooltip {\n  max-width: 280px !important;\n  z-index: 2000 !important;\n}\n\n/* 随机规则设置对话框样式 */\n.cascade-random-rules {\n  min-height: 400px;\n}\n\n/* 确保对话框层级正确 */\n::v-deep .el-dialog__wrapper {\n  z-index: 3000 !important;\n}\n\n::v-deep .el-dialog {\n  z-index: 3001 !important;\n  position: relative !important;\n}\n\n::v-deep .el-overlay {\n  z-index: 2999 !important;\n}\n\n.topbar {\n  margin-bottom: 20px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.topbar .fl {\n  float: left;\n}\n\n.topbar .summary {\n  margin-left: 15px;\n  color: #666;\n  font-size: 14px;\n}\n\n.topbar .total_score {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.guide-steps-list {\n  margin: 30px 0;\n  padding: 0 20px;\n}\n\n/* 步骤组件样式优化 */\n::v-deep .guide-steps-list .el-step {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__head {\n  text-align: center;\n}\n\n::v-deep .guide-steps-list .el-step__main {\n  text-align: center;\n  margin-top: 10px;\n}\n\n::v-deep .guide-steps-list .el-step__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n::v-deep .guide-steps-list .el-step__description {\n  margin-top: 8px;\n  padding: 0 10px;\n}\n\n.step-content {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.5;\n  margin-top: 5px;\n}\n\n.rules-content {\n  min-height: 200px;\n  margin: 20px 0;\n}\n\n.empty-rules {\n  text-align: center;\n  padding: 60px 0;\n  color: #999;\n  font-size: 14px;\n}\n\n.bottom-panel {\n  text-align: center;\n  padding: 20px 0;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n/* 强制覆盖Element UI折叠面板背景色 */\n.el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n  font-size: 16px !important;\n  font-weight: bold !important;\n}\n\n.el-collapse-item__header.is-active {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 使用属性选择器强制覆盖 */\n[class*=\"el-collapse-item__header\"] {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 深度选择器 */\n.exam-editor >>> .el-collapse-item__header {\n  background: #f8f8f8 !important;\n  background-color: #f8f8f8 !important;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .exam-editor-left {\n    padding-right: 370px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 70px;\n  }\n}\n\n@media (max-width: 768px) {\n  .exam-editor {\n    flex-direction: column;\n  }\n\n  .exam-editor-left {\n    padding-right: 20px;\n  }\n\n  .exam-editor-left.collapsed {\n    padding-right: 20px;\n  }\n\n  .exam-editor-right {\n    position: relative !important;\n    width: 100% !important;\n    height: 50vh;\n    border-left: none;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .collapse-button {\n    display: none;\n  }\n}\n</style>\n"]}]}