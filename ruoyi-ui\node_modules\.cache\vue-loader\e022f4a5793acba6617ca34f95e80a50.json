{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=template&id=241b9cf5&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754447000218}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}