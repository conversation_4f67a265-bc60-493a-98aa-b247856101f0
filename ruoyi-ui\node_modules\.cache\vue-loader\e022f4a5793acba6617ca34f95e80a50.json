{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue?vue&type=template&id=241b9cf5&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\paper\\create.vue", "mtime": 1754447510935}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}