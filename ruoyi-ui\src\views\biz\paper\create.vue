<template>
  <div class="exam-editor">
    <!-- 左侧主要内容区域 -->
    <div class="exam-editor-left" :class="{ collapsed: rightPanelCollapsed }">
      <!-- 顶部操作栏 -->
      <div class="editPaper_main_top">
        <div class="el-button-group">
          <el-button type="primary" @click="handleBack">
            <i class="el-icon-back"></i>
            <span>返回试卷</span>
          </el-button>
        </div>
        
        <div class="el-button-group">
          <el-button @click="handleExamEntry">
            <i class="el-icon-share"></i>
            <span>考试入口</span>
          </el-button>
          <el-button @click="handlePageSetting">
            <i class="el-icon-picture-outline"></i>
            <span>设置考试页面</span>
          </el-button>
          <el-button type="warning" @click="handlePublish">
            <i class="el-icon-upload"></i>
            <span>发布</span>
          </el-button>
        </div>
        
        <div class="el-button-group">
          <el-button type="success" @click="handleInviteStudents">
            邀请考生
          </el-button>
          <el-button type="warning" @click="handleInviteList">
            已邀请列表
          </el-button>
        </div>
        
        <div class="clear_both"></div>
      </div>

      <!-- 试卷信息卡片 -->
      <div class="subject_main-wrapper">
        <div class="subject_main">
          <el-card class="is-hover-shadow" style="width: 100%;">
            <div class="subtitle-title" style="padding: 10px;">
              <div>
                <div style="float: left;">
                  <strong class="slgfont">{{ paperForm.paperName || '新建试卷' }}</strong>
                </div>
                <div style="float: right; color: #ec661a; width: 130px; text-align: right;">
                  <span style="font-size: 40px; font-family: 'Segoe UI'; font-weight: bold;">{{ totalRuleScore }}</span>分
                </div>
                <div style="clear: both;"></div>
                <div class="paper-count" style="font-size: 13px; color: #aaa;">
                  <span v-if="paperForm.createTime">创建时间：{{ paperForm.createTime }}</span>
                  <el-tag type="warning" size="medium" effect="light">共 {{ totalQuestions }} 题</el-tag>
                  <el-tag type="success" size="medium" effect="light">{{ paperForm.paperType === 1 ? '随机试卷' : '固定试卷' }}</el-tag>
                  <el-tag size="medium" effect="light">自动出分</el-tag>
                </div>
                <div class="rich-text" style="display: none;"></div>
              </div>
              <div style="clear: both;"></div>
            </div>
          </el-card>
        </div>

        <!-- 题目设置区域 -->
        <div>
          <div class="mb10 mt10">
            <div class="el-button-group"></div>
          </div>
          
          <div v-if="paperForm.paperType === 1">
            <!-- 随机试卷 -->
            <div class="random-paper">
              <el-card class="is-hover-shadow">
                <!-- 有规则时显示规则内容 -->
                <div v-if="rules.length > 0" style="padding: 10px;">
                  <div class="tac pd5">
                    <div>
                      <el-button type="primary" @click="handleEditRules">
                        <i class="el-icon-edit"></i>
                        <span>编辑规则</span>
                      </el-button>
                    </div>
                  </div>

                  <!-- 规则显示区域 -->
                  <div v-for="rule in parentRules" :key="rule.id" class="rule-display-container">
                    <!-- 父规则显示 -->
                    <div class="parent-rule-display">
                      <span class="rule-label">{{ getRuleTypeLabel(rule) }}</span>
                      <span class="rule-content">{{ getRuleContent(rule) }}</span>
                    </div>

                    <!-- 子规则显示 -->
                    <div v-if="rule.children && rule.children.length > 0" class="children-rules-display">
                      <div v-for="childRule in rule.children" :key="childRule.id" class="child-rule-display">
                        <div class="child-rule-left">
                          <span class="rule-label">{{ getRuleTypeLabel(childRule) }}</span>
                          <span class="rule-content">{{ getRuleContent(childRule) }}</span>
                        </div>
                        <div class="child-rule-right">
                          <span class="rule-stats">
                            选取 <strong>{{ childRule.selectedCount }}</strong> / {{ childRule.maxQuestions }} 题，
                            每题 <strong>{{ childRule.scorePerQuestion }}</strong> 分，
                            总分 <strong>{{ childRule.totalScore }}</strong> 分
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 没有规则时显示添加按钮 -->
                <div v-else class="tac pd5" style="padding: 10px;">
                  <div>
                    <div class="mb10">点击添加规则设置本试卷的抽题规则</div>
                    <el-button type="primary" @click="handleAddRule">
                      <i class="el-icon-plus"></i>
                      <span>添加规则</span>
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
          
          <div v-else>
            <!-- 固定试卷 -->
            <div class="fixed-paper">
              <el-card class="is-hover-shadow">
                <!-- 操作栏 -->
                <div class="mb10 mt10">
                  <div class="el-button-group">
                    <label class="el-checkbox fl el-checkbox--medium is-bordered" style="padding: 7.5px 20px;">
                      <span class="el-checkbox__input">
                        <span class="el-checkbox__inner"></span>
                        <input type="checkbox" aria-hidden="false" class="el-checkbox__original" value="" v-model="selectAll" @change="handleSelectAll">
                      </span>
                      <span class="el-checkbox__label">全选</span>
                    </label>
                    <el-tooltip content="删除选中题目" placement="top">
                      <el-button type="danger" size="medium" @click="handleDeleteSelected">
                        <i class="el-icon-delete"></i>
                      </el-button>
                    </el-tooltip>
                  </div>

                  <div class="el-button-group">
                    <el-button type="default" size="medium" @click="handleManualSelect">
                      <i class="el-icon-plus"></i>
                      <span>手动选题</span>
                    </el-button>
                    <el-button type="default" size="medium" @click="handleRandomSelect">
                      <i class="el-icon-plus"></i>
                      <span>随机抽题</span>
                    </el-button>
                    <el-button type="default" size="medium" @click="handleSort">
                      <i class="el-icon-sort"></i>
                      <span>排序</span>
                    </el-button>
                    <el-button type="default" size="medium" @click="handleBatchSetScore">
                      <i class="icon_size iconfont icon-batch-add"></i>
                      <span>批量设置分数</span>
                    </el-button>
                    <el-button type="default" size="medium" @click="handleExport">
                      <i class="icon_size iconfont icon-daochu"></i>
                      <span>导出</span>
                    </el-button>
                  </div>

                  <el-button type="default" size="medium" style="vertical-align: middle;" @click="handleToggleExpand">
                    <i class="icon_size iconfont icon-zhankai"></i>
                    <span>{{ isExpanded ? '收起' : '展开' }}</span>
                  </el-button>
                </div>

                <!-- 题目列表区域 -->
                <div v-if="fixedQuestions.length > 0" class="question-list">
                  <!-- 这里将显示已添加的题目列表 -->
                  <div class="question-item" v-for="(question, index) in fixedQuestions" :key="question.id">
                    <!-- 题目头部 -->
                    <div class="question-header">
                      <div class="question-header-left">
                        <el-checkbox v-model="question.selected"></el-checkbox>
                        <span class="question-number">{{ index + 1 }}.</span>
                        <span class="question-content">{{ question.content }}</span>
                      </div>
                      <div class="question-header-right">
                        <!-- 分数设置 -->
                        <el-input-number
                          v-model="question.score"
                          :min="0.5"
                          :step="0.5"
                          size="mini"
                          style="width: 100px;"
                          @change="updateQuestionScore(question)"
                        ></el-input-number>
                        <span style="margin-left: 5px; margin-right: 10px;">分</span>

                        <!-- 展开/收起按钮 -->
                        <el-tooltip :content="question.expanded ? '收起' : '展开'" placement="top">
                          <el-button
                            type="text"
                            size="mini"
                            @click="toggleQuestionExpand(question)"
                            style="margin-right: 5px;"
                          >
                            <i :class="question.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                          </el-button>
                        </el-tooltip>

                        <!-- 删除按钮 -->
                        <el-tooltip content="删除" placement="top">
                          <el-button
                            type="text"
                            size="mini"
                            @click="deleteQuestion(question, index)"
                            style="color: #f56c6c;"
                          >
                            <i class="el-icon-delete"></i>
                          </el-button>
                        </el-tooltip>
                      </div>
                    </div>

                    <!-- 题目详情（展开时显示） -->
                    <div v-if="question.expanded" class="question-details">
                      <div class="question-info">
                        <span class="info-item">题型：{{ getQuestionTypeText(question.type) }}</span>
                        <span class="info-item">难度：{{ getDifficultyText(question.difficulty) }}</span>
                      </div>

                      <!-- 选项显示 -->
                      <div v-if="question.options" class="question-options">
                        <div class="options-title">选项：</div>
                        <div
                          v-for="(option, optIndex) in parseOptions(question.options)"
                          :key="optIndex"
                          class="option-item"
                          :class="{ 'correct-option': option.isCorrect }"
                        >
                          <span class="option-key">{{ option.key }}.</span>
                          <span class="option-content">{{ option.content }}</span>
                          <span v-if="option.isCorrect" class="correct-mark">✓</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="tac pd5" style="padding: 40px 10px;">
                  <div>
                    <div class="mb10">暂无题目，点击上方按钮添加题目到试卷中</div>
                    <el-button type="primary" @click="handleManualSelect">
                      <i class="el-icon-plus"></i>
                      <span>开始添加题目</span>
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧设置面板 -->
    <div class="exam-editor-right" :style="{ width: rightPanelWidth + 'px' }">
      <i 
        :class="rightPanelCollapsed ? 'el-icon-s-fold' : 'el-icon-s-unfold'" 
        class="collapse-button" 
        title="收起/展开"
        @click="toggleRightPanel"
      ></i>
      
      <div v-show="!rightPanelCollapsed">
        <div class="editor-header editor-header--big">
          <span>考试设置</span>
          <i class="el-icon-close close-button" @click="handleBack" title="关闭"></i>
        </div>
        
        <div class="main">
          <el-form class="h100p">
            <el-collapse v-model="activeCollapse" class="main-collapse h100p" accordion>
              <!-- 基础设置 -->
              <el-collapse-item title="基础设置" name="basic" class="editor-collapse-item">
                <template slot="title">
                  <div class="bold collapse-title">
                    <i class="el-icon-setting" style="color: #67c23a;"></i>基础设置
                  </div>
                </template>

                <div class="main-module">
                  <!-- 封面图 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>封面图</b>
                      <div class="input-area">
                        <div style="margin-top: 20px;">
                          <span class="dpib" style="width: 160px; line-height: 1.3; font-size: 12px; word-break: break-all; vertical-align: super; color: #aaa;">
                            仅支持上传.png/.jpeg/.jpg格式的文件，尺寸建议为3:2
                          </span>
                          <div class="g-component-cover-uploader dpib">
                            <div class="avatar-uploader" style="width: 120px; height: 80px;">
                              <el-upload
                                class="avatar-uploader"
                                action="#"
                                :show-file-list="false"
                                :before-upload="beforeUpload"
                                accept=".png, .jpeg, .jpg"
                              >
                                <div class="image_area">
                                  <img v-if="paperForm.coverImg" :src="paperForm.coverImg" class="avatar">
                                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                </div>
                              </el-upload>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 试卷名称 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>试卷名称</b>
                      <div class="input-area">
                        <el-input
                          v-model="paperForm.paperName"
                          placeholder="请输入试卷名称"
                          maxlength="100"
                          show-word-limit
                        ></el-input>
                      </div>
                    </div>
                  </div>

                  <!-- 试卷描述 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>试卷描述</b>
                      <div class="input-area">
                        <el-input
                          v-model="paperForm.paperDesc"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入试卷描述"
                          maxlength="500"
                          show-word-limit
                        ></el-input>
                      </div>
                    </div>
                  </div>

                  <!-- 出题方式 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <div class="setting-title">
                        <b>出题方式</b>
                        <el-tooltip
                          placement="top"
                          effect="light"
                          popper-class="paper-type-tooltip"
                        >
                          <div slot="content">
                            <div style="font-weight: bold; margin-bottom: 8px;">出题方式</div>
                            <div><b>固定试卷：</b>每个考生考试的题目都是相同的，可设置题目和选项随机。</div>
                            <br>
                            <div><b>随机试卷：</b>通过配置随机规则，随机从题库里抽取题目，每个考生考试的题目都不同。</div>
                          </div>
                          <i class="el-icon-question paper-type-question"></i>
                        </el-tooltip>
                      </div>
                      <div class="input-area">
                        <el-radio-group v-model="paperForm.paperType" size="mini">
                          <el-radio-button :label="1">随机试卷</el-radio-button>
                          <el-radio-button :label="0">固定试卷</el-radio-button>
                        </el-radio-group>
                      </div>
                    </div>
                  </div>



                  <!-- 时长 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>时长(按卷限时)</b>
                      <el-popover placement="top" width="240" trigger="hover">
                        <div slot="content">
                          <div class="el-popover__title">时长</div>
                          <b>按卷限时：</b>限制试卷总时长，答题时间超过总时长会立即交卷。<br>
                          <b>按题限时：</b>每一题都限制时长，超时自动提交答案并跳转到下一题，只能按顺序答题，不能跳题，也不会回退。
                        </div>
                      </el-popover>
                      <div class="input-area">
                        <el-input-number v-model="paperForm.duration" :min="0" size="mini" class="input--number"></el-input-number> 分
                        <el-input-number v-model="paperForm.durationSeconds" :min="0" :max="59" size="mini" style="width: 85px;"></el-input-number> 秒
                      </div>
                    </div>
                  </div>

                  <!-- 及格分 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>及格分</b>
                      <div class="input-area">
                        <el-input-number v-model="paperForm.passScore" :min="0" :max="paperForm.totalScore || 100" size="mini" class="input--number"></el-input-number> 分
                      </div>
                    </div>
                  </div>

                  <!-- 考试时间 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>考试时间</b>
                      <el-popover placement="top" width="240" trigger="hover">
                        <div slot="content">
                          <div class="el-popover__title">考试时间</div>
                          开启后需要设置考试的开始和结束时间，只有在指定时间段内才能参加考试
                        </div>
                      </el-popover>
                      <div class="input-area">
                        <el-switch v-model="paperForm.enableTimeLimit" :active-value="1" :inactive-value="0"></el-switch>

                      </div>
                    </div>
                  </div>

                  <!-- 考试时间子项容器 -->
                  <div class="block-expand" v-if="paperForm.enableTimeLimit">
                    <!-- 考试开始时间 -->
                    <div class="setting-block sub-setting">
                      <div class="line block-header">
                        <b>考试开始时间</b>
                        <div class="input-area">
                          <el-date-picker
                            v-model="paperForm.startTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            size="mini"
                          ></el-date-picker>
                        </div>
                      </div>
                    </div>

                    <!-- 考试结束时间 -->
                    <div class="setting-block sub-setting">
                      <div class="line block-header">
                        <b>考试结束时间</b>
                        <div class="input-area">
                          <el-date-picker
                            v-model="paperForm.endTime"
                            type="datetime"
                            placeholder="选择结束时间"
                            format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            size="mini"
                          ></el-date-picker>
                        </div>
                      </div>
                    </div>


                  </div>

                  <!-- 提前交卷 -->
                  <div class="setting-block" v-if="paperForm.enableTimeLimit">
                    <div class="line block-header">
                      <b>提前交卷</b>
                      <div class="input-area">
                        <el-switch
                          v-model="paperForm.allowEarlySubmit"
                          :active-value="1"
                          :inactive-value="0"
                        ></el-switch>
                        <span v-if="paperForm.allowEarlySubmit" style="margin-left: 10px;">
                          <el-input-number
                            v-model="paperForm.earlySubmitTime"
                            :min="1"
                            :max="60"
                            size="mini"
                            style="width: 100px;"
                          ></el-input-number> 分钟
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 是否显示分值 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示分值</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showScore" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>

                  <!-- 是否显示题型 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示题型</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showType" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>

                  <!-- 迟到限制 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <div class="setting-title">
                        <b>迟到限制</b>
                        <el-tooltip
                          placement="top"
                          effect="light"
                          popper-class="late-limit-tooltip"
                        >
                          <div slot="content">
                            <div style="font-weight: bold; margin-bottom: 8px;">迟到限制</div>
                            <div>设置迟到多少分钟后，禁止再进入考试。</div>
                          </div>
                          <i class="el-icon-question paper-type-question"></i>
                        </el-tooltip>
                      </div>
                      <div class="input-area">
                        <el-input-number v-model="paperForm.lateLimit" :min="0" size="mini" class="input--number"></el-input-number> 分钟
                      </div>
                    </div>
                  </div>


                </div>
              </el-collapse-item>

              <!-- 考试中设置 -->
              <el-collapse-item title="考试中" name="during" class="editor-collapse-item">
                <template slot="title">
                  <div class="bold collapse-title">
                    <i class="el-icon-edit-outline" style="color: #409eff;"></i>考试中
                  </div>
                </template>
                
                <div class="main-module">


                  <!-- 全部答完才能交卷 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>全部答完才能交卷</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.requireAllAnswered" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>




                </div>
              </el-collapse-item>

              <!-- 考试后设置 -->
              <el-collapse-item title="考试后" name="after" class="editor-collapse-item">
                <template slot="title">
                  <div class="bold collapse-title">
                    <i class="el-icon-finished" style="color: #e6a23c;"></i>考试后
                  </div>
                </template>
                
                <div class="main-module">
                  <!-- 显示成绩 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示成绩</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showResult" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>

                  <!-- 显示对错 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示对错</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showCorrect" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>

                  <!-- 显示答案 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示答案</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showAnswer" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>

                  <!-- 显示解析 -->
                  <div class="setting-block">
                    <div class="line block-header">
                      <b>显示解析</b>
                      <div class="input-area">
                        <el-switch v-model="paperForm.showAnalysis" :active-value="1" :inactive-value="0"></el-switch>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 随机规则设置对话框 -->
    <el-dialog
      title="随机规则设置"
      :visible.sync="showRuleDialog"
      width="1100px"
      top="15vh"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      :z-index="3000"
    >
      <div class="pd10">
        <el-tabs value="newRule" type="border-card">
          <el-tab-pane label="新版规则" name="newRule">
            <div class="cascade-random-rules">
              <!-- 顶部操作栏 -->
              <div class="topbar clearfix">
                <div class="fl">
                  <el-button type="success" @click="handleAddFirstRule">
                    <i class="el-icon-plus"></i>
                    添加一级规则
                  </el-button>
                  <span class="summary">
                    共选择 <span class="total_score">{{ totalQuestions }}</span> 道题目，
                    总分 <span class="total_score">{{ totalRuleScore }}</span> 分
                  </span>
                </div>
              </div>

              <!-- 步骤指引 -->
              <el-steps :active="0" class="guide-steps-list" align-center>
                <el-step title="确定一级规则">
                  <template slot="description">
                    <div class="step-content">
                      按题库、按题型、按难度、按知识点抽题四选一
                    </div>
                  </template>
                </el-step>
                <el-step title="添加子规则">
                  <template slot="description">
                    <div class="step-content">
                      在每个上级规则基础上继续添加子规则
                    </div>
                  </template>
                </el-step>
                <el-step title="保存抽题规则">
                  <template slot="description">
                    <div class="step-content">
                      各级规则设置完成后即可保存规则开始抽题
                    </div>
                  </template>
                </el-step>
              </el-steps>

              <!-- 规则列表区域 -->
              <div class="rules-content">
                <!-- 已添加的规则列表 -->
                <div v-if="rules.length > 0" class="rules-list">
                  <div
                    v-for="rule in parentRules"
                    :key="rule.id"
                    class="rule-node-cascade"
                  >
                    <!-- 父规则显示 -->
                    <div class="parent-rule">
                      <!-- 左侧：规则信息 -->
                      <div class="rule-left">
                        <div class="rule-type-label">
                          {{ rule.type === 3 ? '题库：' : rule.type === 1 ? '题型：' : '难度：' }}
                        </div>
                        <div class="rule-content">
                          <span v-if="rule.type === 3">
                            {{ rule.selectedBanks.map(bank => bank.bankName).join('、') }}
                          </span>
                          <span v-else-if="rule.type === 1">
                            {{ getQuestionTypeText(rule.selectedQuestionTypes) }}
                          </span>
                        </div>
                      </div>

                      <!-- 右侧：操作区域 -->
                      <div class="rule-right">
                        <!-- 父规则没有子规则时显示完整控件 -->
                        <div v-if="!rule.children || rule.children.length === 0" class="rule-controls">
                          <span class="control-item">
                            选取
                            <el-input-number
                              v-model="rule.selectedCount"
                              :min="1"
                              :max="rule.maxQuestions"
                              size="mini"
                              class="input-number-mini"
                              @change="updateRuleScore(rule)"
                            ></el-input-number>
                          </span>
                          <span class="control-divider">/</span>
                          <span class="control-item">{{ rule.maxQuestions }} 题</span>
                          <span class="control-item">
                            每题
                            <el-input-number
                              v-model="rule.scorePerQuestion"
                              :min="0.5"
                              :step="0.5"
                              size="mini"
                              class="input-number-mini"
                              @change="updateRuleScore(rule)"
                            ></el-input-number>
                            分
                          </span>
                          <span class="control-item total-score">总分 {{ rule.totalScore }} 分</span>
                        </div>

                        <!-- 操作按钮（始终显示） -->
                        <div class="rule-actions">
                          <el-tooltip content="添加子规则" placement="top">
                            <el-button type="text" size="mini" class="action-btn" @click="addSubRule(rule)">
                              <i class="el-icon-plus"></i>
                            </el-button>
                          </el-tooltip>
                          <el-tooltip content="删除" placement="top">
                            <el-button type="text" size="mini" class="action-btn" @click="deleteRule(rule)">
                              <i class="el-icon-delete"></i>
                            </el-button>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>

                    <!-- 子规则列表 -->
                    <div v-if="rule.children && rule.children.length > 0" class="children-rules">
                      <div
                        v-for="childRule in rule.children"
                        :key="childRule.id"
                        class="rule-node-cascade children_component"
                      >
                        <!-- 左侧：子规则信息 -->
                        <div class="rule-left">
                          <div class="rule-type-label">
                            {{ getChildRuleLabel(childRule) }}
                          </div>
                          <div class="rule-content" v-if="childRule.type === 3">
                            <span>
                              {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}
                            </span>
                          </div>

                        </div>

                        <!-- 右侧：子规则操作区域 -->
                        <div class="rule-right">
                          <div class="rule-controls">
                            <span class="control-item">
                              选取
                              <el-input-number
                                v-model="childRule.selectedCount"
                                :min="1"
                                :max="childRule.maxQuestions"
                                size="mini"
                                class="input-number-mini"
                                @change="updateRuleScore(childRule)"
                              ></el-input-number>
                            </span>
                            <span class="control-divider">/</span>
                            <span class="control-item">{{ childRule.maxQuestions }} 题</span>
                            <span class="control-item">
                              每题
                              <el-input-number
                                v-model="childRule.scorePerQuestion"
                                :min="0.5"
                                :step="0.5"
                                size="mini"
                                class="input-number-mini"
                                @change="updateRuleScore(childRule)"
                              ></el-input-number>
                              分
                            </span>
                            <span class="control-item total-score">总分 {{ childRule.totalScore }} 分</span>
                          </div>

                          <div class="rule-actions">
                            <el-tooltip content="删除" placement="top">
                              <el-button type="text" size="mini" class="action-btn" @click="deleteChildRule(rule, childRule)">
                                <i class="el-icon-delete"></i>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="rules.length === 0" class="empty-rules">
                  <p>暂无规则，请点击左上角"添加一级规则"开始设置</p>
                </div>
              </div>

              <!-- 底部按钮 -->
              <div class="bottom-panel">
                <el-button @click="showRuleDialog = false">取 消</el-button>
                <el-button type="primary" @click="handleSaveRules">保存规则</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 手动选题对话框 -->
    <el-dialog
      title="选择题目"
      :visible.sync="showManualSelectDialog"
      width="1200px"
      :close-on-click-modal="false"
      class="checked-question"
      top="15vh"
      :append-to-body="true"
      :z-index="3200"
    >
      <div style="display: flex; height: 550px;">
        <!-- 左侧：题库列表 -->
        <div style="width: 300px; padding-right: 10px;">
          <!-- 搜索区域 -->
          <div style="padding: 5px; height: 42px; display: flex; gap: 10px;">
            <el-cascader
              v-model="manualSelect.selectedCategory"
              :options="categoryOptions"
              :props="cascaderProps"
              placeholder="选择题库分类"
              size="small"
              style="width: 135px;"
              clearable
              @change="searchQuestionBanks"
            ></el-cascader>

            <el-input
              v-model="manualSelect.bankSearchKeyword"
              placeholder="题库名称"
              size="small"
              style="width: 150px;"
            >
              <el-button slot="append" icon="el-icon-search" @click="searchQuestionBanks"></el-button>
            </el-input>
          </div>

          <!-- 题库表格 -->
          <div style="padding: 0px 5px; height: 485px; overflow: auto;">
            <el-table
              :data="manualSelect.questionBanks"
              border
              size="medium"
              height="485"
              @row-click="selectQuestionBank"
              highlight-current-row
            >
              <el-table-column type="index" width="38" align="center"></el-table-column>
              <el-table-column prop="bankName" label="题库名称" align="left" header-align="center"></el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div style="padding-top: 10px; text-align: right;">
            <el-pagination
              @size-change="handleBankSizeChange"
              @current-change="handleBankCurrentChange"
              :current-page="manualSelect.bankPagination.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="manualSelect.bankPagination.pageSize"
              :total="manualSelect.bankPagination.total"
              layout="prev, pager, next"
              background
              small
            ></el-pagination>
          </div>
        </div>

        <!-- 中间：题目列表 -->
        <div style="width: 700px; padding: 0px 5px;">
          <!-- 筛选区域 -->
          <div style="padding: 7px 5px; height: 42px; display: flex; gap: 10px; align-items: center;">
            <el-select
              v-model="manualSelect.questionType"
              placeholder="题型"
              size="small"
              style="width: 110px;"
              @change="searchQuestions"
            >
              <el-option label="全部题型" value=""></el-option>
              <el-option label="单选题" value="1"></el-option>
              <el-option label="多选题" value="2"></el-option>
              <el-option label="判断题" value="3"></el-option>
            </el-select>

            <el-select
              v-model="manualSelect.difficulty"
              placeholder="难度"
              size="small"
              style="width: 110px;"
              @change="searchQuestions"
            >
              <el-option label="全部难度" value=""></el-option>
              <el-option label="低" value="1"></el-option>
              <el-option label="中" value="2"></el-option>
              <el-option label="高" value="3"></el-option>
            </el-select>

            <el-input
              v-model="manualSelect.questionSearchKeyword"
              placeholder="搜索题目"
              size="small"
              style="width: 250px;"
            >
              <template slot="append">
                <el-button @click="searchQuestions" style="border-left: 1px solid #dcdfe6;">搜索</el-button>
                <el-button @click="resetQuestionSearch" style="border-left: 1px solid #dcdfe6;">重置</el-button>
              </template>
            </el-input>
          </div>

          <!-- 题目表格 -->
          <div style="padding: 0px 5px; height: 485px; overflow: auto;">
            <el-table
              :data="manualSelect.questions"
              border
              size="medium"
              height="485"
              @selection-change="handleQuestionSelectionChange"
              :row-class-name="getQuestionRowClassName"
            >
              <el-table-column type="index" width="50" align="center"></el-table-column>
              <el-table-column type="selection" width="40" :selectable="isQuestionSelectable"></el-table-column>
              <el-table-column prop="questionContent" label="题干" min-width="400" header-align="center" class-name="question-content-column">
                <template slot-scope="scope">
                  <div class="question-content-text">
                    {{ scope.row.questionContent }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="questionType" label="题目类型" width="78" align="center">
                <template slot-scope="scope">
                  {{ getQuestionTypeText(scope.row.questionType) }}
                </template>
              </el-table-column>
              <el-table-column prop="difficulty" label="难度" width="50" align="center">
                <template slot-scope="scope">
                  {{ getDifficultyText(scope.row.difficulty) }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div style="padding: 10px; text-align: right;">
            <el-pagination
              @size-change="handleQuestionSizeChange"
              @current-change="handleQuestionCurrentChange"
              :current-page="manualSelect.questionPagination.pageNum"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :page-size="manualSelect.questionPagination.pageSize"
              :total="manualSelect.questionPagination.total"
              layout="sizes, prev, pager, next"
              background
              small
            ></el-pagination>
          </div>
        </div>

        <!-- 右侧：统计信息 -->
        <div style="width: 150px; padding: 0px 5px;">
          <div style="padding: 7px 0px; height: 42px; font-size: 16px;">试卷题型统计</div>
          <div style="border-top: 1px solid #ebeef5; height: 485px; padding: 10px 5px 0px 0px;">
            <!-- 显示已添加到试卷的题目统计 -->
            <div v-for="(count, type) in fixedQuestionStats" :key="type" style="margin-bottom: 10px;">
              <div style="font-size: 14px; color: #606266;">
                {{ getQuestionTypeText(type) }}：{{ count }} 题
              </div>
            </div>

            <!-- 显示当前选择的题目统计 -->
            <div v-if="manualSelect.selectedQuestions.length > 0" style="border-top: 1px solid #ebeef5; padding-top: 10px; margin-top: 10px;">
              <div style="font-size: 13px; color: #909399; margin-bottom: 8px;">本次选择：</div>
              <div v-for="(count, type) in manualSelect.selectedStats" :key="'selected-' + type" style="margin-bottom: 8px;">
                <div style="font-size: 13px; color: #409eff;">
                  {{ getQuestionTypeText(type) }}：{{ count }} 题
                </div>
              </div>
            </div>
          </div>
          <div style="width: 140px; text-align: right; padding-right: 5px;">
            <div style="font-size: 14px; font-weight: bold;">总题数：{{ fixedQuestions.length }} 题</div>
            <div v-if="manualSelect.selectedQuestions.length > 0" style="font-size: 12px; color: #409eff;">
              +{{ manualSelect.selectedQuestions.length }} 题
            </div>
          </div>
        </div>
      </div>

      <div slot="footer">
        <el-button @click="showManualSelectDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmManualSelect">确 认</el-button>
      </div>
    </el-dialog>

    <!-- 添加规则对话框 -->
    <el-dialog
      :title="getDialogTitle()"
      :visible.sync="showAddRuleDialog"
      width="900px"
      top="15vh"
      :close-on-click-modal="false"
      :append-to-body="true"
      :z-index="3100"
    >
      <div style="margin-bottom: -30px;">
        <el-form :model="ruleForm" label-width="140px">
          <!-- 选择规则类型 -->
          <el-form-item v-if="shouldShowRuleTypeSelection()" label="选择规则类型">
            <el-radio-group v-model="ruleForm.ruleType">
              <el-radio
                v-if="shouldShowRuleType(3)"
                :label="3"
                :disabled="shouldDisableRuleType(3)"
              >题库</el-radio>
              <el-radio
                v-if="shouldShowRuleType(1)"
                :label="1"
                :disabled="shouldDisableRuleType(1)"
              >题型</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 规则生成方式 -->
          <el-form-item v-if="currentOperation !== 'addSub'">
            <template slot="label">
              <div>
                <el-tooltip content="若选择&quot;分开设置&quot;，则下方的每一项选择都将作为一条独立的随机规则。" placement="top">
                  <i class="el-icon-info" style="cursor: pointer;"></i>
                </el-tooltip>
                规则生成方式
              </div>
            </template>
            <el-radio-group v-model="ruleForm.generateType">
              <el-radio label="divided">分开设置</el-radio>
              <el-radio label="one">整体设置</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 已选择的题库 -->
          <el-form-item v-if="ruleForm.ruleType === 3">
            <template slot="label">
              <div>
                <el-tooltip content="点击选项可取消选择。" placement="top">
                  <i class="el-icon-info" style="cursor: pointer;"></i>
                </el-tooltip>
                已选择的题库
              </div>
            </template>
            <span v-if="ruleForm.selectedItems.length === 0" class="ml20">
              暂无选择，请至少选择一项。
            </span>
            <div v-else>
              <el-tag
                v-for="item in selectedBanks"
                :key="item.bankId"
                closable
                @close="removeSelectedBank(item.bankId)"
                type="info"
                size="medium"
                class="selected-bank-tag"
              >
                {{ item.bankName }}
              </el-tag>
            </div>
          </el-form-item>

          <!-- 选择题型 -->
          <el-form-item v-if="ruleForm.ruleType === 1" label="选择题型">
            <el-checkbox-group v-model="ruleForm.selectedQuestionTypes">
              <el-checkbox v-if="shouldShowQuestionType('1')" label="1">单选题</el-checkbox>
              <el-checkbox v-if="shouldShowQuestionType('2')" label="2">多选题</el-checkbox>
              <el-checkbox v-if="shouldShowQuestionType('3')" label="3">判断题</el-checkbox>
            </el-checkbox-group>
          </el-form-item>



          <!-- 选择题库 -->
          <el-form-item v-if="ruleForm.ruleType === 3" label="选择题库">
            <!-- 搜索区域 -->
            <div class="clearfix" style="margin-bottom: 6px;">
              <div style="float: right; display: flex; align-items: center;">
                <el-input
                  v-model="searchKeyword"
                  placeholder="题库名称"
                  size="small"
                  style="width: 200px; margin-right: 8px;"
                  @keyup.enter.native="handleSearch"
                  clearable
                >
                  <el-button slot="append" @click="handleSearch">搜索</el-button>
                </el-input>
                <el-button size="small" @click="handleReset">重置</el-button>
              </div>
            </div>

            <!-- 题库表格 -->
            <el-table
              ref="questionBankTable"
              :data="questionBanks"
              border
              size="small"
              max-height="300"
              v-loading="questionBankLoading"
              @selection-change="handleSelectionChange"
              :row-class-name="getRowClassName"
            >
              <el-table-column type="index" width="40" label="#" align="center"></el-table-column>
              <el-table-column type="selection" width="50" align="center" :selectable="isRowSelectable"></el-table-column>
              <el-table-column prop="bankName" label="题库名称" align="center"></el-table-column>
              <el-table-column prop="questionCount" label="题目数量" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.questionCount || 0 }}
                </template>
              </el-table-column>
              <el-table-column label="分类" width="150" align="center">
                <template slot-scope="scope">
                  {{ getCategoryName(scope.row.categoryId) }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="text-align: right; margin-top: 10px;">
              <el-pagination
                @current-change="handleCurrentChange"
                :current-page="queryParams.pageNum"
                :page-size="queryParams.pageSize"
                layout="total, prev, pager, next"
                :total="total"
                small
                background
              >
              </el-pagination>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer">
        <el-button @click="showAddRuleDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveRule">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 表格垂直对齐优化 */
::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell {
  vertical-align: middle;
}

/* 多选框垂直居中 */
::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .el-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 序号列垂直居中 */
::v-deep .el-table .el-table__body-wrapper .el-table__body .el-table__row .el-table__cell .cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

/* 规则节点样式 */
.rule-node-cascade {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
}

.rule-node-cascade:hover {
  border: 1px dashed #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

/* 左侧题库信息 */
.rule-left {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-type-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  min-width: 50px;
}

.rule-content {
  font-size: 14px;
  color: #666;
}

/* 右侧操作区域 */
.rule-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.rule-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.control-divider {
  color: #999;
  margin: 0 4px;
}

.total-score {
  font-weight: 600;
  color: #333;
}

.input-number-mini {
  width: 110px !important;
}

.rule-actions {
  display: flex;
  gap: 2px;
}

.action-btn {
  color: #409eff !important;
  padding: 4px 6px !important;
  margin-left: 0 !important;
}

.action-btn:hover {
  background-color: #ecf5ff !important;
}

.empty-rules {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

/* 子规则样式 */
.children-rules {
  margin-top: 12px;
  margin-left: 20px;
  border-left: 2px dashed #e8e8e8;
  padding-left: 20px;
}

.children_component {
  margin-bottom: 8px !important;
  border: 1px dashed #d9d9d9 !important;
  background-color: #f9f9f9 !important;
}

.children_component:hover {
  border: 1px dashed #409eff !important;
  background-color: #ecf5ff !important;
}

.parent-rule {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 禁用的表格行样式 */
::v-deep .disabled-row {
  background-color: #f5f5f5 !important;
  color: #c0c4cc !important;
}

::v-deep .disabled-row:hover {
  background-color: #f5f5f5 !important;
}

::v-deep .disabled-row td {
  color: #c0c4cc !important;
}

/* random-paper 规则显示样式 */
.rule-display-container {
  border: 1px dashed #d0d0d0;
  border-radius: 6px;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #fafbfc;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 题库容器悬停效果 */
.rule-display-container:hover {
  border-color: #409eff;
  background-color: #f8fbff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.rule-display-container:hover .parent-rule-display {
  background-color: #e3f2fd;
  border-left-color: #1976d2;
}

.rule-display-container:hover .parent-rule-display .rule-label {
  color: #1976d2;
}

.parent-rule-display {
  font-size: 14px;
  color: #303133;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.children-rules-display {
  margin-left: 20px;
}

.child-rule-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 8px;
  background-color: #ffffff;
  border: 1px dashed #d0d0d0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.child-rule-display:last-child {
  margin-bottom: 0;
}

/* 子规则悬停效果 */
.child-rule-display:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
}

.child-rule-display:hover .rule-label {
  color: #1976d2;
}

.child-rule-display:hover .rule-stats {
  background-color: #e3f2fd;
  border-color: #90caf9;
}

.child-rule-left {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.child-rule-right {
  flex-shrink: 0;
  margin-left: 20px;
}

.rule-label {
  font-weight: bold;
  color: #409eff;
  margin-right: 8px;
}

.rule-content {
  color: #303133;
}

.rule-stats {
  font-size: 13px;
  color: #606266;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 5px;
  border: 1px solid #b3d8ff;
  white-space: nowrap;
}

.rule-stats strong {
  color: #409eff;
  font-weight: 600;
}

/* 固定试卷样式 */
.question-list {
  margin-top: 20px;
}

.question-item {
  margin-bottom: 12px;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.question-item:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
}

.question-header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.question-header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.question-number {
  font-weight: bold;
  color: #409eff;
  margin-left: 10px;
  margin-right: 10px;
  min-width: 30px;
}

.question-content {
  flex: 1;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
  margin-right: 15px;
}

.question-details {
  border-top: 1px solid #e4e7ed;
  padding: 15px;
  background-color: #ffffff;
}

.question-info {
  margin-bottom: 15px;
}

.info-item {
  display: inline-block;
  margin-right: 20px;
  font-size: 13px;
  color: #606266;
}

.question-options {
  margin-top: 10px;
}

.options-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #606266;
}

.option-item.correct-option {
  color: #67c23a;
  font-weight: 500;
}

.option-key {
  font-weight: bold;
  margin-right: 8px;
  min-width: 20px;
}

.option-content {
  flex: 1;
}

.correct-mark {
  color: #67c23a;
  font-weight: bold;
  margin-left: 8px;
}

.el-button-group {
  display: inline-block;
  margin-right: 10px;
}

.icon_size {
  font-size: 14px;
}

/* 禁用题目行样式 */
.disabled-question-row {
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
}

.disabled-question-row:hover {
  background-color: #f5f7fa !important;
}

.disabled-question-row td {
  color: #c0c4cc !important;
}

/* 题干内容左对齐样式 */
.question-content-text {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left !important;
  line-height: 1.4;
  word-break: break-word;
}

/* 强制题干列内容左对齐 */
.el-table .question-content-column {
  text-align: left !important;
}

.el-table .question-content-column .cell {
  text-align: left !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

/* 已选择题库标签样式 */
.selected-bank-tag {
  margin-right: 8px !important;
  margin-bottom: 4px !important;
  font-size: 13px !important;
}

/* 修复标签关闭按钮样式 */
::v-deep .selected-bank-tag .el-tag__close {
  color: #909399 !important;
  font-size: 12px !important;
  margin-left: 6px !important;
  cursor: pointer !important;
  border-radius: 50% !important;
  width: 16px !important;
  height: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

::v-deep .selected-bank-tag .el-tag__close:hover {
  background-color: #909399 !important;
  color: #fff !important;
}
</style>

<script>
import { listQuestionBank } from "@/api/biz/questionBank"
import { listCategory } from "@/api/biz/category"
import { getQuestionStatistics, listQuestion } from "@/api/biz/question"

export default {
  name: "PaperCreate",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paperId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      // 右侧面板状态
      rightPanelCollapsed: false,
      rightPanelWidth: 410,
      
      // 折叠面板激活项（accordion模式下为字符串）
      activeCollapse: 'basic',
      
      // 试卷表单数据
      paperForm: {
        paperId: null,
        paperName: '',
        paperDesc: '',
        paperType: 1, // 0: 固定试卷, 1: 随机试卷
        coverImg: '',
        totalScore: 100,
        passScore: 60,
        startTime: null,
        endTime: null,
        duration: 90, // 考试时长，分钟
        lateLimit: 0, // 迟到限制，分钟
        allowEarlySubmit: 0, // 是否允许提前交卷
        earlySubmitTime: 40, // 提前交卷时间，分钟
        showScore: 0, // 是否显示分值
        showType: 0, // 是否显示题型
        requireAllAnswered: 0, // 是否要求全部答完才能交卷
        showResult: 0, // 是否显示成绩
        showCorrect: 0, // 是否显示对错
        showAnswer: 0, // 是否显示答案
        showAnalysis: 0, // 是否显示解析
        status: 0, // 状态：0未发布 1已发布
        enableTimeLimit: 0, // 是否启用考试时间限制
        durationSeconds: 0, // 时长秒数
        createTime: null
      },
      
      // 统计数据（注：题目数量和总分现在通过计算属性totalQuestions和totalRuleScore动态计算）

      // 固定试卷相关数据
      fixedQuestions: [], // 固定试卷的题目列表
      selectAll: false, // 全选状态
      isExpanded: false, // 展开状态

      // 手动选题对话框
      showManualSelectDialog: false,
      categoryOptions: [], // 分类选项数据
      cascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: false,
        emitPath: false
      },
      manualSelect: {
        selectedCategory: '', // 选择的题库目录
        bankSearchKeyword: '', // 题库搜索关键词
        questionBanks: [], // 题库列表
        bankPagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        selectedBankId: null, // 选择的题库ID
        questionType: '', // 题型筛选
        difficulty: '', // 难度筛选
        questionSearchKeyword: '', // 题目搜索关键词
        questions: [], // 题目列表
        questionPagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        selectedQuestions: [], // 选中的题目
        selectedStats: {} // 选中题目的统计信息
      },

      // 随机规则对话框
      showRuleDialog: false,
      // 规则列表
      rules: [],

      // 添加规则对话框
      showAddRuleDialog: false,
      ruleForm: {
        ruleType: 3, // 1:题型 3:题库
        generateType: 'divided', // one:整体设置 divided:分开设置
        selectedItems: [],
        selectedQuestionTypes: [] // 选择的题型
      },
      // 当前操作状态
      currentOperation: 'add', // add: 添加, edit: 编辑, addSub: 添加子规则
      editingRule: null, // 正在编辑的规则
      parentRule: null, // 父规则（添加子规则时使用）
      currentQuestionTypeCount: 0, // 当前选择题型的可用题目数量
      questionBanks: [],
      questionBankLoading: false,
      categoryOptions: [],
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bankName: undefined
      }
    }
  },
  computed: {
    // 已选择的题库
    selectedBanks() {
      return this.questionBanks.filter(bank =>
        this.ruleForm.selectedItems.includes(bank.bankId)
      )
    },

    // 计算总题目数量
    totalQuestions() {
      let total = 0
      this.rules.forEach(rule => {
        if (rule.children && rule.children.length > 0) {
          // 如果有子规则，计算子规则的题目数
          rule.children.forEach(child => {
            total += child.selectedCount || 0
          })
        } else {
          // 如果没有子规则，计算父规则的题目数
          total += rule.selectedCount || 0
        }
      })
      return total
    },

    // 计算总分数
    totalRuleScore() {
      let total = 0
      this.rules.forEach(rule => {
        if (rule.children && rule.children.length > 0) {
          // 如果有子规则，计算子规则的总分
          rule.children.forEach(child => {
            total += child.totalScore || 0
          })
        } else {
          // 如果没有子规则，计算父规则的总分
          total += rule.totalScore || 0
        }
      })
      return total
    },

    // 获取父规则列表（只包含没有parentId的规则）
    parentRules() {
      return this.rules.filter(rule => !rule.parentId)
    },

    // 获取已使用的题库ID列表
    usedBankIds() {
      const usedIds = new Set()
      this.rules.forEach(rule => {
        if (rule.type === 3 && rule.selectedItems) {
          rule.selectedItems.forEach(bankId => {
            usedIds.add(bankId)
          })
        }
      })
      return Array.from(usedIds)
    },

    // 计算已添加到试卷的题目统计
    fixedQuestionStats() {
      const stats = {}
      this.fixedQuestions.forEach(question => {
        const type = question.type
        stats[type] = (stats[type] || 0) + 1
      })
      return stats
    }
  },
  watch: {
    visible(val) {
      if (val && this.paperId) {
        this.loadPaperData()
      }
    },

    // 监听题型选择变化
    'ruleForm.selectedQuestionTypes': {
      async handler(newVal) {
        if (this.ruleForm.ruleType === 1 && newVal && newVal.length > 0) {
          await this.updateQuestionTypeCount()
        }
      },
      deep: true
    }
  },
  methods: {
    /** 返回试卷列表 */
    handleBack() {
      this.$emit('close')
    },
    
    /** 切换右侧面板 */
    toggleRightPanel() {
      this.rightPanelCollapsed = !this.rightPanelCollapsed
      this.rightPanelWidth = this.rightPanelCollapsed ? 50 : 400
    },
    
    /** 考试入口 */
    handleExamEntry() {
      this.$message.info('考试入口功能开发中...')
    },
    
    /** 设置考试页面 */
    handlePageSetting() {
      this.$message.info('设置考试页面功能开发中...')
    },
    
    /** 发布试卷 */
    handlePublish() {
      this.$message.info('发布试卷功能开发中...')
    },
    
    /** 邀请考生 */
    handleInviteStudents() {
      this.$message.info('邀请考生功能开发中...')
    },
    
    /** 已邀请列表 */
    handleInviteList() {
      this.$message.info('已邀请列表功能开发中...')
    },
    

    
    /** 添加规则（随机试卷） */
    handleAddRule() {
      this.showRuleDialog = true
    },

    /** 编辑规则（打开规则编辑界面） */
    handleEditRules() {
      this.showRuleDialog = true
    },

    /** 添加一级规则 */
    handleAddFirstRule() {
      this.currentOperation = 'add'
      this.editingRule = null

      // 如果已有规则，限制只能选择相同类型
      if (this.rules.length > 0) {
        this.ruleForm.ruleType = this.rules[0].type
      } else {
        this.ruleForm.ruleType = 3 // 默认题库
      }

      this.showAddRuleDialog = true
      // 重置查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        bankName: undefined
      }
      this.searchKeyword = ''
      this.loadQuestionBanks()
    },

    /** 加载题库列表 */
    loadQuestionBanks() {
      this.questionBankLoading = true
      // 同时加载题库和分类数据
      Promise.all([
        listQuestionBank(this.queryParams),
        listCategory({ pageSize: 1000 })
      ]).then(([bankResponse, categoryResponse]) => {
        const questionBanks = bankResponse.rows || []
        this.total = bankResponse.total || 0
        this.categoryOptions = categoryResponse.rows || categoryResponse.data || []

        // 为每个题库获取题目统计
        const statisticsPromises = questionBanks.map(bank =>
          getQuestionStatistics(bank.bankId).then(stats => {
            bank.questionCount = stats.data ? (stats.data.totalCount || stats.data.total || 0) : 0
            return bank
          }).catch(() => {
            bank.questionCount = 0
            return bank
          })
        )

        Promise.all(statisticsPromises).then(banksWithStats => {
          this.questionBanks = banksWithStats
          this.questionBankLoading = false
        })
      }).catch(() => {
        this.questionBankLoading = false
      })
    },

    /** 保存规则 */
    handleSaveRules() {
      this.$message.info('保存规则功能开发中...')
      this.showRuleDialog = false
    },

    /** 保存单个规则 */
    async handleSaveRule() {
      if (this.ruleForm.ruleType === 3 && this.ruleForm.selectedItems.length === 0) {
        this.$message.warning('请至少选择一个题库')
        return
      }
      if (this.ruleForm.ruleType === 1 && this.ruleForm.selectedQuestionTypes.length === 0) {
        this.$message.warning('请至少选择一个题型')
        return
      }

      // 题型规则验证：检查是否有可用题目
      if (this.ruleForm.ruleType === 1) {
        if (this.currentQuestionTypeCount === 0) {
          this.$message.warning('所选题型在当前题库中没有可用题目，无法保存')
          return
        }
      }

      if (this.currentOperation === 'edit') {
        // 编辑现有规则
        this.updateExistingRule()
      } else {
        // 添加新规则（包括添加子规则）
        this.addNewRule()
      }

      this.$message.success(this.currentOperation === 'edit' ? '规则更新成功' : '规则保存成功')
      this.showAddRuleDialog = false

      // 重置表单
      this.resetRuleForm()
    },

    /** 更新现有规则 */
    updateExistingRule() {
      const rule = this.editingRule
      rule.type = this.ruleForm.ruleType
      rule.generateType = this.ruleForm.generateType

      if (this.ruleForm.ruleType === 3) {
        // 题库规则
        rule.selectedItems = [...this.ruleForm.selectedItems]
        rule.selectedBanks = this.selectedBanks.map(bank => ({
          bankId: bank.bankId,
          bankName: bank.bankName,
          questionCount: bank.questionCount
        }))
        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)

        // 重新计算总分，确保选取数量不超过最大题目数
        if (rule.selectedCount > rule.maxQuestions) {
          rule.selectedCount = rule.maxQuestions
        }
        rule.totalScore = rule.selectedCount * rule.scorePerQuestion
      } else if (this.ruleForm.ruleType === 1) {
        // 题型规则
        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]
        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量
      }
    },

    /** 添加新规则 */
    addNewRule() {
      // 如果是添加子规则，按原逻辑处理
      if (this.currentOperation === 'addSub' && this.parentRule) {
        this.addSingleRule()
        return
      }

      // 如果是题库规则且选择了分开设置，为每个题库创建独立规则
      if (this.ruleForm.ruleType === 3 && this.ruleForm.generateType === 'divided' && this.selectedBanks.length > 1) {
        this.selectedBanks.forEach((bank, index) => {
          const rule = {
            id: Date.now() + index, // 确保每个规则有唯一ID
            type: this.ruleForm.ruleType,
            generateType: this.ruleForm.generateType,
            selectedCount: 1,
            scorePerQuestion: 0.5,
            totalScore: 0.5,
            selectedItems: [bank.bankId],
            selectedBanks: [{
              bankId: bank.bankId,
              bankName: bank.bankName,
              questionCount: bank.questionCount
            }],
            maxQuestions: bank.questionCount || 0
          }
          this.updateRuleScore(rule)
          this.rules.push(rule)
        })
      } else {
        // 整体设置或其他情况，创建单个规则
        this.addSingleRule()
      }
    },

    /** 添加单个规则 */
    addSingleRule() {
      const rule = {
        id: Date.now(), // 临时ID
        type: this.ruleForm.ruleType,
        generateType: this.ruleForm.generateType,
        selectedCount: 1, // 默认选取1题
        scorePerQuestion: 0.5, // 默认每题0.5分
        totalScore: 0.5, // 默认总分0.5分
        maxQuestions: 0 // 最大题目数
      }

      // 如果是添加子规则
      if (this.currentOperation === 'addSub' && this.parentRule) {
        rule.parentId = this.parentRule.id

        // 确保父规则有children数组
        if (!this.parentRule.children) {
          this.$set(this.parentRule, 'children', [])
        }

        // 添加到父规则的children中
        this.parentRule.children.push(rule)
      }

      if (this.ruleForm.ruleType === 3) {
        // 题库规则
        rule.selectedItems = [...this.ruleForm.selectedItems]
        rule.selectedBanks = this.selectedBanks.map(bank => ({
          bankId: bank.bankId,
          bankName: bank.bankName,
          questionCount: bank.questionCount
        }))
        rule.maxQuestions = rule.selectedBanks.reduce((sum, bank) => sum + (bank.questionCount || 0), 0)
      } else if (this.ruleForm.ruleType === 1) {
        // 题型规则
        rule.selectedQuestionTypes = [...this.ruleForm.selectedQuestionTypes]
        rule.maxQuestions = this.currentQuestionTypeCount // 使用实际查询到的题目数量
      }

      // 只有父规则才添加到主规则列表
      if (this.currentOperation !== 'addSub') {
        this.rules.push(rule)
      }

      // 更新规则分数
      this.updateRuleScore(rule)
    },

    /** 重置规则表单 */
    resetRuleForm() {
      this.ruleForm = {
        ruleType: 3,
        generateType: 'divided',
        selectedItems: [],
        selectedQuestionTypes: []
      }
      this.currentOperation = 'add'
      this.editingRule = null
      this.parentRule = null
      this.currentQuestionTypeCount = 0
    },

    /** 搜索题库 */
    handleSearch() {
      this.queryParams.pageNum = 1
      this.queryParams.bankName = this.searchKeyword || undefined
      this.loadQuestionBanks()
    },

    /** 重置搜索 */
    handleReset() {
      this.searchKeyword = ''
      this.queryParams.pageNum = 1
      this.queryParams.bankName = undefined
      this.loadQuestionBanks()
    },

    /** 表格选择变化 */
    handleSelectionChange(selection) {
      this.ruleForm.selectedItems = selection.map(item => item.bankId)
    },

    /** 移除已选择的题库 */
    removeSelectedBank(bankId) {
      const index = this.ruleForm.selectedItems.indexOf(bankId)
      if (index > -1) {
        this.ruleForm.selectedItems.splice(index, 1)
      }

      // 同步取消表格中的勾选状态
      this.$nextTick(() => {
        const table = this.$refs.questionBankTable
        if (table) {
          // 找到对应的行数据
          const rowToDeselect = this.questionBanks.find(bank => bank.bankId === bankId)
          if (rowToDeselect) {
            table.toggleRowSelection(rowToDeselect, false)
          }
        }
      })
    },

    /** 分页变化 */
    handleCurrentChange(page) {
      this.queryParams.pageNum = page
      this.loadQuestionBanks()
    },

    /** 根据分类ID获取分类名称 */
    getCategoryName(categoryId) {
      const category = this.findCategoryById(this.categoryOptions, categoryId)
      return category ? category.name : '未分类'
    },

    /** 在分类数据中查找分类（支持扁平和树形结构） */
    findCategoryById(categories, id) {
      // 创建扁平化的分类列表
      const flatCategories = this.flattenCategories(categories)

      // 在扁平化列表中查找
      const category = flatCategories.find(cat => cat.id === id)
      return category || null
    },

    /** 将树形分类数据扁平化 */
    flattenCategories(categories) {
      let result = []

      function flatten(cats) {
        for (const cat of cats) {
          result.push(cat)
          if (cat.children && cat.children.length > 0) {
            flatten(cat.children)
          }
        }
      }

      flatten(categories)
      return result
    },

    /** 更新规则分数 */
    updateRuleScore(rule) {
      rule.totalScore = rule.selectedCount * rule.scorePerQuestion
    },

    /** 获取题型文本 */
    getQuestionTypeText(questionTypes) {
      const questionTypeMap = {
        '1': '单选题', '2': '多选题', '3': '判断题',
        1: '单选题', 2: '多选题', 3: '判断题'
      }

      // 如果是数组，处理多个题型
      if (Array.isArray(questionTypes)) {
        return questionTypes.map(t => questionTypeMap[t]).join('、')
      }

      // 如果是单个值，直接返回对应文本
      return questionTypeMap[questionTypes] || '未知'
    },



    /** 获取子规则标签 */
    getChildRuleLabel(childRule) {
      if (childRule.type === 3) {
        return '题库：'
      } else if (childRule.type === 1) {
        // 题型子规则只显示具体的题型名称，不显示"题型："前缀
        if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {
          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }
          return questionTypeMap[childRule.selectedQuestionTypes[0]]
        } else {
          return '题型'
        }
      }
      return '规则：'
    },

    /** 获取规则类型标签（用于random-paper显示） */
    getRuleTypeLabel(rule) {
      if (rule.type === 3) {
        return '题库：'
      } else if (rule.type === 1) {
        return '题型：'
      }
      return '规则：'
    },

    /** 获取规则内容（用于random-paper显示） */
    getRuleContent(rule) {
      if (rule.type === 3) {
        // 题库规则显示题库名称
        if (rule.selectedBanks && rule.selectedBanks.length > 0) {
          return rule.selectedBanks.map(bank => bank.bankName).join('、')
        }
        return '未选择题库'
      } else if (rule.type === 1) {
        // 题型规则显示题型名称
        if (rule.selectedQuestionTypes && rule.selectedQuestionTypes.length > 0) {
          const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }
          return rule.selectedQuestionTypes.map(type => questionTypeMap[type]).join('、')
        }
        return '未选择题型'
      }
      return '未配置'
    },

    /** 添加子规则 */
    addSubRule(rule) {
      this.currentOperation = 'addSub'
      this.parentRule = rule

      // 根据父规则类型确定子规则类型
      let defaultRuleType

      if (rule.type === 3) {
        // 题库规则的子规则只能是题型规则
        defaultRuleType = 1
      } else {
        // 其他情况的默认处理（虽然目前只支持题库作为一级规则）
        defaultRuleType = 1
      }

      this.ruleForm.ruleType = defaultRuleType
      this.ruleForm.generateType = 'divided'
      this.ruleForm.selectedItems = []
      this.ruleForm.selectedQuestionTypes = []

      this.showAddRuleDialog = true
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        bankName: undefined
      }
      this.searchKeyword = ''
      this.loadQuestionBanks()
    },

    /** 编辑规则 */
    editRule(rule) {
      this.currentOperation = 'edit'
      this.editingRule = rule
      this.ruleForm.ruleType = rule.type
      this.ruleForm.generateType = rule.generateType

      if (rule.type === 3) {
        // 题库规则
        this.ruleForm.selectedItems = [...rule.selectedItems]
      } else if (rule.type === 1) {
        // 题型规则
        this.ruleForm.selectedQuestionTypes = [...rule.selectedQuestionTypes]
        // 编辑题型规则时，更新题目数量
        this.$nextTick(() => {
          this.updateQuestionTypeCount()
        })
      }

      this.showAddRuleDialog = true
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        bankName: undefined
      }
      this.searchKeyword = ''
      this.loadQuestionBanks()
    },

    /** 删除规则 */
    deleteRule(rule) {
      this.$confirm('确定要删除这条规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.rules.findIndex(r => r.id === rule.id)
        if (index > -1) {
          this.rules.splice(index, 1)
          this.$message.success('规则删除成功')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    /** 删除子规则 */
    deleteChildRule(parentRule, childRule) {
      this.$confirm('确定要删除这条子规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = parentRule.children.findIndex(child => child.id === childRule.id)
        if (index > -1) {
          parentRule.children.splice(index, 1)
          this.$message.success('子规则删除成功')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    /** 获取对话框标题 */
    getDialogTitle() {
      switch (this.currentOperation) {
        case 'add':
          return '添加规则'
        case 'edit':
          return '编辑规则'
        case 'addSub':
          return '添加子规则'
        default:
          return '添加规则'
      }
    },

    /** 是否显示规则类型选择 */
    shouldShowRuleTypeSelection() {
      // 所有操作都显示规则类型选择
      return true
    },

    /** 是否显示规则类型选项 */
    shouldShowRuleType(ruleType) {
      // 编辑时只显示当前规则的类型
      if (this.currentOperation === 'edit') {
        return this.editingRule.type === ruleType
      }

      // 添加子规则时的逻辑
      if (this.currentOperation === 'addSub' && this.parentRule) {
        // 题型规则的子规则只能是题库规则
        if (this.parentRule.type === 1) {
          return ruleType === 3
        }
        // 题库规则的子规则只能是题型规则
        if (this.parentRule.type === 3) {
          return ruleType === 1
        }
        // 其他情况不能选择父规则的类型
        return ruleType !== this.parentRule.type
      }

      // 添加一级规则时的逻辑
      if (this.currentOperation === 'add') {
        // 一级规则只能选择题库
        return ruleType === 3
      }

      // 其他情况显示所有类型
      return true
    },

    /** 是否显示特定题型选项 */
    shouldShowQuestionType(questionType) {
      // 如果不是添加子规则，显示所有题型
      if (this.currentOperation !== 'addSub' || !this.parentRule) {
        return true
      }

      // 获取父规则下已有的题型子规则中选择的题型
      const existingQuestionTypes = []
      if (this.parentRule.children) {
        this.parentRule.children.forEach(child => {
          if (child.type === 1 && child.selectedQuestionTypes) {
            existingQuestionTypes.push(...child.selectedQuestionTypes)
          }
        })
      }

      // 如果该题型已经被选择过，则隐藏
      return !existingQuestionTypes.includes(questionType)
    },

    /** 判断表格行是否可选择 */
    isRowSelectable(row) {
      // 如果是编辑操作，允许选择
      if (this.currentOperation === 'edit') {
        return true
      }

      // 如果是添加子规则，允许选择（子规则不涉及题库选择）
      if (this.currentOperation === 'addSub') {
        return true
      }

      // 如果是添加一级规则，检查题库是否已被使用
      return !this.usedBankIds.includes(row.bankId)
    },

    /** 获取表格行的样式类名 */
    getRowClassName({ row }) {
      // 如果题库已被使用且不是编辑操作，添加禁用样式
      if (this.currentOperation !== 'edit' && this.currentOperation !== 'addSub' && this.usedBankIds.includes(row.bankId)) {
        return 'disabled-row'
      }
      return ''
    },

    /** 更新题型题目数量 */
    async updateQuestionTypeCount() {
      if (this.ruleForm.ruleType !== 1 || !this.ruleForm.selectedQuestionTypes.length) {
        this.currentQuestionTypeCount = 0
        return
      }

      // 获取题库信息
      let selectedBanks = []
      if (this.currentOperation === 'addSub' && this.parentRule && this.parentRule.type === 3) {
        // 添加子规则时，使用父规则的题库
        selectedBanks = this.parentRule.selectedBanks || []
      } else {
        // 其他情况使用当前选择的题库
        selectedBanks = this.selectedBanks
      }

      if (!selectedBanks.length) {
        this.currentQuestionTypeCount = 0
        this.$message.warning('请先选择题库')
        return
      }

      try {
        const count = await this.getQuestionCountByType(selectedBanks, this.ruleForm.selectedQuestionTypes)
        this.currentQuestionTypeCount = count

        if (count === 0) {
          this.$message.warning('所选题型在当前题库中没有可用题目')
        }
      } catch (error) {
        console.error('查询题目数量失败:', error)
        this.currentQuestionTypeCount = 0
        this.$message.error('查询题目数量失败')
      }
    },

    /** 根据题库和题型查询题目数量 */
    async getQuestionCountByType(banks, questionTypes) {
      if (!banks.length || !questionTypes.length) {
        return 0
      }

      let totalCount = 0

      // 遍历每个题库，查询题目统计
      for (const bank of banks) {
        try {
          // 使用已导入的API方法
          const response = await getQuestionStatistics(bank.bankId)
          console.log(`题库${bank.bankId}统计数据:`, response)

          if (response.code === 200 && response.data) {
            const statistics = response.data

            // 根据选择的题型累加数量
            questionTypes.forEach(type => {
              switch (type) {
                case '1': // 单选题
                  totalCount += statistics.singleChoice || 0
                  break
                case '2': // 多选题
                  totalCount += statistics.multipleChoice || 0
                  break
                case '3': // 判断题
                  totalCount += statistics.judgment || 0
                  break
              }
            })
          }
        } catch (error) {
          console.error(`查询题库${bank.bankId}统计信息失败:`, error)
        }
      }

      console.log(`总题目数量: ${totalCount}`)
      return totalCount
    },

    /** 是否禁用规则类型 */
    shouldDisableRuleType(ruleType) {
      // 编辑时不能更改类型
      if (this.currentOperation === 'edit') {
        return this.editingRule.type !== ruleType
      }

      // 添加一级规则时，只能选择题库，禁用其他类型
      if (this.currentOperation === 'add') {
        return ruleType !== 3
      }

      return false
    },
    
    /** 全选/取消全选 */
    handleSelectAll() {
      this.fixedQuestions.forEach(question => {
        question.selected = this.selectAll
      })
    },

    /** 删除选中题目 */
    handleDeleteSelected() {
      const selectedQuestions = this.fixedQuestions.filter(q => q.selected)
      if (selectedQuestions.length === 0) {
        this.$message.warning('请先选择要删除的题目')
        return
      }

      this.$confirm(`确定删除选中的 ${selectedQuestions.length} 道题目吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fixedQuestions = this.fixedQuestions.filter(q => !q.selected)
        this.selectAll = false
        this.$message.success('删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    /** 手动选题 */
    handleManualSelect() {
      this.showManualSelectDialog = true
      this.initManualSelectData()
    },

    /** 初始化手动选题数据 */
    initManualSelectData() {
      // 重置数据
      this.manualSelect.selectedCategory = ''
      this.manualSelect.bankSearchKeyword = ''
      this.manualSelect.questionType = ''
      this.manualSelect.difficulty = ''
      this.manualSelect.questionSearchKeyword = ''
      this.manualSelect.selectedQuestions = []
      this.manualSelect.selectedStats = {}

      // 加载分类数据
      this.loadCategoryTree()

      // 加载题库列表
      this.loadManualSelectQuestionBanks()
    },

    /** 加载分类树数据 */
    loadCategoryTree() {
      listCategory({ pageSize: 1000 }).then(response => {
        const categories = response.rows || []
        this.categoryOptions = this.buildCategoryTree(categories)
      }).catch(error => {
        console.error('加载分类数据失败:', error)
        this.categoryOptions = []
      })
    },

    /** 构建分类树 */
    buildCategoryTree(categories) {
      const map = {}

      // 先将所有分类放入map中
      categories.forEach(category => {
        map[category.id] = { ...category, children: [] }
      })

      // 构建完整的树形结构
      const result = []
      categories.forEach(category => {
        if (category.parentId === 0) {
          // 顶级分类
          result.push(map[category.id])
        } else {
          // 子分类
          if (map[category.parentId]) {
            map[category.parentId].children.push(map[category.id])
          }
        }
      })

      // 清理空的children数组
      const cleanEmptyChildren = (node) => {
        if (node.children && node.children.length === 0) {
          delete node.children
        } else if (node.children && node.children.length > 0) {
          node.children.forEach(child => cleanEmptyChildren(child))
        }
      }

      // 清理所有节点的空children
      result.forEach(node => cleanEmptyChildren(node))

      return result
    },

    /** 获取所有子分类ID */
    getAllChildCategoryIds(categoryId, categories) {
      const result = [categoryId]

      const findChildren = (parentId) => {
        categories.forEach(category => {
          if (category.parentId === parentId) {
            result.push(category.id)
            findChildren(category.id)
          }
        })
      }

      findChildren(categoryId)
      return result
    },

    /** 加载手动选题的题库列表 */
    loadManualSelectQuestionBanks() {
      let categoryIds = []

      // 如果选择了分类，获取该分类及其所有子分类的ID
      if (this.manualSelect.selectedCategory) {
        // 获取原始分类数据（包含所有层级）
        listCategory({ pageSize: 1000 }).then(response => {
          const allCategories = response.rows || []
          categoryIds = this.getAllChildCategoryIds(this.manualSelect.selectedCategory, allCategories)

          // 执行多次查询，因为后端不支持IN查询
          this.loadQuestionBanksByCategories(categoryIds)
        }).catch(error => {
          console.error('加载分类数据失败:', error)
          this.loadQuestionBanksByCategories([])
        })
      } else {
        // 没有选择分类，加载所有题库
        this.loadQuestionBanksByCategories([])
      }
    },

    /** 根据分类ID列表加载题库 */
    loadQuestionBanksByCategories(categoryIds) {
      const queryParams = {
        pageNum: this.manualSelect.bankPagination.pageNum,
        pageSize: this.manualSelect.bankPagination.pageSize,
        bankName: this.manualSelect.bankSearchKeyword || undefined
      }

      if (categoryIds.length > 0) {
        // 如果有分类筛选，需要合并多个分类的结果
        const promises = categoryIds.map(categoryId => {
          return listQuestionBank({ ...queryParams, categoryId })
        })

        Promise.all(promises).then(responses => {
          const allBanks = []
          let totalCount = 0

          responses.forEach(response => {
            if (response.rows) {
              allBanks.push(...response.rows)
              totalCount += response.total || 0
            }
          })

          // 去重（根据bankId）
          const uniqueBanks = allBanks.filter((bank, index, self) =>
            index === self.findIndex(b => b.bankId === bank.bankId)
          )

          this.manualSelect.questionBanks = uniqueBanks
          this.manualSelect.bankPagination.total = uniqueBanks.length
        }).catch(error => {
          console.error('加载题库列表失败:', error)
          this.$message.error('加载题库列表失败')
          this.manualSelect.questionBanks = []
          this.manualSelect.bankPagination.total = 0
        })
      } else {
        // 没有分类筛选，直接查询
        listQuestionBank(queryParams).then(response => {
          this.manualSelect.questionBanks = response.rows || []
          this.manualSelect.bankPagination.total = response.total || 0
        }).catch(error => {
          console.error('加载题库列表失败:', error)
          this.$message.error('加载题库列表失败')
          this.manualSelect.questionBanks = []
          this.manualSelect.bankPagination.total = 0
        })
      }
    },

    /** 搜索题库 */
    searchQuestionBanks() {
      this.manualSelect.bankPagination.pageNum = 1
      this.loadManualSelectQuestionBanks()
    },

    /** 选择题库 */
    selectQuestionBank(row) {
      this.manualSelect.selectedBankId = row.bankId
      this.loadQuestions()
    },

    /** 加载题目列表 */
    loadQuestions() {
      if (!this.manualSelect.selectedBankId) {
        this.manualSelect.questions = []
        this.manualSelect.questionPagination.total = 0
        return
      }

      const queryParams = {
        pageNum: this.manualSelect.questionPagination.pageNum,
        pageSize: this.manualSelect.questionPagination.pageSize,
        bankId: this.manualSelect.selectedBankId,
        questionType: this.manualSelect.questionType || undefined,
        difficulty: this.manualSelect.difficulty || undefined,
        questionContent: this.manualSelect.questionSearchKeyword || undefined
      }

      listQuestion(queryParams).then(response => {
        this.manualSelect.questions = response.rows || []
        this.manualSelect.questionPagination.total = response.total || 0
      }).catch(error => {
        console.error('加载题目列表失败:', error)
        this.$message.error('加载题目列表失败')
        this.manualSelect.questions = []
        this.manualSelect.questionPagination.total = 0
      })
    },

    /** 搜索题目 */
    searchQuestions() {
      this.manualSelect.questionPagination.pageNum = 1
      this.loadQuestions()
    },

    /** 重置题目搜索 */
    resetQuestionSearch() {
      this.manualSelect.questionType = ''
      this.manualSelect.difficulty = ''
      this.manualSelect.questionSearchKeyword = ''
      this.searchQuestions()
    },

    /** 随机抽题 */
    handleRandomSelect() {
      this.$message.info('随机抽题功能开发中...')
    },

    /** 排序 */
    handleSort() {
      this.$message.info('排序功能开发中...')
    },

    /** 批量设置分数 */
    handleBatchSetScore() {
      this.$message.info('批量设置分数功能开发中...')
    },

    /** 导出 */
    handleExport() {
      this.$message.info('导出功能开发中...')
    },

    /** 展开/收起 */
    handleToggleExpand() {
      this.isExpanded = !this.isExpanded
      // 批量设置所有题目的展开状态
      this.fixedQuestions.forEach(question => {
        question.expanded = this.isExpanded
      })
      this.$message.info(`已${this.isExpanded ? '展开' : '收起'}所有题目`)
    },

    /** 题目选择变化 */
    handleQuestionSelectionChange(selection) {
      this.manualSelect.selectedQuestions = selection
      this.updateSelectedStats()
    },

    /** 更新选中题目统计 */
    updateSelectedStats() {
      const stats = {}
      this.manualSelect.selectedQuestions.forEach(question => {
        const type = question.questionType
        stats[type] = (stats[type] || 0) + 1
      })
      this.manualSelect.selectedStats = stats
    },

    /** 确认手动选题 */
    confirmManualSelect() {
      if (this.manualSelect.selectedQuestions.length === 0) {
        this.$message.warning('请先选择题目')
        return
      }

      // 将选中的题目添加到固定试卷中
      this.manualSelect.selectedQuestions.forEach((question, index) => {
        this.fixedQuestions.push({
          id: Date.now() + index,
          questionId: question.questionId,
          content: question.questionContent,
          type: question.questionType,
          difficulty: question.difficulty,
          options: question.options, // 保存选项信息
          score: 1, // 默认1分
          selected: false,
          expanded: false // 默认收起状态
        })
      })

      this.showManualSelectDialog = false
      this.$message.success(`成功添加 ${this.manualSelect.selectedQuestions.length} 道题目`)
    },

    /** 题库分页大小变化 */
    handleBankSizeChange(val) {
      this.manualSelect.bankPagination.pageSize = val
      this.manualSelect.bankPagination.pageNum = 1
      this.loadManualSelectQuestionBanks()
    },

    /** 题库当前页变化 */
    handleBankCurrentChange(val) {
      this.manualSelect.bankPagination.pageNum = val
      this.loadManualSelectQuestionBanks()
    },

    /** 题目分页大小变化 */
    handleQuestionSizeChange(val) {
      this.manualSelect.questionPagination.pageSize = val
      this.manualSelect.questionPagination.pageNum = 1
      this.loadQuestions()
    },

    /** 题目当前页变化 */
    handleQuestionCurrentChange(val) {
      this.manualSelect.questionPagination.pageNum = val
      this.loadQuestions()
    },

    /** 获取难度文本 */
    getDifficultyText(difficulty) {
      const difficultyMap = { '1': '简单', '2': '中等', '3': '困难', 1: '简单', 2: '中等', 3: '困难' }
      return difficultyMap[difficulty] || '未知'
    },

    /** 更新题目分数 */
    updateQuestionScore(question) {
      // 分数更新后可以触发总分重新计算
      this.$forceUpdate()
    },

    /** 切换题目展开/收起状态 */
    toggleQuestionExpand(question) {
      question.expanded = !question.expanded
    },

    /** 删除单个题目 */
    deleteQuestion(question, index) {
      this.$confirm('确定删除这道题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fixedQuestions.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    /** 解析选项JSON字符串 */
    parseOptions(optionsStr) {
      try {
        if (typeof optionsStr === 'string') {
          return JSON.parse(optionsStr)
        }
        return optionsStr || []
      } catch (error) {
        console.error('解析选项失败:', error)
        return []
      }
    },

    /** 判断题目是否可选择 */
    isQuestionSelectable(row) {
      // 检查题目是否已经添加到试卷中
      return !this.fixedQuestions.some(question => question.questionId === row.questionId)
    },

    /** 获取题目行的样式类名 */
    getQuestionRowClassName({ row }) {
      // 如果题目已被添加，添加禁用样式
      if (this.fixedQuestions.some(question => question.questionId === row.questionId)) {
        return 'disabled-question-row'
      }
      return ''
    },
    
    /** 上传前验证 */
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    
    /** 加载试卷数据 */
    loadPaperData() {
      if (this.paperId) {
        // TODO: 调用API加载试卷数据
        console.log('加载试卷数据:', this.paperId)
      }
    }
  }
}
</script>

<style scoped>
.exam-editor {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.exam-editor-left {
  flex: 1;
  padding: 20px;
  padding-right: 420px; /* 为右侧固定面板留出空间 */
  overflow-y: auto;
  transition: padding-right 0.3s ease;
  background-color: #FFF;
}

.exam-editor-left.collapsed {
  padding-right: 70px; /* 右侧面板收起时的空间 */
}

.exam-editor-right {
  background: #fff;
  border-left: 1px solid #e4e7ed;
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  transition: width 0.3s ease;
  z-index: 100;
}

.collapse-button {
  position: absolute;
  left: -15px;
  top: 20px;
  width: 30px;
  height: 30px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  font-size: 16px;
  color: #606266;
}

.collapse-button:hover {
  background: #f5f7fa;
  color: #409eff;
}

.editPaper_main_top {
  background: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.editPaper_main_top .el-button-group {
  margin-right: 15px;
  display: inline-block;
}

.clear_both {
  clear: both;
}

.subject_main-wrapper {
  max-width: 100%;
}

/* 左侧卡片模块悬停效果 */
.subject_main-wrapper .el-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.subject_main-wrapper .el-card:hover {
  border: 2px dashed #409eff;
  background-color: #fafbff;
}

.subtitle-title {
  position: relative;
}

.slgfont {
  font-size: 24px;
  color: #303133;
}

.paper-count {
  margin-top: 10px;
}

.paper-count .el-tag {
  margin-left: 8px;
}



.mb10 {
  margin-bottom: 10px;
}

.mt10 {
  margin-top: 10px;
}

.tac {
  text-align: center;
}

.pd5 {
  padding: 5px;
}

.editor-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-header--big {
  font-size: 20px;
}

.close-button {
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-button:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.main {
  height: calc(100vh - 80px);
  overflow-y: auto;
  padding: 0;
}

.h100p {
  height: 100%;
}

.main-collapse {
  border: none;
}

.main-collapse .el-collapse-item__header {
  padding: 0 20px;
  height: 60px;
  line-height: 60px;
  background: #f8f8f8;
  border-bottom: 1px solid #e4e7ed;
  font-size: 16px;
  font-weight: bold;
}

/* 使用深度选择器覆盖Element UI默认样式 */
.main-collapse >>> .el-collapse-item__content {
  padding: 0 !important;
}

.editor-collapse-item >>> .el-collapse-item__content {
  padding: 0 !important;
}

/* Vue 3 深度选择器语法 */
.main-collapse :deep(.el-collapse-item__content) {
  padding: 0 !important;
}

.editor-collapse-item :deep(.el-collapse-item__content) {
  padding: 0 !important;
}

.main-module {
  padding: 20px;
}

.setting-block {
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.setting-block:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

/* 二级设置块容器 */
.block-expand {
  margin-top: 10px;
  padding-left: 20px;
  border-left: 2px solid #f0f0f0;
}

/* 二级设置块样式 */
.sub-setting {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e8e8e8;
}

.sub-setting:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.line {
  display: flex;
  align-items: flex-start;
}

.block-header {
  justify-content: space-between;
}

.block-header b {
  font-size: 14px;
  color: #303133;
  min-width: 93px;
  line-height: 32px;
}

.input-area {
  flex: 1;
  margin-left: 20px;
}

.input--number {
  width: 120px;
}

.dpib {
  display: inline-block;
}

.avatar-uploader {
  border: none !important;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 80px;
  box-sizing: border-box;
}



/* 使用伪元素创建统一的虚线边框 */
.avatar-uploader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  pointer-events: none;
  z-index: 1;
}

.avatar-uploader:hover::before {
  border-color: #409eff;
}

/* 确保Element UI组件没有边框 */
.avatar-uploader .el-upload {
  border: none !important;
  width: 100%;
  height: 100%;
  background: transparent !important;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 80px;
  display: block;
  object-fit: cover;
}

.image_area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bold {
  font-weight: bold;
}

.mr10 {
  margin-right: 10px;
}

/* 折叠面板标题样式 */
.collapse-title {
  margin-left: 15px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.collapse-title i {
  font-size: 18px;
  margin-right: 12px;
}

.el-popover__reference {
  margin-left: 5px;
  color: #909399;
  cursor: help;
}

.el-popover__title {
  font-weight: bold;
  margin-bottom: 8px;
}

/* 设置标题容器样式 */
.setting-title {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.setting-title b {
  font-size: 14px;
  color: #303133;
}

/* 出题方式问号图标样式 */
.paper-type-question {
  margin-left: 5px;
  color: #909399;
  cursor: help;
  position: relative;
  z-index: 10;
  font-size: 14px;
  line-height: 1;
}

.paper-type-question:hover {
  color: #409eff;
}

/* 出题方式提示框样式 */
.paper-type-tooltip {
  max-width: 320px !important;
  z-index: 2000 !important;
}

/* 迟到限制提示框样式 */
.late-limit-tooltip {
  max-width: 280px !important;
  z-index: 2000 !important;
}

/* 随机规则设置对话框样式 */
.cascade-random-rules {
  min-height: 400px;
}

/* 确保对话框层级正确 */
::v-deep .el-dialog__wrapper {
  z-index: 3000 !important;
}

::v-deep .el-dialog {
  z-index: 3001 !important;
  position: relative !important;
}

::v-deep .el-overlay {
  z-index: 2999 !important;
}

.topbar {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.topbar .fl {
  float: left;
}

.topbar .summary {
  margin-left: 15px;
  color: #666;
  font-size: 14px;
}

.topbar .total_score {
  color: #409eff;
  font-weight: bold;
}

.guide-steps-list {
  margin: 30px 0;
  padding: 0 20px;
}

/* 步骤组件样式优化 */
::v-deep .guide-steps-list .el-step {
  text-align: center;
}

::v-deep .guide-steps-list .el-step__head {
  text-align: center;
}

::v-deep .guide-steps-list .el-step__main {
  text-align: center;
  margin-top: 10px;
}

::v-deep .guide-steps-list .el-step__title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

::v-deep .guide-steps-list .el-step__description {
  margin-top: 8px;
  padding: 0 10px;
}

.step-content {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin-top: 5px;
}

.rules-content {
  min-height: 200px;
  margin: 20px 0;
}

.empty-rules {
  text-align: center;
  padding: 60px 0;
  color: #999;
  font-size: 14px;
}

.bottom-panel {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 强制覆盖Element UI折叠面板背景色 */
.el-collapse-item__header {
  background: #f8f8f8 !important;
  background-color: #f8f8f8 !important;
  font-size: 16px !important;
  font-weight: bold !important;
}

.el-collapse-item__header.is-active {
  background: #f8f8f8 !important;
  background-color: #f8f8f8 !important;
}

/* 使用属性选择器强制覆盖 */
[class*="el-collapse-item__header"] {
  background: #f8f8f8 !important;
  background-color: #f8f8f8 !important;
}

/* 深度选择器 */
.exam-editor >>> .el-collapse-item__header {
  background: #f8f8f8 !important;
  background-color: #f8f8f8 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .exam-editor-left {
    padding-right: 370px;
  }

  .exam-editor-left.collapsed {
    padding-right: 70px;
  }
}

@media (max-width: 768px) {
  .exam-editor {
    flex-direction: column;
  }

  .exam-editor-left {
    padding-right: 20px;
  }

  .exam-editor-left.collapsed {
    padding-right: 20px;
  }

  .exam-editor-right {
    position: relative !important;
    width: 100% !important;
    height: 50vh;
    border-left: none;
    border-top: 1px solid #e4e7ed;
  }

  .collapse-button {
    display: none;
  }
}
</style>
