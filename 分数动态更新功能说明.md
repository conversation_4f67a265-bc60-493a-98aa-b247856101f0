# 分数动态更新功能说明

## 功能概述

已成功修改固定试卷中的分数统计逻辑，使subtitle-title中的分数能够随着题目分数的改变而动态更新，同时保持随机试卷功能不受影响。

## 修改内容

### 1. totalRuleScore 计算属性修改

**修改前**：只计算随机试卷规则的总分
```javascript
totalRuleScore() {
  let total = 0
  this.rules.forEach(rule => {
    // 只计算规则分数
  })
  return total
}
```

**修改后**：根据试卷类型动态计算
```javascript
totalRuleScore() {
  // 如果是固定试卷，计算固定题目的总分
  if (this.paperForm.paperType === 0) {
    let total = 0
    this.fixedQuestions.forEach(question => {
      total += question.score || 0
    })
    return total
  }
  
  // 如果是随机试卷，计算规则的总分
  let total = 0
  this.rules.forEach(rule => {
    // 计算规则分数逻辑保持不变
  })
  return total
}
```

### 2. totalQuestions 计算属性修改

**修改前**：只计算随机试卷规则的题目数
```javascript
totalQuestions() {
  let total = 0
  this.rules.forEach(rule => {
    // 只计算规则题目数
  })
  return total
}
```

**修改后**：根据试卷类型动态计算
```javascript
totalQuestions() {
  // 如果是固定试卷，返回固定题目的数量
  if (this.paperForm.paperType === 0) {
    return this.fixedQuestions.length
  }
  
  // 如果是随机试卷，计算规则的题目数量
  let total = 0
  this.rules.forEach(rule => {
    // 计算规则题目数逻辑保持不变
  })
  return total
}
```

## 功能特性

### 1. 动态分数更新
- **实时响应**：当修改任何题目的分数时，subtitle-title中的总分立即更新
- **批量更新**：使用批量设置分数功能时，总分同步更新
- **随机抽题**：通过随机抽题添加题目时，总分自动计算

### 2. 动态题目数量更新
- **添加题目**：手动添加或随机抽题添加题目时，题目数量立即更新
- **删除题目**：删除题目时，题目数量同步减少
- **实时显示**：subtitle-title中始终显示准确的题目数量

### 3. 试卷类型兼容
- **固定试卷**：计算 `fixedQuestions` 数组中题目的分数和数量
- **随机试卷**：计算 `rules` 数组中规则的分数和数量
- **自动切换**：根据 `paperForm.paperType` 自动选择计算方式

## 界面显示效果

### subtitle-title 区域
```
[试卷名称]                    [总分数]分
创建时间：2024-01-01    [共X题] [固定试卷] [自动出分]
```

### 动态更新场景

1. **修改单个题目分数**
   - 操作：在题目列表中修改某题分数从2分改为3分
   - 效果：subtitle-title中的总分立即增加1分

2. **批量设置分数**
   - 操作：批量将10道题目设置为每题5分
   - 效果：subtitle-title中的总分立即更新为50分

3. **添加题目**
   - 操作：通过手动选题或随机抽题添加5道题目
   - 效果：subtitle-title中的题目数量立即增加5题，总分相应增加

4. **删除题目**
   - 操作：删除某道3分的题目
   - 效果：subtitle-title中的题目数量减1，总分减3分

## 技术实现

### 1. 响应式计算
- 使用Vue的计算属性（computed）实现自动响应
- 当 `fixedQuestions` 数组或其中题目的 `score` 属性变化时，自动重新计算
- 无需手动触发更新，Vue响应式系统自动处理

### 2. 条件判断
```javascript
if (this.paperForm.paperType === 0) {
  // 固定试卷逻辑
} else {
  // 随机试卷逻辑（保持原有逻辑不变）
}
```

### 3. 数据绑定
- subtitle-title中的显示直接绑定计算属性
- `{{ totalRuleScore }}` 和 `{{ totalQuestions }}` 自动更新
- 无需修改模板结构

## 兼容性保证

### 1. 随机试卷功能不受影响
- 随机试卷的计算逻辑完全保持原样
- 只在固定试卷模式下使用新的计算方式
- 通过 `paperForm.paperType` 进行条件判断

### 2. 现有功能保持不变
- 所有现有的分数设置功能正常工作
- 题目添加、删除、编辑功能不受影响
- 界面布局和样式保持不变

### 3. 向后兼容
- 对于没有设置分数的题目，使用默认值0
- 对于空的题目列表，正确返回0分和0题

## 测试验证

### 测试用例1：单个题目分数修改
1. 创建固定试卷，添加3道题目，每题2分
2. 验证subtitle-title显示"6分"和"共3题"
3. 修改第一题分数为5分
4. 验证subtitle-title立即更新为"9分"

### 测试用例2：批量设置分数
1. 固定试卷有5道题目，分数不一
2. 批量设置所有题目为每题3分
3. 验证subtitle-title立即更新为"15分"

### 测试用例3：添加删除题目
1. 固定试卷有2道题目，共6分
2. 添加1道4分的题目
3. 验证subtitle-title更新为"10分"和"共3题"
4. 删除4分题目
5. 验证subtitle-title恢复为"6分"和"共2题"

### 测试用例4：随机试卷兼容性
1. 切换到随机试卷模式
2. 添加规则，设置抽题数量和分数
3. 验证subtitle-title显示规则的总分和题目数
4. 确认随机试卷功能完全正常

## 注意事项

1. **数据类型**：确保题目分数为数字类型，避免字符串导致计算错误
2. **默认值处理**：使用 `|| 0` 处理未设置分数的情况
3. **性能考虑**：计算属性有缓存机制，只在依赖数据变化时重新计算
4. **边界情况**：正确处理空题目列表、零分题目等边界情况
