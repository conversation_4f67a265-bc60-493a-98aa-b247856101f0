# 固定试卷随机抽题功能测试

## 功能实现总结

已成功为固定试卷添加了随机抽题功能，主要特点：

### 1. 规则设置逻辑
- **一级规则**：只能选择题库（ruleType = 3）
- **子规则**：只能选择题型（ruleType = 1）
- **题库禁用**：已选择的题库在再次添加规则时会被禁用

### 2. 界面展示
- 复用随机试卷的规则设置对话框
- 支持父子规则的层级显示
- 实时显示抽题数量和总分统计

### 3. 抽题算法
- **有子规则**：从父规则的题库中按子规则的题型抽取题目
- **无子规则**：直接从父规则的题库中抽取所有题目
- **随机性**：使用 Fisher-Yates 洗牌算法确保随机性

## 测试用例

### 测试用例1：基本抽题功能
1. 进入固定试卷编辑页面
2. 点击"随机抽题"按钮
3. 添加一个题库规则，选择题库A
4. 设置抽题数量为5题，每题2分
5. 点击"确认抽题"
6. 验证：题目成功添加到固定试卷中，显示格式正确

### 测试用例2：题库禁用功能
1. 添加第一个规则，选择题库A
2. 再次点击"添加抽题规则"
3. 验证：题库A在选择列表中被禁用（不可选择）
4. 选择题库B，完成规则添加
5. 验证：可以成功添加第二个规则

### 测试用例3：子规则功能
1. 添加一个题库规则，选择题库A
2. 点击该规则的"添加子规则"按钮
3. 选择题型"单选题"，设置抽题数量和分数
4. 再次添加子规则，选择"多选题"
5. 点击"确认抽题"
6. 验证：只从题库A中抽取单选题和多选题

### 测试用例4：规则删除功能
1. 添加多个规则（包含子规则）
2. 删除父规则
3. 验证：父规则及其所有子规则都被删除
4. 删除单个子规则
5. 验证：只删除指定的子规则，父规则保留

## 代码关键点

### 新增数据结构
```javascript
// 固定试卷随机抽题对话框
showFixedRandomDialog: false,

// 固定试卷随机抽题相关数据
fixedRandomRules: [], // 临时规则列表
fixedRandomForm: {
  ruleType: 3, // 1:题型 3:题库
  generateType: 'divided',
  selectedItems: [],
  selectedQuestionTypes: []
}
```

### 核心方法
1. `handleRandomSelect()` - 打开随机抽题对话框
2. `handleAddFixedRandomRule()` - 添加题库规则
3. `handleAddFixedRandomSubRule()` - 添加题型子规则
4. `extractQuestionsFromRules()` - 根据规则抽取题目
5. `isRowSelectable()` - 题库禁用逻辑

### 计算属性
1. `fixedRandomParentRules` - 获取父规则列表
2. `usedFixedRandomBankIds` - 获取已使用的题库ID
3. `fixedRandomTotalQuestions` - 计算总题数
4. `fixedRandomTotalScore` - 计算总分数

## 与随机试卷的区别

| 特性 | 随机试卷 | 固定试卷随机抽题 |
|------|----------|------------------|
| 规则存储 | 保存到数据库 | 临时使用，抽题后丢弃 |
| 题目展示 | 不展示具体题目 | 抽题后立即展示题目 |
| 考试行为 | 每次考试动态抽题 | 所有考生使用相同题目 |
| 编辑能力 | 可编辑规则重新抽题 | 抽题后可编辑具体题目 |

## 注意事项

1. **题库选择限制**：一级规则只能选择题库，已选择的题库会被禁用
2. **子规则限制**：子规则只能选择题型，从父规则的题库中抽取
3. **抽题数量**：不能超过可用题目的数量
4. **随机性保证**：每次抽题结果都是随机的
5. **数据一致性**：删除父规则时会同时删除所有子规则

## 后续优化建议

1. **抽题预览**：在确认抽题前显示预览信息
2. **智能抽题**：根据难度分布、知识点覆盖等进行智能抽题
3. **模板保存**：保存常用的抽题规则配置
4. **批量操作**：支持批量设置分数、批量删除等操作
5. **抽题历史**：记录抽题历史，避免重复抽取
