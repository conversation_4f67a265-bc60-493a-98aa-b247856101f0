# 固定试卷随机抽题功能详细说明

## 功能概述

已成功为固定试卷添加了随机抽题功能，完全参考随机试卷的规则设置逻辑，支持选择规则类型和题型。

## 核心特性

### 1. 规则层级结构
- **一级规则（父规则）**：只能选择题库类型
- **子规则**：可以选择题型类型，从父规则的题库中按题型抽取题目

### 2. 规则类型选择
- **题库规则（ruleType = 3）**：从指定题库中抽取题目
- **题型规则（ruleType = 1）**：按题型从题库中抽取题目

### 3. 智能禁用机制
- **题库禁用**：已选择的题库在再次添加规则时被禁用
- **题型禁用**：在同一父规则下，已选择的题型在添加子规则时被禁用

## 详细功能说明

### 一级规则设置
1. 点击"添加抽题规则"按钮
2. 规则类型固定为"题库"（不可更改）
3. 选择一个或多个题库
4. 设置抽题数量和每题分数
5. 可以选择"分开设置"或"整体设置"

### 子规则设置
1. 在已有的一级规则上点击"添加子规则"按钮
2. 规则类型固定为"题型"（不可更改）
3. 选择题型（单选题、多选题、判断题）
4. 已选择的题型会被禁用，避免重复
5. 设置抽题数量和每题分数

### 抽题逻辑
1. **有子规则的情况**：
   - 忽略父规则的抽题设置
   - 按子规则的题型从父规则的题库中抽取题目
   - 每个子规则独立抽取指定数量的题目

2. **无子规则的情况**：
   - 直接从父规则的题库中抽取所有类型的题目
   - 按父规则设置的数量和分数抽取

## 界面展示

### 规则列表显示
```
题库：计算机基础题库、网络技术题库
├── 题型：单选题 (选取 5/20 题，每题 2 分，总分 10 分)
├── 题型：多选题 (选取 3/15 题，每题 3 分，总分 9 分)
└── 题型：判断题 (选取 2/10 题，每题 1 分，总分 2 分)
```

### 操作按钮
- **添加子规则**：为父规则添加题型子规则
- **删除**：删除规则（删除父规则会同时删除所有子规则）

## 技术实现要点

### 关键方法修改

1. **shouldShowRuleTypeSelection()**
   ```javascript
   // 固定试卷随机抽题也显示规则类型选择
   if (this.currentOperation === 'addFixedRandom') {
     return true
   }
   if (this.currentOperation === 'addFixedRandomSub') {
     return true
   }
   ```

2. **shouldShowRuleType(ruleType)**
   ```javascript
   // 固定试卷随机抽题一级规则只能选择题库
   if (this.currentOperation === 'addFixedRandom') {
     return ruleType === 3
   }
   // 固定试卷随机抽题子规则只能选择题型
   if (this.currentOperation === 'addFixedRandomSub' && this.parentRule) {
     if (this.parentRule.type === 3) {
       return ruleType === 1
     }
   }
   ```

3. **shouldShowQuestionType(questionType)**
   ```javascript
   // 支持固定试卷随机抽题的题型禁用
   if (this.currentOperation !== 'addSub' && this.currentOperation !== 'addFixedRandomSub') {
     return true
   }
   ```

4. **updateQuestionTypeCount()**
   ```javascript
   // 支持固定试卷随机抽题子规则的题目数量统计
   if ((this.currentOperation === 'addSub' || this.currentOperation === 'addFixedRandomSub') && this.parentRule && this.parentRule.type === 3) {
     selectedBanks = this.parentRule.selectedBanks || []
   }
   ```

### 抽题算法优化

```javascript
// 处理父规则和子规则的层级关系
const parentRules = rules.filter(rule => !rule.parentId)

for (const parentRule of parentRules) {
  const childRules = rules.filter(rule => rule.parentId === parentRule.id)
  
  if (childRules.length > 0) {
    // 有子规则：按子规则的题型从父规则的题库中抽取
    for (const childRule of childRules) {
      // 从父规则的题库中按子规则的题型抽取题目
    }
  } else {
    // 无子规则：直接从父规则的题库中抽取题目
  }
}
```

## 测试用例

### 测试用例1：基本一级规则
1. 添加题库规则，选择"计算机基础题库"
2. 设置抽取5题，每题2分
3. 确认抽题，验证从该题库随机抽取5道题目

### 测试用例2：题库禁用
1. 添加第一个规则，选择"题库A"
2. 再次添加规则，验证"题库A"被禁用
3. 选择"题库B"，成功添加第二个规则

### 测试用例3：子规则功能
1. 添加题库规则，选择"计算机基础题库"
2. 添加子规则，选择"单选题"，设置3题
3. 添加子规则，选择"多选题"，设置2题
4. 验证"单选题"在第二次添加时被禁用
5. 确认抽题，验证只抽取单选题和多选题

### 测试用例4：规则删除
1. 创建包含子规则的父规则
2. 删除父规则，验证所有子规则同时被删除
3. 删除单个子规则，验证只删除指定子规则

## 与随机试卷的一致性

| 功能特性 | 随机试卷 | 固定试卷随机抽题 | 一致性 |
|----------|----------|------------------|--------|
| 规则类型选择 | ✓ | ✓ | 完全一致 |
| 题库禁用 | ✓ | ✓ | 完全一致 |
| 题型禁用 | ✓ | ✓ | 完全一致 |
| 子规则支持 | ✓ | ✓ | 完全一致 |
| 界面布局 | ✓ | ✓ | 完全一致 |
| 操作流程 | ✓ | ✓ | 完全一致 |

## 注意事项

1. **规则限制**：一级规则只能选择题库，子规则只能选择题型
2. **禁用机制**：已选择的题库/题型会自动禁用，避免重复选择
3. **抽题优先级**：有子规则时忽略父规则的抽题设置
4. **数据一致性**：删除父规则会级联删除所有子规则
5. **题目数量**：抽题数量不能超过可用题目数量

## 后续优化方向

1. **规则模板**：保存常用的抽题规则配置
2. **智能推荐**：根据题库内容智能推荐抽题配置
3. **预览功能**：在确认抽题前显示预览信息
4. **批量操作**：支持批量设置分数、批量删除等
5. **统计分析**：显示题库覆盖率、难度分布等统计信息
