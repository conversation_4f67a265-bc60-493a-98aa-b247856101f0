# 固定试卷随机抽题功能说明

## 功能概述

在固定试卷模块中新增了随机抽题功能，允许用户通过设置随机规则来自动抽取题目并添加到固定试卷中。该功能复用了随机试卷的规则设置界面，但专门为固定试卷进行了优化。

## 功能特点

1. **复用随机试卷规则设置界面**：使用与随机试卷相同的规则设置对话框，保持界面一致性
2. **支持题库和题型规则**：可以按题库或按题型设置抽题规则
3. **灵活的抽题配置**：可以设置抽题数量和每题分数
4. **即时题目展示**：抽题完成后立即将题目添加到固定试卷中，像手动录入一样展示

## 使用流程

### 1. 进入固定试卷编辑页面
- 创建或编辑一个固定试卷（paperType = 0）

### 2. 点击随机抽题按钮
- 在固定试卷的操作栏中点击"随机抽题"按钮
- 系统会打开"随机抽题规则设置"对话框

### 3. 设置抽题规则
- **添加抽题规则**：点击"添加抽题规则"按钮
- **选择规则类型**：
  - 题库规则：从指定题库中抽取题目
  - 题型规则：按题型从所有题库中抽取题目
- **配置抽题参数**：
  - 选择题库或题型
  - 设置抽题数量
  - 设置每题分数

### 4. 确认抽题
- 点击"确认抽题"按钮
- 系统根据设置的规则随机抽取题目
- 抽取的题目自动添加到固定试卷中

## 技术实现

### 新增数据结构
```javascript
// 固定试卷随机抽题对话框
showFixedRandomDialog: false,

// 固定试卷随机抽题相关数据
fixedRandomRules: [], // 临时规则列表
fixedRandomForm: {
  ruleType: 3, // 1:题型 3:题库
  generateType: 'divided',
  selectedItems: [],
  selectedQuestionTypes: []
}
```

### 核心方法

1. **handleRandomSelect()**: 打开随机抽题对话框
2. **handleAddFixedRandomRule()**: 添加抽题规则
3. **handleConfirmFixedRandomSelect()**: 确认抽题并添加到试卷
4. **extractQuestionsFromRules()**: 根据规则抽取题目
5. **shuffleArray()**: 数组随机排序

### 抽题算法
1. 根据规则类型（题库/题型）构建查询条件
2. 调用题目查询API获取候选题目
3. 使用随机算法从候选题目中选择指定数量的题目
4. 为选中的题目设置分数
5. 将题目添加到固定试卷的题目列表中

## 界面展示

### 随机抽题规则设置对话框
- 顶部显示已选择题目数量和总分
- 步骤指引说明操作流程
- 规则列表展示已配置的抽题规则
- 每个规则可以设置抽题数量和分数
- 支持删除规则操作

### 抽题结果展示
- 抽取的题目直接添加到固定试卷的题目列表中
- 题目显示格式与手动添加的题目完全一致
- 包含题目内容、题型、难度、分数等信息
- 支持展开查看题目详情和选项

## 与随机试卷的区别

| 特性 | 随机试卷 | 固定试卷随机抽题 |
|------|----------|------------------|
| 规则保存 | 保存到数据库，用于考试时动态抽题 | 临时使用，立即抽题后丢弃 |
| 题目展示 | 不展示具体题目，只显示规则 | 抽题后立即展示具体题目 |
| 编辑能力 | 可以编辑规则，重新抽题 | 抽题后可以像普通题目一样编辑 |
| 考试行为 | 每次考试动态抽题 | 所有考生使用相同的题目 |

## 注意事项

1. **题目去重**：如果多个规则抽取到相同题目，系统会自动去重
2. **数量限制**：抽题数量不能超过题库中可用题目的数量
3. **分数设置**：每个规则可以独立设置每题分数
4. **随机性**：每次抽题结果都是随机的，即使使用相同规则

## 后续扩展

1. **保存抽题模板**：可以保存常用的抽题规则配置
2. **批量抽题**：支持一次性配置多个规则并批量抽题
3. **智能抽题**：根据难度分布、知识点覆盖等智能抽题
4. **抽题历史**：记录抽题历史，避免重复抽取相同题目
