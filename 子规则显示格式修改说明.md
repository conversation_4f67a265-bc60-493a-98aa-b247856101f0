# 子规则显示格式修改说明

## 修改内容

已修改固定试卷随机抽题中子规则的显示格式，使其与随机试卷保持一致。

## 修改前后对比

### 修改前
```
题库：计算机基础题库
├── 题型：单选题 选取 5/20 题，每题 2 分，总分 10 分
├── 题型：多选题 选取 3/15 题，每题 3 分，总分 9 分
└── 题型：判断题 选取 2/10 题，每题 1 分，总分 2 分
```

### 修改后
```
题库：计算机基础题库
├── 单选题：选取 5/20 题，每题 2 分，总分 10 分
├── 多选题：选取 3/15 题，每题 3 分，总分 9 分
└── 判断题：选取 2/10 题，每题 1 分，总分 2 分
```

## 技术实现

### 使用现有方法
复用了随机试卷中的 `getChildRuleLabel(childRule)` 方法：

```javascript
getChildRuleLabel(childRule) {
  if (childRule.type === 3) {
    return '题库：'
  } else if (childRule.type === 1) {
    // 题型子规则只显示具体的题型名称，不显示"题型："前缀
    if (childRule.selectedQuestionTypes && childRule.selectedQuestionTypes.length === 1) {
      const questionTypeMap = { '1': '单选题', '2': '多选题', '3': '判断题' }
      return questionTypeMap[childRule.selectedQuestionTypes[0]]
    } else {
      return '题型'
    }
  }
  return '规则：'
}
```

### 模板修改
将固定试卷随机抽题对话框中的子规则显示修改为：

```vue
<div class="rule-type-label">
  {{ getChildRuleLabel(childRule) }}：
</div>
<div class="rule-content">
  <span v-if="childRule.type === 3">
    {{ childRule.selectedBanks.map(bank => bank.bankName).join('、') }}
  </span>
  <span v-else-if="childRule.type === 1">
    选取
  </span>
</div>
```

## 显示逻辑

### 题库子规则
- 显示：`题库：题库名称`
- 示例：`题库：计算机基础题库`

### 题型子规则
- 单个题型：显示具体题型名称
  - `单选题：选取`
  - `多选题：选取`
  - `判断题：选取`
- 多个题型：显示`题型：选取`

## 一致性保证

现在固定试卷随机抽题和随机试卷的子规则显示格式完全一致：

| 规则类型 | 随机试卷显示 | 固定试卷随机抽题显示 | 一致性 |
|----------|--------------|---------------------|--------|
| 题库子规则 | 题库：题库名称 | 题库：题库名称 | ✓ |
| 单选题子规则 | 单选题：选取 | 单选题：选取 | ✓ |
| 多选题子规则 | 多选题：选取 | 多选题：选取 | ✓ |
| 判断题子规则 | 判断题：选取 | 判断题：选取 | ✓ |

## 用户体验改进

1. **更直观**：直接显示题型名称，用户一眼就能看出是什么类型的题目
2. **更简洁**：去掉了冗余的"题型："前缀
3. **更一致**：与随机试卷的显示格式完全一致，降低学习成本

## 测试验证

### 测试步骤
1. 进入固定试卷编辑页面
2. 点击"随机抽题"按钮
3. 添加题库规则
4. 添加单选题子规则
5. 添加多选题子规则
6. 验证显示格式为：
   ```
   题库：[题库名称]
   ├── 单选题：选取 X/Y 题
   └── 多选题：选取 X/Y 题
   ```

### 预期结果
- 子规则标签直接显示题型名称（如"单选题："）
- 不显示"题型：单选题"的冗余格式
- 与随机试卷的显示格式完全一致
