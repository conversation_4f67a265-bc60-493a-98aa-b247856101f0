# 批量设置分数功能说明

## 功能概述

已成功实现固定试卷中的批量设置分数功能，支持多种设置范围和灵活的分数配置。

## 功能特性

### 1. 设置范围选项
- **全部题目**：为试卷中的所有题目设置相同分数
- **选中题目**：仅为勾选的题目设置分数
- **按题型**：为指定题型的所有题目设置分数

### 2. 题型支持
- 单选题
- 多选题  
- 判断题

### 3. 分数设置
- 最小分数：0.5分
- 步长：0.5分
- 精度：1位小数
- 支持任意正数分值

## 使用流程

### 1. 打开批量设置对话框
- 点击固定试卷操作栏中的"批量设置分数"按钮
- 系统检查是否有题目，无题目时提示用户

### 2. 选择设置范围
- **全部题目**：直接设置分数即可
- **选中题目**：需要先在题目列表中勾选要设置的题目
- **按题型**：需要选择具体的题型（单选题/多选题/判断题）

### 3. 设置分数值
- 使用数字输入框设置分数
- 支持0.5的步长调整
- 实时显示影响的题目数量

### 4. 确认设置
- 点击"确定"按钮执行批量设置
- 系统显示设置成功的题目数量

## 界面设计

### 对话框结构
```
批量设置分数
├── 设置范围
│   ├── ○ 全部题目
│   ├── ○ 选中题目  
│   └── ○ 按题型
├── 选择题型（按题型时显示）
│   └── 下拉选择：单选题/多选题/判断题
├── 分数
│   └── 数字输入框（0.5步长）
├── 影响题目
│   └── 提示信息：X道题目将被设置为Y分
└── 操作按钮
    ├── 取消
    └── 确定
```

### 实时反馈
- **影响题目数量**：根据选择的范围实时计算并显示
- **验证提示**：选中题目为0时、未选择题型时的友好提示
- **成功反馈**：设置完成后显示具体影响的题目数量

## 技术实现

### 数据结构
```javascript
batchScoreForm: {
  scoreType: 'all',     // 设置范围：all/selected/byType
  score: 1,             // 分数值
  questionType: ''      // 题型（按题型时使用）
}
```

### 核心方法

1. **handleBatchSetScore()**
   - 打开批量设置对话框
   - 检查题目数量
   - 重置表单数据

2. **getBatchScoreAffectedCount()**
   - 计算影响的题目数量
   - 根据设置范围动态计算
   - 实时更新显示

3. **handleConfirmBatchSetScore()**
   - 验证设置条件
   - 筛选目标题目
   - 批量更新分数
   - 显示成功反馈

### 筛选逻辑
```javascript
switch (scoreType) {
  case 'all':
    targetQuestions = this.fixedQuestions
    break
  case 'selected':
    targetQuestions = this.fixedQuestions.filter(q => q.selected)
    break
  case 'byType':
    targetQuestions = this.fixedQuestions.filter(q => q.type == questionType)
    break
}
```

## 使用场景

### 场景1：统一分数设置
- 新建试卷后，为所有题目设置统一分数
- 选择"全部题目"，设置目标分数

### 场景2：差异化分数设置
- 根据题目难度设置不同分数
- 选择"按题型"，分别为不同题型设置分数

### 场景3：局部调整
- 调整部分题目的分数
- 先勾选目标题目，选择"选中题目"进行设置

## 验证机制

### 前置验证
- 检查是否有题目存在
- 检查选中题目数量（选中题目模式）
- 检查题型选择（按题型模式）

### 数据验证
- 分数值必须大于等于0.5
- 题型必须在支持范围内
- 影响题目数量必须大于0

### 用户反馈
- 友好的错误提示信息
- 成功操作的确认反馈
- 实时的影响范围显示

## 扩展性

### 未来可扩展功能
1. **分数模板**：保存常用的分数设置方案
2. **按难度设置**：根据题目难度自动设置分数
3. **批量调整**：支持按比例调整现有分数
4. **分数验证**：检查总分是否符合要求
5. **撤销功能**：支持撤销批量设置操作

### 性能优化
- 大量题目时的分页处理
- 批量操作的进度显示
- 操作历史记录

## 注意事项

1. **操作不可逆**：批量设置会覆盖原有分数，建议谨慎操作
2. **选择验证**：使用"选中题目"模式时需要先勾选题目
3. **题型匹配**：按题型设置时确保题型选择正确
4. **分数合理性**：建议设置合理的分数值，避免过高或过低
